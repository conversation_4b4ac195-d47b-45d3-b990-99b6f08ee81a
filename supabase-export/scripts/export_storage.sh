#!/bin/bash

# Script to export storage bucket configurations from Supabase
# This script exports bucket definitions and policies

# Set base directory
BASE_DIR="../storage"
BUCKETS_DIR="$BASE_DIR/buckets"
POLICIES_DIR="$BASE_DIR/policies"

# Check if required directories exist
for dir in "$BUCKETS_DIR" "$POLICIES_DIR"; do
  if [ ! -d "$dir" ]; then
    echo "Error: Directory $dir does not exist."
    echo "Please run the create_export_structure.sh script first."
    exit 1
  fi
done

# Create temp directory
TEMP_DIR=$(mktemp -d)
echo "Created temporary directory: $TEMP_DIR"

# Export storage buckets
echo "Exporting storage buckets..."
cat > "$TEMP_DIR/export_buckets.sql" << EOF
SELECT 
  format(
    'INSERT INTO storage.buckets (id, name, owner, created_at, updated_at, public, avif_autodetection, file_size_limit, allowed_mime_types) VALUES (%L, %L, %L, %L, %L, %L, %L, %L, %L);',
    id, 
    name, 
    owner, 
    created_at, 
    updated_at, 
    public, 
    avif_autodetection,
    file_size_limit,
    allowed_mime_types
  ) as bucket_statement
FROM 
  storage.buckets;
EOF

# Export storage policies
echo "Exporting storage policies..."
cat > "$TEMP_DIR/export_policies.sql" << EOF
SELECT 
  format(
    'CREATE POLICY %s ON storage.objects FOR %s TO %s USING (%s);',
    p.polname,
    CASE 
      WHEN p.polcmd = 'r' THEN 'SELECT'
      WHEN p.polcmd = 'a' THEN 'INSERT'
      WHEN p.polcmd = 'w' THEN 'UPDATE'
      WHEN p.polcmd = 'd' THEN 'DELETE'
      WHEN p.polcmd = '*' THEN 'ALL'
    END,
    CASE WHEN p.polroles = '{0}' THEN 'PUBLIC' ELSE 
      array_to_string(ARRAY(SELECT rolname FROM pg_roles WHERE oid = ANY(p.polroles)), ',')
    END,
    COALESCE(pg_get_expr(p.polqual, p.polrelid), 'true')
  ) as policy_statement
FROM 
  pg_policy p
JOIN 
  pg_class c ON c.oid = p.polrelid
JOIN 
  pg_namespace n ON n.oid = c.relnamespace
WHERE 
  n.nspname = 'storage' AND c.relname = 'objects';
EOF

# Export bucket creation statements
echo "Exporting buckets to file..."
psql -d postgres -t -f "$TEMP_DIR/export_buckets.sql" > "$BUCKETS_DIR/buckets.sql"

# Export bucket policies
echo "Exporting bucket policies to file..."
psql -d postgres -t -f "$TEMP_DIR/export_policies.sql" > "$POLICIES_DIR/policies.sql"

# Add enable RLS statement to the policies file
echo "ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;" >> "$POLICIES_DIR/policies.sql"

# Clean up temp files
echo "Cleaning up temporary files..."
rm -rf "$TEMP_DIR"

echo "Storage configuration export completed successfully!" 