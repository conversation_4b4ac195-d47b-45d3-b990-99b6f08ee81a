#!/bin/bash

# Script to export Supabase Edge Functions using the MCP tools
# This script exports all edge functions and their code

# Set base directory and project ID
BASE_DIR="../edge-functions"
PROJECT_ID="bvgyhorawikkarjcucxv"

# Check if required directory exists
if [ ! -d "$BASE_DIR" ]; then
  echo "Error: Directory $BASE_DIR does not exist."
  echo "Please run the create_export_structure.sh script first."
  exit 1
fi

# Create a Python script to export the edge functions
cat > export_edge_functions.py << EOL
import json
import os
import sys
import requests

def main():
    # Set up the API URL and project ID
    project_id = "$PROJECT_ID"
    base_dir = "$BASE_DIR"
    
    # Get the list of edge functions using MCP
    response = requests.post(
        "http://localhost:3000/api/mcp/supabase/list_edge_functions",
        json={"project_id": project_id}
    )
    
    if response.status_code != 200:
        print(f"Error fetching edge functions: {response.text}")
        return 1
    
    functions = response.json()
    
    # Save the manifest
    with open(os.path.join(base_dir, "functions_manifest.json"), "w") as f:
        json.dump(functions, f, indent=2)
    
    print(f"Found {len(functions)} edge functions.")
    
    # Process each function
    for func in functions:
        name = func.get("name")
        slug = func.get("slug")
        
        print(f"Exporting edge function: {name} ({slug})")
        
        # Create directory for function
        function_dir = os.path.join(base_dir, slug)
        os.makedirs(function_dir, exist_ok=True)
        
        # Save function metadata
        with open(os.path.join(function_dir, "metadata.json"), "w") as f:
            json.dump(func, f, indent=2)
        
        # Extract and save function files
        if "files" in func:
            for file_info in func["files"]:
                file_name = os.path.basename(file_info["name"])
                file_path = os.path.join(function_dir, file_name)
                
                print(f"  Saving file: {file_path}")
                with open(file_path, "w") as f:
                    f.write(file_info["content"])
        else:
            print(f"  No files found for function: {name}")
    
    print("Edge functions export completed successfully!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
EOL

# Execute the Python script
echo "Exporting edge functions..."
python3 export_edge_functions.py

# Clean up the Python script
rm export_edge_functions.py 