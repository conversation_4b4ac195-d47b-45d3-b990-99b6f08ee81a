#!/bin/bash

# Master script to run all export scripts

# Set script path
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)"
cd "$SCRIPT_DIR"

echo "=================================================="
echo "Starting Supabase Project Export Process"
echo "=================================================="
echo ""

# Make all scripts executable
chmod +x *.sh

# Export database schema
echo "Step 1/3: Exporting database schema..."
./export_database.sh
if [ $? -ne 0 ]; then
  echo "Error: Database schema export failed."
  exit 1
fi
echo ""

# Export storage buckets
echo "Step 2/3: Exporting storage buckets and policies..."
./export_storage.sh
if [ $? -ne 0 ]; then
  echo "Error: Storage export failed."
  exit 1
fi
echo ""

# Export edge functions
echo "Step 3/3: Exporting edge functions..."
./export_edge_functions_mcp.sh
if [ $? -ne 0 ]; then
  echo "Warning: Edge functions export failed with MCP method."
  echo "Trying alternative export method..."
  ./export_edge_functions.sh
  if [ $? -ne 0 ]; then
    echo "Error: Edge functions export failed with both methods."
    exit 1
  fi
fi
echo ""

echo "=================================================="
echo "Supabase Project Export Completed Successfully!"
echo "=================================================="
echo ""
echo "Export package is ready in the supabase-export directory."
echo "To use this package to create a new Supabase project:"
echo ""
echo "1. Create a new Supabase project"
echo "2. Run the import scripts in the following order:"
echo "   - Import database schema"
echo "   - Import storage buckets and policies"
echo "   - Import edge functions"
echo ""
echo "See the README.md file for detailed instructions."
echo "==================================================" 