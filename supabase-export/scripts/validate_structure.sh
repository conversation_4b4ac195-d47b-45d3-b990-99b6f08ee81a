#!/bin/bash

# Validation script for the Supabase export package structure
# Usage: ./validate_structure.sh

# Set base directory relative to this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"

echo "Validating directory structure in: $BASE_DIR"

# Define expected directories
EXPECTED_DIRS=(
    "."
    "./database"
    "./database/tables"
    "./database/functions"
    "./database/triggers"
    "./database/policies"
    "./storage"
    "./storage/buckets"
    "./storage/policies"
    "./edge-functions"
    "./scripts"
    "./docs"
)

# Check directories
echo -e "\n=== Checking Directories ==="
MISSING_DIRS=0

for dir in "${EXPECTED_DIRS[@]}"; do
    full_path="$BASE_DIR/$dir"
    # Convert relative path for display
    display_path="${dir:2}"
    if [ "$dir" = "." ]; then
        display_path="(root)"
    fi
    
    if [ -d "$full_path" ]; then
        echo "✅ Directory exists: $display_path"
    else
        echo "❌ Missing directory: $display_path"
        MISSING_DIRS=$((MISSING_DIRS + 1))
    fi
done

# Check README files
echo -e "\n=== Checking README Files ==="
MISSING_READMES=0

for dir in "${EXPECTED_DIRS[@]}"; do
    full_path="$BASE_DIR/$dir"
    readme_path="$full_path/README.md"
    
    # Convert relative path for display
    display_path="${dir:2}/README.md"
    if [ "$dir" = "." ]; then
        display_path="README.md"
    fi
    
    if [ -f "$readme_path" ]; then
        echo "✅ README exists: $display_path"
    else
        echo "❌ Missing README: $display_path"
        MISSING_READMES=$((MISSING_READMES + 1))
    fi
done

# Summary
echo -e "\n=== Validation Summary ==="
if [ $MISSING_DIRS -eq 0 ] && [ $MISSING_READMES -eq 0 ]; then
    echo "✅ All directories and README files exist."
    echo "The Supabase export package structure is valid."
    exit 0
else
    echo "❌ Found issues with the directory structure:"
    [ $MISSING_DIRS -gt 0 ] && echo "   - Missing directories: $MISSING_DIRS"
    [ $MISSING_READMES -gt 0 ] && echo "   - Missing README files: $MISSING_READMES"
    echo "Please fix these issues before proceeding."
    exit 1
fi 