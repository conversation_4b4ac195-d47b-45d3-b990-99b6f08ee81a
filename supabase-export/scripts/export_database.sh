#!/bin/bash

# <PERSON>ript to export database schema from Supabase
# This script exports tables, functions, triggers and policies

# Set base directory
BASE_DIR="../database"
OUTPUT_DIR="$BASE_DIR"
TABLES_DIR="$OUTPUT_DIR/tables"
FUNCTIONS_DIR="$OUTPUT_DIR/functions"
TRIGGERS_DIR="$OUTPUT_DIR/triggers"
POLICIES_DIR="$OUTPUT_DIR/policies"

# Check if required directories exist
for dir in "$TABLES_DIR" "$FUNCTIONS_DIR" "$TRIGGERS_DIR" "$POLICIES_DIR"; do
  if [ ! -d "$dir" ]; then
    echo "Error: Directory $dir does not exist."
    echo "Please run the create_export_structure.sh script first."
    exit 1
  fi
done

# Create a temp directory
TEMP_DIR=$(mktemp -d)
echo "Created temporary directory: $TEMP_DIR"

# Export tables schema
echo "Exporting table schemas..."
cat > "$TEMP_DIR/export_tables.sql" << EOF
SELECT 
  'CREATE TABLE IF NOT EXISTS ' || 
  quote_ident(table_schema) || '.' || quote_ident(table_name) || 
  E' (\n' ||
  string_agg(
    '  ' || quote_ident(column_name) || ' ' || 
    data_type || 
    CASE 
      WHEN character_maximum_length IS NOT NULL THEN '(' || character_maximum_length || ')'
      WHEN data_type = 'numeric' AND numeric_precision IS NOT NULL AND numeric_scale IS NOT NULL THEN '(' || numeric_precision || ',' || numeric_scale || ')'
      ELSE ''
    END ||
    CASE WHEN is_nullable = 'NO' THEN ' NOT NULL' ELSE '' END ||
    CASE WHEN column_default IS NOT NULL THEN ' DEFAULT ' || column_default ELSE '' END,
    E',\n'
  ) ||
  CASE 
    WHEN pc.conname IS NOT NULL THEN 
      E',\n  CONSTRAINT ' || quote_ident(pc.conname) || ' PRIMARY KEY (' || 
      string_agg(quote_ident(pkc.attname), ', ' ORDER BY pkc.attnum) || 
      ')'
    ELSE ''
  END ||
  E'\n);' as create_table_statement
FROM 
  information_schema.columns c
LEFT JOIN 
  pg_constraint pc ON pc.conrelid = (quote_ident(c.table_schema) || '.' || quote_ident(c.table_name))::regclass AND pc.contype = 'p'
LEFT JOIN 
  pg_class cl ON cl.oid = pc.conrelid
LEFT JOIN 
  pg_attribute pkc ON pkc.attrelid = cl.oid AND pkc.attnum = ANY(pc.conkey)
WHERE 
  c.table_schema = 'public'
  AND c.table_name NOT IN ('pg_stat_statements', 'pg_stat_statements_info')
GROUP BY 
  c.table_schema, c.table_name, pc.conname
ORDER BY 
  c.table_schema, c.table_name;
EOF

# Export foreign keys
echo "Exporting foreign keys..."
cat > "$TEMP_DIR/export_fk.sql" << EOF
SELECT 
  'ALTER TABLE ' || quote_ident(kcu.table_schema) || '.' || quote_ident(kcu.table_name) || 
  ' ADD CONSTRAINT ' || quote_ident(tc.constraint_name) || 
  ' FOREIGN KEY (' || quote_ident(kcu.column_name) || ')' ||
  ' REFERENCES ' || quote_ident(ccu.table_schema) || '.' || quote_ident(ccu.table_name) || 
  ' (' || quote_ident(ccu.column_name) || ')' ||
  CASE 
    WHEN rc.update_rule <> 'NO ACTION' THEN ' ON UPDATE ' || rc.update_rule
    ELSE ''
  END ||
  CASE 
    WHEN rc.delete_rule <> 'NO ACTION' THEN ' ON DELETE ' || rc.delete_rule
    ELSE ''
  END ||
  ';' as foreign_key_statement
FROM 
  information_schema.table_constraints tc
JOIN 
  information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
  AND tc.table_schema = kcu.table_schema
JOIN 
  information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
  AND ccu.table_schema = tc.table_schema
JOIN 
  information_schema.referential_constraints rc ON tc.constraint_name = rc.constraint_name
  AND tc.table_schema = rc.constraint_schema
WHERE 
  tc.constraint_type = 'FOREIGN KEY'
  AND kcu.table_schema = 'public';
EOF

# Export functions
echo "Exporting functions..."
cat > "$TEMP_DIR/export_functions.sql" << EOF
SELECT 
  'CREATE OR REPLACE FUNCTION ' || 
  n.nspname || '.' || p.proname || '(' || 
  pg_get_function_arguments(p.oid) || ') ' ||
  'RETURNS ' || pg_get_function_result(p.oid) || ' AS $BODY$' ||
  p.prosrc || '$BODY$ LANGUAGE ' || 
  l.lanname || ';' as function_statement
FROM 
  pg_proc p
JOIN 
  pg_namespace n ON p.pronamespace = n.oid
JOIN 
  pg_language l ON p.prolang = l.oid
WHERE 
  n.nspname = 'public' AND
  p.prokind = 'f';
EOF

# Export triggers
echo "Exporting triggers..."
cat > "$TEMP_DIR/export_triggers.sql" << EOF
SELECT 
  format(
    'CREATE TRIGGER %s %s %s ON %s.%s FOR EACH %s %s; ',
    trg.tgname, 
    CASE 
      WHEN trg.tgtype & 2 = 2 THEN 'BEFORE' 
      WHEN trg.tgtype & 64 = 64 THEN 'INSTEAD OF' 
      ELSE 'AFTER' 
    END,
    CASE 
      WHEN trg.tgtype & 4 = 4 THEN 'INSERT' 
      WHEN trg.tgtype & 8 = 8 THEN 'DELETE' 
      WHEN trg.tgtype & 16 = 16 THEN 'UPDATE' 
      WHEN trg.tgtype & 28 = 28 THEN 'INSERT OR DELETE OR UPDATE' 
      WHEN trg.tgtype & 20 = 20 THEN 'INSERT OR UPDATE' 
      WHEN trg.tgtype & 24 = 24 THEN 'DELETE OR UPDATE' 
      WHEN trg.tgtype & 12 = 12 THEN 'INSERT OR DELETE' 
      ELSE '' 
    END,
    ns.nspname,
    tbl.relname,
    CASE 
      WHEN trg.tgtype & 1 = 1 THEN 'ROW' 
      ELSE 'STATEMENT' 
    END,
    CASE 
      WHEN trg.tgtype & 2 = 2 THEN 'EXECUTE FUNCTION ' || ns_func.nspname || '.' || func.proname || '();'
      ELSE ''
    END
  ) as trigger_statement
FROM 
  pg_trigger trg
JOIN 
  pg_class tbl ON trg.tgrelid = tbl.oid
JOIN 
  pg_namespace ns ON tbl.relnamespace = ns.oid
LEFT JOIN 
  pg_proc func ON trg.tgfoid = func.oid
LEFT JOIN 
  pg_namespace ns_func ON func.pronamespace = ns_func.oid
WHERE 
  ns.nspname = 'public' AND NOT trg.tgisinternal;
EOF

# Export RLS policies
echo "Exporting RLS policies..."
cat > "$TEMP_DIR/export_policies.sql" << EOF
SELECT 
  format(
    'CREATE POLICY %s ON %s.%s TO %s USING (%s) WITH CHECK (%s);',
    pol.polname,
    n.nspname,
    c.relname,
    CASE WHEN pol.polroles = '{0}' THEN 'PUBLIC' ELSE 
      array_to_string(ARRAY(SELECT rolname FROM pg_roles WHERE oid = ANY(pol.polroles)), ',')
    END,
    COALESCE(pg_get_expr(pol.polqual, pol.polrelid), 'true'),
    COALESCE(pg_get_expr(pol.polwithcheck, pol.polrelid), 'true')
  ) as policy_statement,
  format(
    'ALTER TABLE %s.%s ENABLE ROW LEVEL SECURITY;',
    n.nspname,
    c.relname
  ) as enable_rls_statement,
  CASE 
    WHEN c.relrowsecurity = true AND c.relforcerowsecurity = true THEN
      format('ALTER TABLE %s.%s FORCE ROW LEVEL SECURITY;', n.nspname, c.relname)
    ELSE ''
  END as force_rls_statement
FROM 
  pg_policy pol
JOIN 
  pg_class c ON c.oid = pol.polrelid
JOIN 
  pg_namespace n ON n.oid = c.relnamespace
WHERE 
  n.nspname = 'public';
EOF

# Now, export the schema to individual files
for table in $(psql -d postgres -t -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'");
do
  table_name=$(echo $table | xargs)
  echo "Exporting schema for table: $table_name"
  
  # Create file for table definition
  psql -d postgres -t -f "$TEMP_DIR/export_tables.sql" | grep -i "CREATE TABLE.*$table_name" > "$TABLES_DIR/${table_name}.sql"
  
  # Export foreign keys for this table
  psql -d postgres -t -f "$TEMP_DIR/export_fk.sql" | grep -i "FOREIGN KEY.*$table_name" >> "$TABLES_DIR/${table_name}.sql"
done

# Export all functions
echo "Exporting all functions to individual files..."
psql -d postgres -t -f "$TEMP_DIR/export_functions.sql" | while read -r func_def; do
  if [ -n "$func_def" ]; then
    # Extract function name from definition
    func_name=$(echo "$func_def" | sed -n 's/CREATE OR REPLACE FUNCTION public\.\([a-zA-Z0-9_]*\).*/\1/p')
    if [ -n "$func_name" ]; then
      echo "$func_def" > "$FUNCTIONS_DIR/${func_name}.sql"
      echo "Exported function: $func_name"
    fi
  fi
done

# Export all triggers
echo "Exporting all triggers to individual files..."
psql -d postgres -t -f "$TEMP_DIR/export_triggers.sql" | while read -r trigger_def; do
  if [ -n "$trigger_def" ]; then
    # Extract trigger name from definition
    trigger_name=$(echo "$trigger_def" | sed -n 's/CREATE TRIGGER \([a-zA-Z0-9_]*\).*/\1/p')
    if [ -n "$trigger_name" ]; then
      echo "$trigger_def" > "$TRIGGERS_DIR/${trigger_name}.sql"
      echo "Exported trigger: $trigger_name"
    fi
  fi
done

# Export all policies
echo "Exporting all policies to individual files..."
psql -d postgres -t -f "$TEMP_DIR/export_policies.sql" | while read -r policy_def; do
  if [ -n "$policy_def" ]; then
    # Extract policy name from definition
    policy_name=$(echo "$policy_def" | sed -n 's/CREATE POLICY \([a-zA-Z0-9_]*\).*/\1/p')
    if [ -n "$policy_name" ]; then
      echo "$policy_def" > "$POLICIES_DIR/${policy_name}.sql"
      echo "Exported policy: $policy_name"
    fi
  fi
done

# Clean up temp files
echo "Cleaning up temporary files..."
rm -rf "$TEMP_DIR"

echo "Database schema export completed successfully!" 