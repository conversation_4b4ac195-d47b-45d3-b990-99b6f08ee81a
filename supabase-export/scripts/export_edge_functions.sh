#!/bin/bash

# Script to export Supabase Edge Functions
# This script exports all edge functions and their code

# Set base directory
BASE_DIR="../edge-functions"

# Check if required directory exists
if [ ! -d "$BASE_DIR" ]; then
  echo "Error: Directory $BASE_DIR does not exist."
  echo "Please run the create_export_structure.sh script first."
  exit 1
fi

# Create temporary directory for edge functions list
TEMP_DIR=$(mktemp -d)
echo "Created temporary directory: $TEMP_DIR"

# Get list of edge functions
echo "Retrieving list of edge functions..."
supabase functions list --project-ref bvgyhorawikkarjcucxv -o json > "$TEMP_DIR/functions.json"

if [ ! -s "$TEMP_DIR/functions.json" ]; then
  echo "Error: Failed to retrieve edge functions list or no functions found."
  rm -rf "$TEMP_DIR"
  exit 1
fi

# Process each function
cat "$TEMP_DIR/functions.json" | jq -c '.[]' | while read -r func; do
  name=$(echo $func | jq -r '.name')
  slug=$(echo $func | jq -r '.slug')
  
  echo "Exporting edge function: $name ($slug)"
  
  # Create directory for function
  mkdir -p "$BASE_DIR/$slug"
  
  # Get function code
  supabase functions download "$slug" --project-ref bvgyhorawikkarjcucxv -o "$BASE_DIR/$slug"
  
  if [ $? -ne 0 ]; then
    echo "Warning: Failed to download function code for $name"
  else
    echo "Successfully exported function: $name"
  fi
  
  # Create metadata file
  echo $func > "$BASE_DIR/$slug/metadata.json"
done

# Create a manifest file with all functions
echo "Creating edge functions manifest..."
cat "$TEMP_DIR/functions.json" > "$BASE_DIR/functions_manifest.json"

# Clean up temp directory
echo "Cleaning up temporary files..."
rm -rf "$TEMP_DIR"

echo "Edge functions export completed successfully!" 