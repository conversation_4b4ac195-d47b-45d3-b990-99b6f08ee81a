<context>
# Overview
StageCoachAI Multi-Tenancy Implementation

StageCoachAI is currently a platform designed for pageant contestants to practice their interviewing skills. The goal of this project is to expand the platform to support multiple verticals (markets) while maintaining a single codebase. This will allow the same core platform to be used by different user groups with domain-specific features and content.

For example, in addition to pageant contestants, the platform will support salespeople who want to practice their sales scripts and objection handling. Each vertical will be identified by its own domain name, and the platform will adapt its behavior, pricing, grading categories, and content based on the domain used to access it.

# Core Features
1. **Domain-Based Tenant Identification**
   - The system will identify which vertical/tenant a user belongs to based on the domain used to access the application
   - Each domain will map to a specific tenant configuration in the database
   - The application will adapt its behavior and features based on the identified tenant

2. **Tenant-Specific Configuration**
   - Each tenant will have its own configuration including:
     - Home page content and branding
     - Per-minute pricing
     - Grading categories specific to the vertical
     - Custom terminology and UI elements

3. **Data Segmentation**
   - User data will be segmented by tenant
   - Recordings, feedback, and other user-generated content will be associated with specific tenants
   - Analytics and reporting will be available per tenant

4. **Shared Core Functionality**
   - The core recording, playback, and AI analysis features will remain the same across all tenants
   - The underlying infrastructure will be shared to minimize maintenance and development overhead

# User Experience
## User Personas
1. **Pageant Contestant (Current)**
   - Practice interview questions specific to pageants
   - Receive feedback on communication skills, confidence, and content
   - Access pageant-specific guidelines and best practices

2. **Sales Professional (New Vertical)**
   - Practice sales pitches and objection handling
   - Receive feedback on persuasion techniques, product knowledge, and closing skills
   - Access sales-specific templates and frameworks

## Key User Flows
- Users access the application through a domain specific to their vertical
- The application loads with branding, content, and features tailored to that vertical
- Users can create recordings, receive feedback, and improve their skills within the context of their specific domain
- Admin users can manage multiple tenants and their configurations
</context>
<PRD>
# Technical Architecture
## System Components
1. **Tenant Management System**
   - New `tenants` table in the database to store tenant configurations
   - Domain resolution middleware to identify the current tenant based on the request domain
   - Tenant context provider to make tenant information available throughout the application

2. **Database Schema Modifications**
   - Add `tenant_id` column to relevant tables:
     - `users`
     - `recordings`
     - `grading_categories`
     - `feedback`
     - Other user-related data tables
   - Create new `tenants` table with fields:
     - `id` (Primary key)
     - `domain` (e.g., pageant.stagecoach.ai, sales.stagecoach.ai)
     - `name` (Display name for the tenant)
     - `description`
     - `logo_url`
     - `primary_color`
     - `home_page_content` (JSON field for customizable content)
     - `per_minute_price`
     - `default_grading_category_id`
     - `created_at`
     - `updated_at`

3. **Row Level Security (RLS) Policies**
   - Update RLS policies to filter data based on `tenant_id`
   - Ensure users can only access data within their tenant

4. **Tenant-Aware UI Components**
   - Modify components to use tenant-specific styling and content
   - Create conditional rendering based on tenant context

5. **Authentication Flow Updates**
   - Modify signup and login to associate users with the correct tenant
   - Handle cross-tenant access control and permissions

## Data Models
1. **Tenant Model**
   ```sql
   CREATE TABLE tenants (
     id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
     domain TEXT UNIQUE NOT NULL,
     name TEXT NOT NULL,
     description TEXT,
     logo_url TEXT,
     primary_color TEXT,
     home_page_content JSONB DEFAULT '{}'::jsonb,
     per_minute_price NUMERIC(10,2) NOT NULL,
     default_grading_category_id UUID,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   ```

2. **User Model Update**
   ```sql
   ALTER TABLE users ADD COLUMN tenant_id UUID REFERENCES tenants(id);
   ```

3. **Recording Model Update**
   ```sql
   ALTER TABLE recordings ADD COLUMN tenant_id UUID REFERENCES tenants(id);
   ```

4. **Tenant-Specific Grading Categories**
   ```sql
   ALTER TABLE grading_categories ADD COLUMN tenant_id UUID REFERENCES tenants(id);
   ```

## APIs and Integrations
1. **Tenant Management API**
   - CRUD operations for tenant management
   - Domain mapping configuration
   - Tenant-specific settings management

2. **Tenant Context API**
   - Get current tenant information
   - Resolve tenant from domain
   - Get tenant-specific configuration

# Development Roadmap
## Phase 1: Foundation
1. Create the `tenants` table in the database
2. Add tenant_id columns to existing tables
3. Implement basic domain-to-tenant resolution middleware
4. Create tenant context provider for the frontend
5. Update authentication to associate users with tenants
6. Implement basic RLS policies for tenant data isolation

## Phase 2: Admin Interface
1. Create tenant management interface for administrators
2. Implement CRUD operations for tenants
3. Add domain mapping configuration
4. Develop tenant-specific settings management
5. Create tenant analytics dashboard

## Phase 3: Frontend Customization
1. Modify home page to use tenant-specific content
2. Implement tenant-specific styling and branding
3. Update navigation and menu based on tenant
4. Create tenant-specific pricing components
5. Implement tenant-specific grading categories UI

## Phase 4: Vertical-Specific Features
1. Implement sales-specific features and terminology
2. Add vertical-specific question templates
3. Create vertical-specific feedback mechanisms
4. Develop vertical-specific reporting

## Phase 5: Testing and Optimization
1. Set up test tenants with different configurations
2. Perform cross-tenant testing
3. Optimize database queries for multi-tenant environment
4. Implement caching for tenant configurations
5. Load testing with multiple tenants

# Logical Dependency Chain
1. Database schema changes must be implemented first (tenants table, tenant_id columns)
2. Domain resolution middleware needs to be in place before any tenant-specific features
3. Authentication system must be updated to associate users with tenants
4. RLS policies must be implemented to ensure data isolation
5. Tenant context provider must be available for frontend components
6. Admin interface for tenant management
7. Frontend customization based on tenant context
8. Vertical-specific features and content

# Risks and Mitigations
## Technical Challenges
1. **Risk**: Performance degradation due to additional tenant filtering in queries
   **Mitigation**: Implement proper indexing on tenant_id columns and optimize queries

2. **Risk**: Data leakage between tenants
   **Mitigation**: Thorough testing of RLS policies and access controls

3. **Risk**: Complexity in maintaining a single codebase for multiple verticals
   **Mitigation**: Use feature flags and clean abstraction layers

## Implementation Challenges
1. **Risk**: Disruption to existing users during migration
   **Mitigation**: Implement changes gradually and test thoroughly in staging environment

2. **Risk**: Increased maintenance overhead
   **Mitigation**: Automate tenant provisioning and configuration

3. **Risk**: Difficulty in scaling to many tenants
   **Mitigation**: Design for horizontal scaling from the beginning

# Appendix
## Initial Tenant Configuration for Sales Vertical
```json
{
  "domain": "sales.stagecoach.ai",
  "name": "Sales Practice Platform",
  "description": "Practice your sales pitches and objection handling",
  "logo_url": "/images/sales-logo.png",
  "primary_color": "#2E5BFF",
  "home_page_content": {
    "hero_title": "Master Your Sales Pitch",
    "hero_subtitle": "Practice, get AI feedback, and close more deals",
    "features": [
      {
        "title": "Objection Handling",
        "description": "Practice responding to common customer objections"
      },
      {
        "title": "Script Rehearsal",
        "description": "Perfect your sales script through repetition and feedback"
      },
      {
        "title": "AI Coaching",
        "description": "Get instant feedback on your persuasion techniques"
      }
    ]
  },
  "per_minute_price": 0.75,
  "grading_categories": [
    "Persuasiveness",
    "Product Knowledge",
    "Objection Handling",
    "Closing Techniques",
    "Active Listening"
  ]
}
```

## Example RLS Policy for Multi-tenant Data
```sql
CREATE POLICY tenant_isolation_policy ON recordings
  USING (tenant_id = auth.jwt()->>'tenant_id');
```
</PRD> 