{"name": "MissInterview", "private": true, "type": "module", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix"}, "dependencies": {"@mdi/font": "^7.0.96", "@supabase/supabase-js": "^2.49.4", "@vapi-ai/web": "^2.2.5", "chart.js": "^4.4.9", "firebase": "^10.9.0", "pdfjs-dist": "^3.11.174", "roboto-fontface": "*", "vue": "^3.4.24", "vue-gtag": "^3.5.0", "vue-router": "^4.3.0", "vuetify": "^3.5.9"}, "devDependencies": {"@eslint/js": "^9.14.0", "@vitejs/plugin-vue": "^5.0.5", "eslint": "^9.14.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.4.0", "eslint-plugin-vue": "^9.30.0", "pinia": "^2.1.7", "sass": "1.77.8", "sass-embedded": "^1.77.8", "unplugin-auto-import": "^0.17.6", "unplugin-fonts": "^1.1.1", "unplugin-vue-components": "^0.27.2", "unplugin-vue-router": "^0.10.0", "vite": "^5.4.0", "vite-plugin-vue-layouts": "^0.11.0", "vite-plugin-vuetify": "^2.0.3"}}