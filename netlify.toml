[build]
  publish = "dist"
  command = "pnpm install --no-frozen-lockfile && pnpm run build"

# Ensure static assets are served directly with proper MIME types
[[redirects]]
  from = "/assets/*"
  to = "/assets/:splat"
  status = 200
  force = false

# Handle SPA routing by redirecting all paths to index.html
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = true

# Cache control for static assets
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Ensure proper MIME types for JavaScript modules
[[headers]]
  for = "*.js"
  [headers.values]
    Content-Type = "application/javascript"

# Ensure proper MIME types for CSS
[[headers]]
  for = "*.css"
  [headers.values]
    Content-Type = "text/css"

# Cache static assets with a long TTL
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Environment variable configuration
[context.production.environment]
  NODE_VERSION = "18"

[context.deploy-preview.environment]
  NODE_VERSION = "18"

[context.branch-deploy.environment]
  NODE_VERSION = "18" 