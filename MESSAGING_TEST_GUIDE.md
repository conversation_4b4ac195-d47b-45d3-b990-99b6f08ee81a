# Messaging System Test Guide

## Overview
The messaging system has been implemented with real-time functionality using Supabase. This guide explains how to test the system.

## Database Structure
The messaging system uses the following tables:
- `conversations` - Main conversation records
- `conversation_participants` - Links users to conversations with relationship status
- `messages` - Individual messages
- `message_reactions` - Message reactions (future feature)
- `user_notification_preferences` - User notification settings
- `communities` - Community chat groups

## Test Page
Visit `/test-messages` to access the comprehensive test interface.

### Features Available for Testing:

1. **Authentication Status**
   - Shows current user authentication state
   - Displays user ID, email, and role

2. **Messages Store Status**
   - Shows loading states and error messages
   - Displays conversation and message counts

3. **Test Login**
   - Use existing user credentials to test authentication
   - Default email: `<EMAIL>` (you'll need the password)

4. **Conversation Management**
   - Load conversations for the authenticated user
   - Select conversations to view messages
   - View conversation details (type, participants, etc.)

5. **Message Testing**
   - Send test messages to active conversations
   - Real-time message updates

6. **Debug Information**
   - JSON view of current auth and messages store state

## Sample Data
The database includes:
- Multiple test users with different roles (admin, staff, user)
- Sample DM conversations between users
- Support conversations between users and staff
- Community conversations (Tech Talk, General Discussion, etc.)
- Sample messages in conversations

## Testing Steps

1. **Start the development server**
   ```bash
   npm run dev
   ```

2. **Visit the test page**
   Navigate to `http://localhost:3001/test-messages`

3. **Test authentication**
   - Enter valid credentials in the Test Login section
   - Click "Test Login" to authenticate

4. **Test conversation loading**
   - Click "Load Conversations" to fetch user conversations
   - Verify conversations appear in the list

5. **Test message viewing**
   - Click on a conversation to select it
   - Verify messages load and display correctly

6. **Test message sending**
   - Enter a test message in the "Test New Message" section
   - Click "Send Test Message"
   - Verify the message appears in the conversation

7. **Test real-time updates**
   - Open the same conversation in multiple browser tabs
   - Send a message from one tab
   - Verify it appears in real-time in other tabs

## Main Messages Page
Visit `/messages` to test the full messaging interface with:
- Sidebar with conversation categories
- Real-time message display
- Message composition
- Conversation management
- Notification handling

## Troubleshooting

### Common Issues:
1. **Authentication fails**: Verify user credentials exist in the database
2. **Conversations don't load**: Check user has conversation participants records
3. **Messages don't send**: Verify user is authenticated and has permission
4. **Real-time not working**: Check Supabase connection and RLS policies

### Debug Information:
- Check browser console for error messages
- Use the Debug Information section on the test page
- Verify database records in Supabase dashboard

## Database Queries for Testing

### Check user exists:
```sql
SELECT * FROM user_profiles WHERE email = '<EMAIL>';
```

### Check user conversations:
```sql
SELECT c.*, cp.relationship_status 
FROM conversations c
JOIN conversation_participants cp ON c.id = cp.conversation_id
WHERE cp.user_id = 'user-id-here';
```

### Check messages in conversation:
```sql
SELECT m.*, up.first_name, up.last_name
FROM messages m
JOIN user_profiles up ON m.sender_id = up.user_id
WHERE m.conversation_id = 'conversation-id-here'
ORDER BY m.created_at;
``` 