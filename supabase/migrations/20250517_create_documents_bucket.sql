-- Create a storage bucket for document uploads
INSERT INTO storage.buckets (id, name, public)
VALUES ('documents', 'documents', false);

-- Create storage policy for authenticated uploads to temp folder
CREATE POLICY "Allow authenticated users to upload documents to temp folder"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'documents' AND
  (storage.foldername(name))[1] = 'temp' AND
  (storage.foldername(name))[2] = auth.uid()::text
);

-- Allow authenticated users to access their own uploaded documents
CREATE POLICY "Allow authenticated users to access their own documents"
ON storage.objects
FOR SELECT
TO authenticated
USING (
  bucket_id = 'documents' AND
  (storage.foldername(name))[2] = auth.uid()::text
);

-- Allow authenticated users to delete their own temp documents
CREATE POLICY "Allow authenticated users to delete their temp documents"
ON storage.objects
FOR DELETE
TO authenticated
USING (
  bucket_id = 'documents' AND
  (storage.foldername(name))[1] = 'temp' AND
  (storage.foldername(name))[2] = auth.uid()::text
); 