-- Migration: Add status column to conversations table
-- Purpose: Enable closing/archiving support tickets and conversations
-- Affected tables: conversations
-- Special considerations: This adds a status field with default 'open' value

-- add status column to conversations table
alter table public.conversations 
add column status text not null default 'open';

-- add a check constraint to ensure only valid status values
alter table public.conversations 
add constraint conversations_status_check 
check (status in ('open', 'closed'));

-- create index on status for better query performance when filtering
create index idx_conversations_status on public.conversations (status);

-- create index on combined type and status for support ticket filtering
create index idx_conversations_type_status on public.conversations (type, status);

-- create function to update conversation status (admin/staff only)
create or replace function public.update_conversation_status(
  conversation_id uuid,
  new_status text
)
returns json
language plpgsql
security invoker
set search_path = ''
as $$
declare
  current_user_role text;
  conversation_record record;
  result json;
begin
  -- check if user is authenticated
  if auth.uid() is null then
    raise exception 'authentication required';
  end if;

  -- get current user role
  select role into current_user_role
  from public.user_profiles
  where user_id = auth.uid();

  -- only admin and staff can update conversation status
  if current_user_role not in ('admin', 'staff') then
    raise exception 'insufficient permissions - only admin and staff can update conversation status';
  end if;

  -- validate status value
  if new_status not in ('open', 'closed') then
    raise exception 'invalid status value - must be open or closed';
  end if;

  -- check if conversation exists and user has access to it
  select c.* into conversation_record
  from public.conversations c
  inner join public.conversation_participants cp 
    on c.id = cp.conversation_id
  where c.id = update_conversation_status.conversation_id
    and cp.user_id = auth.uid();

  if not found then
    raise exception 'conversation not found or access denied';
  end if;

  -- update the conversation status
  update public.conversations
  set 
    status = new_status,
    updated_at = now()
  where id = update_conversation_status.conversation_id;

  -- prepare result
  result := json_build_object(
    'success', true,
    'conversation_id', conversation_id,
    'old_status', conversation_record.status,
    'new_status', new_status,
    'updated_at', now()
  );

  return result;
end;
$$;

-- create rls policy for status column access
create policy "Users can view conversation status they participate in"
on public.conversations
for select
to authenticated
using (
  id in (
    select conversation_id
    from public.conversation_participants
    where user_id = auth.uid()
  )
);

-- create rls policy for status updates (admin/staff only)
create policy "Admin and staff can update conversation status"
on public.conversations
for update
to authenticated
using (
  (
    select role
    from public.user_profiles
    where user_id = auth.uid()
  ) in ('admin', 'staff')
)
with check (
  (
    select role
    from public.user_profiles
    where user_id = auth.uid()
  ) in ('admin', 'staff')
); 