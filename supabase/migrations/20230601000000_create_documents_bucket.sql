-- Create the documents bucket if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM storage.buckets WHERE name = 'documents'
    ) THEN
        INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
        VALUES ('documents', 'documents', false, 5242880, '{application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain}');
    END IF;
END $$;

-- Policy for document upload
DROP POLICY IF EXISTS "Users can upload documents" ON storage.objects;
CREATE POLICY "Users can upload documents" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'documents' 
  AND (storage.foldername(name))[1] = 'temp' 
  AND (storage.foldername(name))[2] = auth.uid()::text
);

-- Policy for document access
DROP POLICY IF EXISTS "Users can access their own documents" ON storage.objects;
CREATE POLICY "Users can access their own documents" 
ON storage.objects 
FOR SELECT 
TO authenticated 
USING (
  bucket_id = 'documents' 
  AND (storage.foldername(name))[1] = 'temp' 
  AND (storage.foldername(name))[2] = auth.uid()::text
);

-- Policy for document deletion
DROP POLICY IF EXISTS "Users can delete their own documents" ON storage.objects;
CREATE POLICY "Users can delete their own documents" 
ON storage.objects 
FOR DELETE 
TO authenticated 
USING (
  bucket_id = 'documents' 
  AND (storage.foldername(name))[1] = 'temp' 
  AND (storage.foldername(name))[2] = auth.uid()::text
); 