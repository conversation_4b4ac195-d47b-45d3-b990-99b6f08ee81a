import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@12.18.0?target=deno';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.7?target=deno';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Define the available plans
const PLANS = {
  starter: {
    name: 'Starter Plan',
    description: 'Try it out for 10 minutes',
    amount: 10,
    price: 10 // $10
  },
  standard: {
    name: 'Standard Plan',
    description: 'Ideal for serious competitors',
    amount: 100,
    price: 49 // $49
  },
  premium: {
    name: 'Premium Plan',
    description: 'For the crown contender',
    amount: 1000,
    price: 249 // $249
  }
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || 'sk_test_51RDvthPq8TmTTRzxv6WxtOB0pWBEwnNJtCaCzE8cC7UA49t62b6FLObLtdEA06qJJ8ZkUVKQxli2U2PJhpZflyy5003NP9DY9H', {
      apiVersion: '2023-10-16',
      httpClient: Stripe.createFetchHttpClient(),
    });

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

    // Parse request body
    const { plan = 'starter', amount, customerEmail } = await req.json();

    // Determine which plan to use
    const selectedPlan = PLANS[plan] || PLANS.starter;

    // If amount was explicitly provided, use that instead of the plan's default amount
    const minutesAmount = amount || selectedPlan.amount;
    const price = selectedPlan.price;

    // Check if the user has used a referral code before
    let showReferralField = true;
    if (customerEmail) {
      // Find user by email in auth.users
      const { data: userList, error: userError } = await supabase.auth.admin.listUsers();
      let userId = null;
      if (!userError && userList?.users) {
        const user = userList.users.find(u => u.email === customerEmail);
        if (user) {
          userId = user.id;
        }
      }
      if (userId) {
        // Query transactions for this user with a non-null referral_code
        const { data: txs, error: txError } = await supabase
          .from('transactions')
          .select('id')
          .eq('user_id', userId)
          .not('referral_code', 'is', null)
          .limit(1);
        if (!txError && txs && txs.length > 0) {
          showReferralField = false;
        }
      }
    }

    // Build custom_fields array if needed
    let custom_fields = undefined;
    if (showReferralField) {
      custom_fields = [
        {
          key: 'referralcode',
          label: { type: 'custom', custom: 'Referral Code' },
          type: 'text',
          optional: true,
        },
      ];
    }

    // Create Checkout Session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: selectedPlan.name,
              description: `${selectedPlan.description} (${minutesAmount} minutes)`,
            },
            unit_amount: price * 100, // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${req.headers.get('origin')}/account?status=success&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${req.headers.get('origin')}/account?status=canceled`,
      customer_email: customerEmail,
      metadata: {
        plan: plan,
        minutes: minutesAmount,
      },
      ...(custom_fields ? { custom_fields } : {}),
    });

    return new Response(JSON.stringify({ url: session.url }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
      },
    });
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
      },
    });
  }
});
