import { createClient } from 'jsr:@supabase/supabase-js@2';
import 'jsr:@supabase/functions-js/edge-runtime.d.ts';

// CORS headers for development and production
const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Use specific domains in production: 'https://missinterview.com, http://localhost:3000'
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

Deno.serve(async (req: Request) => {
  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    // Initialize the Supabase client with service role - needed for admin operations
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? '';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '';
    const supabaseAdmin = createClient(supabaseUrl, supabaseKey);

    // Check if the request is authenticated
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');

    // Get the current user's email from the token
    const { data: userData, error: userError } = await supabaseAdmin.auth.getUser(token);
    if (userError || !userData.user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Check if user is an admin
    if (userData.user.user_metadata?.role !== 'admin') {
      return new Response(JSON.stringify({ error: 'Unauthorized. Admin access required.' }), {
        status: 403,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    const { code, minutes, maxUses, expiresInDays, description } = await req.json();

    if (!minutes || isNaN(minutes) || minutes <= 0) {
      return new Response(JSON.stringify({ error: 'Minutes must be a positive number' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Generate a random code if not provided
    const couponCode = code || generateRandomCode(8);

    // Calculate expiration date if provided
    let expiresAt = null;
    if (expiresInDays && !isNaN(expiresInDays)) {
      expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + Number(expiresInDays));
    }

    // Create the coupon code
    const { data: couponData, error: couponError } = await supabaseAdmin
      .from('coupon_codes')
      .insert({
        code: couponCode,
        minutes: Number(minutes),
        max_uses: maxUses ? Number(maxUses) : null,
        use_count: 0,
        expires_at: expiresAt,
        is_active: true,
        created_by: userData.user.id,
        description: description || 'Test coupon code'
      })
      .select()
      .single();

    if (couponError) {
      return new Response(JSON.stringify({ error: `Failed to create coupon code: ${couponError.message}` }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: `Coupon code '${couponData.code}' created successfully for ${minutes} minutes.`,
      coupon: couponData
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (err) {
    console.error('Error creating coupon code:', err);

    return new Response(JSON.stringify({ error: 'Error processing your request' }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});

// Helper function to generate random coupon code
function generateRandomCode(length) {
  const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}
