import 'jsr:@supabase/functions-js/edge-runtime.d.ts';
import Stripe from 'npm:stripe@14.22.0';
import { createClient } from 'jsr:@supabase/supabase-js@2';

const stripeSecretKey = Deno.env.get('STRIPE_SECRET_KEY') || '';

const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';

// Use a valid Stripe API version
const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2024-04-10', // Using a stable API version that exists
  httpClient: Stripe.createFetchHttpClient(),
});

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

// Function to extract referral code from Stripe session custom fields
function extractReferralCode(session: any): string | null {
  if (!session.custom_fields || !Array.isArray(session.custom_fields)) {
    return null;
  }

  const referralField = session.custom_fields.find(
    (field: any) => field.key === 'referralcode' && field.type === 'text' && field.text?.value
  );

  return referralField ? referralField.text.value : null;
}

// Helper function to handle referral code
async function handleReferralCode(referralCode: string, purchasedMinutes: number, transactionId: bigint, userId: string) {
  try {
    console.log(`Processing referral code: ${referralCode}`);

    // Check if referral code exists in the database
    const { data: referralData, error: referralError } = await supabase
      .from('referral_codes')
      .select('id, user_id, code, use_count')
      .eq('code', referralCode)
      .eq('status', 'active')
      .single();

    if (referralError || !referralData) {
      console.log(`Invalid or inactive referral code: ${referralCode}`, referralError);
      return;
    }

    const referrerUserId = referralData.user_id;

    // Don't apply bonuses if user is referring themselves
    if (referrerUserId === userId) {
      console.log('User attempted to use their own referral code - skipping bonus');
      return;
    }

    const bonusAmount = Math.round(purchasedMinutes * 0.2); // 20% bonus
    console.log(`Calculated bonus amount: ${bonusAmount} minutes`);

    // Add bonus minutes to the referrer
    const { error: referrerMinutesError } = await supabase.from('user_minutes').insert({
      user_id: referrerUserId,
      minutes: bonusAmount,
      transaction_id: transactionId,
      note: `Referral bonus: ${referralCode}`,
      created_by: referrerUserId,
      is_bonus: true,
    });

    if (referrerMinutesError) {
      console.error('Error adding referrer bonus minutes:', referrerMinutesError);
    } else {
      console.log(`Added ${bonusAmount} bonus minutes to referrer ${referrerUserId}`);
    }

    // Add bonus minutes to the purchaser
    const { error: purchaserMinutesError } = await supabase.from('user_minutes').insert({
      user_id: userId,
      minutes: bonusAmount,
      transaction_id: transactionId,
      note: `Referral bonus: ${referralCode}`,
      created_by: userId,
      is_bonus: true,
    });

    if (purchaserMinutesError) {
      console.error('Error adding purchaser bonus minutes:', purchaserMinutesError);
    } else {
      console.log(`Added ${bonusAmount} bonus minutes to purchaser ${userId}`);
    }

    // Increment use count directly with a simple update
    const newUseCount = (referralData.use_count || 0) + 1;
    const { error: updateError } = await supabase
      .from('referral_codes')
      .update({ use_count: newUseCount })
      .eq('id', referralData.id);

    if (updateError) {
      console.error('Error updating referral code use count:', updateError);
    } else {
      console.log(`Updated referral code use count to ${newUseCount}`);
    }

    console.log(`Successfully applied referral bonus of ${bonusAmount} minutes for code ${referralCode}`);
  } catch (error) {
    console.error('Error processing referral code:', error);
  }
}

Deno.serve(async (req) => {
  try {
    // Get the raw request body
    const body = await req.text();
    const event = JSON.parse(body);

    console.log(`Processing ${event.type} event`);

    // Handle the checkout.session.completed event
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object;
      console.log('Processing checkout session:', session.id);

      // Extract customer email from the session
      const email = session.customer_details?.email;
      if (!email) {
        return new Response('No customer email in session', { status: 400 });
      }

      // Get line items from checkout session
      const lineItems = await stripe.checkout.sessions.listLineItems(session.id);

      if (!lineItems.data.length) {
        return new Response('No line items found', { status: 400 });
      }

      // Get the product details for the first line item
      const productId = lineItems.data[0].price?.product as string;
      if (!productId) {
        return new Response('No product ID found', { status: 400 });
      }

      const product = await stripe.products.retrieve(productId);
      console.log('Product details:', { id: product.id, name: product.name, metadata: product.metadata });

      // Extract minutes from the product metadata
      const minutes = parseInt(product.metadata.minutes || '0', 10);

      if (isNaN(minutes) || minutes <= 0) {
        return new Response('Invalid minutes value', { status: 400 });
      }

      // Check if user exists - using auth.users via the auth API
      const { data: userList, error: userError } = await supabase.auth.admin.listUsers();

      if (userError) {
        console.error('Error listing users:', userError);
      }

      // Find the user with the matching email
      const existingUser = userList?.users.find(user => user.email === email);

      let userId;

      if (existingUser) {
        // User exists, get their ID
        userId = existingUser.id;
        console.log(`Existing user found with ID: ${userId}`);
      } else {
        // Create new user
        const customerName = session.customer_details?.name || '';

        const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
          email,
          email_confirm: true,
          password: crypto.randomUUID().slice(0, 12), // Generate random secure password
          user_metadata: {
            full_name: customerName,
          },
        });

        if (authError || !authUser.user) {
          console.error('Error creating user:', authError);
          return new Response('Error creating user', { status: 500 });
        }

        userId = authUser.user.id;
        console.log(`Created new user with ID: ${userId}`);

        // The user profile should be created by the database trigger
        // Let's check if it exists before trying to create it manually
        const { data: existingProfile } = await supabase
          .from('user_profiles')
          .select('id')
          .eq('user_id', userId)
          .single();

        if (!existingProfile) {
          // Only create the profile if it doesn't already exist
          const names = customerName.split(' ');
          const firstName = names[0] || '';
          const lastName = names.slice(1).join(' ') || '';

          const { error: profileError } = await supabase.from('user_profiles').insert({
            user_id: userId,
            email,
            first_name: firstName,
            last_name: lastName,
          });

          if (profileError) {
            console.error('Error creating user profile:', profileError);
          } else {
            console.log(`Created user profile for ${firstName} ${lastName}`);
          }
        } else {
          console.log('User profile already exists, skipping creation');
        }

        // Generate and send password reset email
        try {
          const { data: resetData, error: resetError } = await supabase.auth.admin.generateLink({
            type: 'recovery',
            email,
          });

          if (resetError) {
            console.error('Error generating password reset link:', resetError);
          } else {
            console.log(`Password reset email sent successfully to ${email}`);
            if (resetData?.properties?.action_link) {
              console.log(`Link generated: ${resetData.properties.action_link}`);
            }
          }
        } catch (resetError) {
          console.error('Exception generating recovery link:', resetError);
        }
      }

      // Extract referral code from custom fields
      const referralCode = extractReferralCode(session);
      console.log(`Referral code extracted: ${referralCode || 'none'}`);

      // Create transaction record
      const { data: transaction, error: transactionError } = await supabase
        .from('transactions')
        .insert({
          user_id: userId,
          checkout_session_id: session.id,
          payment_intent_id: session.payment_intent,
          minutes,
          total: session.amount_total ? session.amount_total / 100 : 0, // Convert from cents to dollars
          product_name: product.name,
          type: 'purchase',
          livemode: session.livemode,
          referral_code: referralCode
        })
        .select()
        .single();

      if (transactionError || !transaction) {
        console.error('Error creating transaction:', transactionError);
        return new Response('Error creating transaction', { status: 500 });
      }

      console.log(`Created transaction with ID: ${transaction.id}`);

      // Add minutes to user
      const { error: minutesError } = await supabase.from('user_minutes').insert({
        user_id: userId,
        minutes,
        transaction_id: transaction.id,
        created_by: userId,
      });

      if (minutesError) {
        console.error('Error adding minutes:', minutesError);
        return new Response('Error adding minutes', { status: 500 });
      }

      console.log(`Added ${minutes} minutes to user ${userId}`);

      // Handle referral code if present
      if (referralCode) {
        await handleReferralCode(referralCode, minutes, transaction.id, userId);
      }

      return new Response(JSON.stringify({ success: true }), {
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Handle the charge.refunded event
    if (event.type === 'charge.refunded') {
      const charge = event.data.object;

      // Get the payment intent to find the checkout session
      const paymentIntentId = charge.payment_intent;
      if (!paymentIntentId) {
        return new Response('No payment intent ID found', { status: 400 });
      }

      // Find the transaction by payment_intent_id
      const { data: transactions, error: transactionError } = await supabase
        .from('transactions')
        .select('id, user_id, minutes')
        .eq('payment_intent_id', paymentIntentId);

      if (transactionError || !transactions || transactions.length === 0) {
        console.error('No transaction found for refund');
        return new Response('No transaction found', { status: 400 });
      }

      const transaction = transactions[0];

      // Create a 'refund' transaction
      await supabase.from('transactions').insert({
        user_id: transaction.user_id,
        minutes: -transaction.minutes, // Negative minutes for refund
        total: -(charge.amount_refunded / 100), // Convert from cents to dollars, negative for refund
        product_name: 'Refund',
        type: 'refund',
        payment_intent_id: paymentIntentId,
        livemode: charge.livemode,
      });

      // Deduct the minutes from the user (add negative minutes)
      await supabase.from('user_minutes').insert({
        user_id: transaction.user_id,
        minutes: -transaction.minutes, // Negative minutes for refund
        transaction_id: transaction.id,
        created_by: transaction.user_id,
        note: 'Refund',
      });

      return new Response(JSON.stringify({ success: true }), {
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Return 200 for other events
    return new Response(JSON.stringify({ received: true }), {
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error processing webhook:', error);
    return new Response(`Error processing webhook: ${error.message}`, { status: 500 });
  }
});
