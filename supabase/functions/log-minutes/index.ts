import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verify request method
    if (req.method !== 'POST') {
      throw new Error('Method not allowed')
    }

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('Missing or invalid authorization header')
    }

    // Get the service role key from environment variable
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')
    if (!serviceRoleKey) {
      throw new Error('Service role key not configured')
    }

    // Verify the authorization token matches the service role key
    const token = authHeader.replace('Bearer ', '')
    if (token !== serviceRoleKey) {
      throw new Error('Invalid authorization token')
    }

    // Parse request body
    const { user_id, minutes: rawMinutes, note, interview_id } = await req.json()

    // Validate required fields
    if (!user_id || rawMinutes == null) {
      throw new Error('Missing required fields')
    }

    // Initialize Supabase client with service role key
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      serviceRoleKey
    )

    // First, delete any pending records for this user
    const { error: deleteError } = await supabaseClient
      .from('user_minutes')
      .delete()
      .eq('user_id', user_id)
      .eq('status', 'pending')

    if (deleteError) {
      throw deleteError
    }

    // Get current user balance (excluding pending records since they're now deleted)
    const { data: balanceData, error: balanceError } = await supabaseClient
      .from('user_minutes')
      .select('minutes')
      .eq('user_id', user_id)

    if (balanceError) {
      throw balanceError
    }

    // Calculate current balance
    const currentBalance = balanceData.reduce((sum, record) => sum + (record.minutes || 0), 0)

    // Calculate minutes to deduct (negative value)
    let minutesToDeduct = -Math.ceil(Math.abs(rawMinutes)) // Default: round up to next minute

    // Special case: if this would put the balance negative by just one minute, round down instead
    if (currentBalance + minutesToDeduct === -1) {
      minutesToDeduct = -Math.floor(Math.abs(rawMinutes))
    }

    // Insert the record
    const { data, error } = await supabaseClient
      .from('user_minutes')
      .insert({
        user_id,
        minutes: minutesToDeduct,
        note,
        interview_id,
        created_at: new Date().toISOString()
      })
      .select()

    if (error) {
      throw error
    }

    return new Response(
      JSON.stringify({
        success: true,
        data,
        balance: currentBalance + minutesToDeduct // Include new balance in response
      }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 200
      }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      }
    )
  }
})
