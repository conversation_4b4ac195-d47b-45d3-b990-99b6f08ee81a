// SET LOCAL jwt.claims.user_id = NULL;

// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.177.0/http/server.ts"

console.log("Hello from Functions!")

serve(async (req) => {
  const { name } = await req.json()
  const data = {
    message: `Hello ${name}!`,
  }

  return new Response(
    JSON.stringify(data),
    { headers: { "Content-Type": "application/json" } },
  )
})

// To invoke:
// curl -i --location --request POST 'http://localhost:54321/functions/v1/' \
//   --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
//   --header 'Content-Type: application/json' \
//   --data '{"name":"Functions"}'

// curl -i --location --request POST 'https://rhsohskmamgopjujmdnx.functions.supabase.co/hello' \
//   --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJoc29oc2ttYW1nb3BqdWptZG54Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2NzU3NzQ4OTAsImV4cCI6MTk5MTM1MDg5MH0.kti6vNTfCac2ErTDp_m3kMiAu1zoEJqDuVbpzp30wyE' \
//   --header 'Content-Type: application/json' \
//   --data '{"name":"Functions"}'
