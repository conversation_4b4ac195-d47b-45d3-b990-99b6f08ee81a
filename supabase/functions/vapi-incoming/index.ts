// Setup type definitions for Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};
// Create a Supabase client using the service role key
const supabaseAdmin = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '');
// Default interview focus options
const interviewFocusOptions = [
  'Current Events',
  'Social Impact Initiative',
  'Personal Background'
];
// Function to check if phone number exists in interviews table
async function checkPhoneNumberExists(phoneNumber) {
  try {
    if (!phoneNumber) return false;
    console.log(`Checking if phone number ${phoneNumber} exists in interviews table`);
    const { data, error, count } = await supabaseAdmin.from('interviews').select('id', {
      count: 'exact'
    }).eq('phone_number', phoneNumber).limit(1);
    if (error) {
      console.error('Error checking phone number existence:', error);
      return false;
    }
    const exists = count > 0;
    console.log(`Phone number ${phoneNumber} ${exists ? 'exists' : 'does not exist'} in interviews table`);
    return exists;
  } catch (error) {
    console.error('Error in checkPhoneNumberExists:', error);
    return false;
  }
}
// Function to fetch a user profile by phone number
async function fetchUserProfileByPhone(phoneNumber) {
  try {
    if (!phoneNumber) return null;

    // Format the phone number - remove +1 if present
    let formattedNumber = phoneNumber;
    if (phoneNumber.startsWith('+1')) {
      formattedNumber = phoneNumber.substring(2); // Remove the +1 prefix
    }

    console.log(`Looking up user profile by phone number: ${phoneNumber} (formatted: ${formattedNumber})`);

    const { data, error } = await supabaseAdmin
      .from('user_profiles')
      .select('id, user_id, first_name, last_name, resume, city, state, social_impact_initiative, current_events, current_events_text, current_events_updated_at, pageant_title, mobile_phone')
      .eq('mobile_phone', formattedNumber)
      .maybeSingle();

    if (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }
    if (!data) {
      console.log(`No user profile found for phone number: ${formattedNumber}`);
      return null;
    }
    console.log('Found user profile:', data);
    return data;
  } catch (error) {
    console.error('Error in fetchUserProfileByPhone:', error);
    return null;
  }
}

// Function to check user's available minutes
async function getUserAvailableMinutes(userId) {
  try {
    if (!userId) return 0;

    console.log(`Checking available minutes for user: ${userId}`);
    const { data, error } = await supabaseAdmin
      .from('user_minutes')
      .select('minutes')
      .eq('user_id', userId);

    if (error) {
      console.error('Error fetching user minutes:', error);
      return 0;
    }

    // Sum the minutes to get the total
    const totalMinutes = data.reduce((sum, record) => sum + parseFloat(record.minutes || 0), 0);
    console.log(`User ${userId} has ${totalMinutes} minutes available`);

    return totalMinutes;
  } catch (error) {
    console.error('Error in getUserAvailableMinutes:', error);
    return 0;
  }
}

// Function to fetch the unregistered fallback user by email
async function fetchUnregisteredUser() {
  try {
    console.log('Looking up unregistered fallback user');
    // Try to use user profiles table directly
    const { data: profileData, error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .select('id, user_id, first_name, last_name')
      .eq('email', '<EMAIL>')
      .maybeSingle();

    if (profileError || !profileData) {
      console.error('Error or no result fetching unregistered user profile:', profileError);
      return null;
    }

    console.log('Found unregistered user profile by email:', profileData);
    return profileData;
  } catch (error) {
    console.error('Error in fetchUnregisteredUser:', error);
    return null;
  }
}
// Function to fetch default interview profile
async function fetchDefaultInterviewProfile() {
  try {
    const { data, error } = await supabaseAdmin.from('interview_profiles').select('*').eq('status', 'active').order('created_at', {
      ascending: false
    }).limit(1).single();
    if (error) {
      console.error('Error fetching default interview profile:', error);
      return null;
    }
    return data;
  } catch (error) {
    console.error('Error in fetchDefaultInterviewProfile:', error);
    return null;
  }
}
// Function to create a new interview record
async function createInterviewRecord(userId, profileId, title = 'Phone Session', phoneNumber = null) {
  try {
    if (!userId) {
      console.warn('Cannot create interview record: No user ID provided');
      return null;
    }
    // Prepare the interview record object
    const interviewRecord = {
      title: title,
      user_id: userId,
      profile_id: profileId,
      source: 'phone',
      created_at: new Date().toISOString()
    };
    // Add phone number if available
    if (phoneNumber) {
      interviewRecord.phone_number = phoneNumber;
    }
    const { data, error } = await supabaseAdmin.from('interviews').insert(interviewRecord).select('id').single();
    if (error) {
      throw error;
    }
    console.log('Created new interview record with ID:', data.id);
    return data.id;
  } catch (error) {
    console.error('Error creating interview record:', error);
    return null;
  }
}
// Helper function to generate the system message with user profile data
function getSystemMessage(userProfile, interviewProfile, focus = []) {
  let systemMessage = 'You are a helpful interviewer assistant';
  if (interviewProfile?.prompt) {
    // Replace placeholder with user's resume if available
    let promptText = interviewProfile.prompt;
    // Only include sections that match the selected focus areas
    const includeUserBio = !focus.length || focus.includes('Personal Background');
    const includeCurrentEvents = !focus.length || focus.includes('Current Events');
    const includeSocialImpact = !focus.length || focus.includes('Social Impact Initiative');
    // Replace user bio placeholder
    if (userProfile?.resume && promptText.includes('{{USER_BIO}}') && includeUserBio) {
      const bioPrefix = "The following is the contestant's bio/resume. Ask several personal, relevant questions based on this information. Focus on hobbies, goals, causes they care about, or achievements. Let their personality and values shine through.\n\nAsk thoughtful follow-up questions based on their answers.\n\nBIO/RESUME:\n";
      promptText = promptText.replace('{{USER_BIO}}', bioPrefix + userProfile.resume);
    } else if (promptText.includes('{{USER_BIO}}')) {
      // If resume is not available or not in focus, replace with empty string
      promptText = promptText.replace('{{USER_BIO}}', '');
    }
    // Replace user name placeholder
    if (userProfile?.first_name && userProfile?.last_name && promptText.includes('{{USER_NAME}}')) {
      const fullName = `${userProfile.first_name} ${userProfile.last_name}`;
      promptText = promptText.replace(/{{USER_NAME}}/g, fullName);
    }
    // Replace user location placeholder
    if (userProfile?.city && userProfile?.state && promptText.includes('{{USER_LOCATION}}')) {
      const location = `${userProfile.city}, ${userProfile.state}`;
      promptText = promptText.replace(/{{USER_LOCATION}}/g, location);
    }
    // Replace pageant title placeholder
    if (promptText.includes('{{PAGEANT_TITLE}}')) {
      const pageantTitle = userProfile?.pageant_title || 'beauty pageant';
      promptText = promptText.replace(/{{PAGEANT_TITLE}}/g, pageantTitle);
    }
    // Replace user social impact initiative placeholder
    if (userProfile?.social_impact_initiative && promptText.includes('{{USER_SOCIAL_IMPACT_INITIATIVE}}') && includeSocialImpact) {
      const impactPrefix = "The contestant has declared the following Social Impact Initiative. Ask at least one question based on this topic. You may ask why they chose this cause, what actions they've taken, what outcomes they've seen, or how they plan to advance the initiative if they win the title.\n\nSOCIAL IMPACT INITIATIVE:\n";
      promptText = promptText.replace(/{{USER_SOCIAL_IMPACT_INITIATIVE}}/g, impactPrefix + userProfile.social_impact_initiative);
    } else if (promptText.includes('{{USER_SOCIAL_IMPACT_INITIATIVE}}')) {
      // If social impact initiative is not available or not in focus, replace with empty string
      promptText = promptText.replace(/{{USER_SOCIAL_IMPACT_INITIATIVE}}/g, '');
    }
    // Replace current events placeholder
    if (userProfile?.current_events_text && promptText.includes('{{CURRENT_EVENTS}}') && includeCurrentEvents) {
      const eventsPrefix = "The following are recent current events. Ask one or more questions that connect to these headlines or summaries. Choose topics that would prompt thoughtful opinions, values-based responses, or critical thinking.\n\nQuestions may explore:\n- Their view on the topic\n- Whether titleholders should speak out about it\n- How the issue affects young women or their community\n- Ethical or leadership challenges involved\n\nCURRENT EVENTS:\n";
      promptText = promptText.replace(/{{CURRENT_EVENTS}}/g, eventsPrefix + userProfile.current_events_text);
    } else if (promptText.includes('{{CURRENT_EVENTS}}')) {
      // If current events text is not available or not in focus, replace with empty string
      promptText = promptText.replace(/{{CURRENT_EVENTS}}/g, '');
    }
    systemMessage = promptText;
  }
  console.log('Generated system message for user');
  return systemMessage;
}
// Function to build server URL for webhook
function buildServerUrl(userId, interviewId, profileId, title = 'Phone Session') {
  let url = "https://automation.aiedgemedia.com/webhook/vapi?";
  // Add required parameters
  if (userId) {
    url += `userId=${userId}`;
  } else {
    url += "userId=phone_user"; // Fallback for users without accounts
  }
  // Add title
  url += `&title=${encodeURIComponent(title)}`;
  // Add profile ID if available
  if (profileId) {
    url += `&profileId=${profileId}`;
  }
  // Add interview ID if available
  if (interviewId) {
    url += `&interviewId=${interviewId}`;
  }
  // Add source parameter to indicate phone call
  url += "&source=phone";
  return url;
}
// Main handler function
Deno.serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  try {
    // Only accept POST requests
    if (req.method !== 'POST') {
      throw new Error('Method not allowed');
    }
    // Parse request body
    const requestData = await req.json();
    console.log('Received incoming VAPI request:', JSON.stringify(requestData));
    // Extract phone number from request (if available)
    let callerPhoneNumber = null;
    let focus = [];
    let callTitle = 'Phone Session';
    if (requestData.message?.customer?.number) {
      callerPhoneNumber = requestData.message.customer.number;
      console.log('Caller phone number:', callerPhoneNumber);
    }
    // Extract optional title from request
    if (requestData.params?.title) {
      callTitle = requestData.params.title;
    }
    // Check if focus areas are specified in the request
    if (requestData.params?.focus) {
      try {
        if (typeof requestData.params.focus === 'string') {
          focus = JSON.parse(requestData.params.focus);
        } else if (Array.isArray(requestData.params.focus)) {
          focus = requestData.params.focus;
        }
        console.log('Using focus areas:', focus);
      } catch (e) {
        console.error('Error parsing focus parameter:', e);
      // Use default (empty array) if parsing fails
      }
    }
    // Fetch user profile based on phone number
    let userProfile = null;
    let isUnregisteredUser = false;
    let hasCalledBefore = false;
    let shouldAllowCall = false;
    if (callerPhoneNumber) {
      userProfile = await fetchUserProfileByPhone(callerPhoneNumber);
    }

    // Check if user has available minutes
    let userMinutes = 0;
    if (userProfile?.user_id) {
      userMinutes = await getUserAvailableMinutes(userProfile.user_id);
      console.log(`User has ${userMinutes} minutes available`);

      // If user has minutes available, allow the call
      if (userMinutes > 0) {
        shouldAllowCall = true;
        console.log(`User has ${userMinutes} minutes available, allowing call`);
      }
    }

    // If no user profile found by phone number, check if they've called before
    if (!userProfile && callerPhoneNumber) {
      console.log('No user profile found by phone number, checking if they have called before');
      hasCalledBefore = await checkPhoneNumberExists(callerPhoneNumber);

      // In either case, use the unregistered fallback user
      isUnregisteredUser = true;
      userProfile = await fetchUnregisteredUser();
      if (!userProfile) {
        console.error('Failed to fetch unregistered fallback user');
      } else {
        console.log('Using unregistered fallback user profile:', userProfile);
      }
    }
    // Fetch default interview profile
    const interviewProfile = await fetchDefaultInterviewProfile();
    if (!interviewProfile) {
      console.warn('No interview profile found, using default system message');
    }
    // Create interview record
    let interviewId = null;
    if (userProfile?.user_id) {
      interviewId = await createInterviewRecord(userProfile.user_id, interviewProfile?.id || null, callTitle, callerPhoneNumber // Pass phone number to save in the record
      );
      console.log(`Created interview record ${interviewId} for user ${userProfile.user_id}`);
    }
    // Build server URL
    const serverUrl = buildServerUrl(userProfile?.user_id || null, interviewId, interviewProfile?.id || null, callTitle);
    console.log('Generated server URL:', serverUrl);
    // Generate system message with user profile data if available
    const systemMessage = getSystemMessage(userProfile, interviewProfile, focus);
    // Create appropriate first message based on user status
    let firstMessage = "Thank you for calling the Miss Interview.com phone line. We have up to 10 minutes to practice our interview. Registered users can be interviewed on their resume, social impact initiative, and local current events. Please start by giving me your name and the name of the pageant you want to practice for.";
    let endCallMessage = null; // By default, don't end the call

    // For unregistered users who have called before, check if we should allow the call
    if (isUnregisteredUser && hasCalledBefore && !shouldAllowCall) {
      console.log('Unregistered user has called before, setting end call message');
      firstMessage = "Thank you for calling the Miss Interview.com interview line. I see that you've called before. To continue, please register at Miss Interview.com. Registered users benefit from being able to be interviewed on their resume, social impact initiative, and local current events. I hope to see you at Miss Interview.com. Goodbye.";
      endCallMessage = "Goodbye."; // Set end call message to terminate the call after first message
    } else if (userProfile?.first_name && userProfile?.mobile_phone && !isUnregisteredUser) {
      firstMessage = `Hello, ${userProfile.first_name}. Thank you for calling Miss Interview.com. We have up to 10 minutes for this interview. Are you ready to get started?`;
    }
    // Create a VAPI configuration for the assistant
    const vapiConfig = {
      "assistant": {
        "name": "Interview Assistant",
        "voice": {
          "voiceId": "Paige",
          "provider": "vapi"
        },
        "model": {
          "model": "gpt-4o-mini",
          "messages": [
            {
              "role": "system",
              "content": systemMessage
            }
          ],
          "provider": "openai",
          "temperature": 0.5
        },
        "firstMessage": firstMessage,
        "transcriber": {
          "model": "nova-3",
          "language": "en",
          "numerals": false,
          "provider": "deepgram",
          "endpointing": 150,
          "confidenceThreshold": 0.4
        },
        "clientMessages": [
          "transcript",
          "hang",
          "function-call",
          "speech-update",
          "metadata",
          "conversation-update",
          "workflow.node.started"
        ],
        "serverMessages": [
          "end-of-call-report"
        ],
        "endCallPhrases": [
          "goodbye",
          "talk to you soon"
        ],
        "hipaaEnabled": false,
        "backgroundSound": "off",
        "backchannelingEnabled": false,
        "backgroundDenoisingEnabled": true,
        "startSpeakingPlan": {
          "waitSeconds": 0.4,
          "transcriptionEndpointingPlan": {
            "onNumberSeconds": 0.5
          },
          "smartEndpointingEnabled": "livekit"
        },
        "server": {
          "url": serverUrl
        }
      }
    };
    // Add endCallMessage if specified (for returning unregistered users)
    if (endCallMessage) {
      vapiConfig.assistant.endCallMessage = endCallMessage;
    }
    // Log the configuration being returned
    console.log('Returning VAPI configuration with customized prompt and server URL');
    // Return the configuration
    return new Response(JSON.stringify(vapiConfig), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  } catch (error) {
    console.error('Error processing VAPI request:', error);
    return new Response(JSON.stringify({
      error: error.message || 'An error occurred processing the VAPI request'
    }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
});
