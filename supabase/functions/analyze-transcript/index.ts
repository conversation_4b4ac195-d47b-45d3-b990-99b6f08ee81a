// Setup type definitions for Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Define common filler words to detect
const FILLER_WORDS = [
  // Basic fillers
  'um', 'uh', 'er', 'ah', 'like', 'you know', 'so', 'okay', 'ok', 'yeah', 'uh-huh',
  // Hedge words
  'sort of', 'kind of', 'basically', 'actually', 'literally', 'honestly', 'really',
  // Discourse markers
  'i mean', 'right', 'well', 'just', 'anyway',
  // Speech disfluencies
  'i think', 'i guess', 'you see', 'hmm', 'mhm'
];

// Create Supabase client with service role key (admin privileges)
const createSupabaseClient = () => {
  const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
  const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase environment variables');
  }

  return createClient(supabaseUrl, supabaseServiceKey);
};

// Helper to get interview by ID
async function getInterview(supabase, interviewId) {
  console.log(`Fetching interview with ID: ${interviewId}`);

  const { data, error } = await supabase
    .from('interviews')
    .select('id, user_transcription, analysis')
    .eq('id', interviewId)
    .single();

  if (error) {
    console.error('Error fetching interview:', error);
    throw new Error(`Failed to fetch interview: ${error.message}`);
  }

  if (!data) {
    throw new Error(`Interview with ID ${interviewId} not found`);
  }

  return data;
}

// Helper to update interview analysis
async function updateInterviewAnalysis(supabase, interviewId, analysis) {
  console.log(`Updating interview ${interviewId} with analysis data`);

  const { data, error } = await supabase
    .from('interviews')
    .update({ analysis })
    .eq('id', interviewId)
    .select('id, analysis');

  if (error) {
    console.error('Error updating interview analysis:', error);
    throw new Error(`Failed to update interview analysis: ${error.message}`);
  }

  return data;
}

// Word counting helpers
function countWords(text) {
  if (!text) return 0;
  // Split by whitespace and filter out empty strings
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
}

// Function to count occurrences of filler words
function analyzeFillerWords(text) {
  if (!text) {
    return {
      total_words: 0,
      total_filler_words: 0,
      filler_word_frequency: {},
      filler_word_percentage: 0,
    };
  }

  // Normalize text for consistent analysis
  const normalizedText = text.toLowerCase();

  // Count total words
  const totalWords = countWords(normalizedText);

  // Count each filler word
  const fillerWordFrequency = {};
  let totalFillerWords = 0;

  FILLER_WORDS.forEach(fillerWord => {
    // Create regex to match whole words or phrases
    // This prevents matching substrings within other words
    // For example, "um" shouldn't match "album"
    let regex;
    if (fillerWord.includes(' ')) {
      // For phrases, match the exact phrase
      regex = new RegExp(`\\b${fillerWord}\\b`, 'gi');
    } else {
      // For single words, make sure they are surrounded by word boundaries
      regex = new RegExp(`\\b${fillerWord}\\b`, 'gi');
    }

    // Count occurrences
    const matches = normalizedText.match(regex) || [];
    const count = matches.length;

    if (count > 0) {
      fillerWordFrequency[fillerWord] = count;
      totalFillerWords += count;
    }
  });

  // Calculate percentage
  const percentage = totalWords > 0 ? (totalFillerWords / totalWords) * 100 : 0;

  return {
    total_words: totalWords,
    total_filler_words: totalFillerWords,
    filler_word_frequency: fillerWordFrequency,
    filler_word_percentage: parseFloat(percentage.toFixed(2)), // Limit to 2 decimal places
  };
}

// Main function to handle requests
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Only accept POST requests
    if (req.method !== 'POST') {
      throw new Error('Method not allowed');
    }

    // Parse request body
    const { interviewId } = await req.json();

    if (!interviewId) {
      throw new Error('Missing required parameter: interviewId');
    }

    console.log(`Processing analysis for interview ID: ${interviewId}`);

    // Initialize Supabase client
    const supabase = createSupabaseClient();

    // Get interview data
    const interview = await getInterview(supabase, interviewId);

    if (!interview.user_transcription) {
      throw new Error('Interview has no transcription to analyze');
    }

    // Analyze the transcription
    const fillerWordsAnalysis = analyzeFillerWords(interview.user_transcription);

    console.log(`Analysis complete: Found ${fillerWordsAnalysis.total_filler_words} filler words in ${fillerWordsAnalysis.total_words} total words`);

    // Prepare the analysis update object, preserving existing analysis data
    let analysisObject = {};

    // If the interview already has an analysis JSON object, use it
    if (interview.analysis) {
      analysisObject = { ...interview.analysis };
    }

    // Add or update the filler_words property
    analysisObject.filler_words = fillerWordsAnalysis;

    // Update the interview with the analysis results
    const updatedInterview = await updateInterviewAnalysis(supabase, interviewId, analysisObject);

    return new Response(JSON.stringify({
      success: true,
      message: 'Transcript analysis complete',
      interviewId,
      analysis: updatedInterview[0].analysis
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('Error:', error);

    return new Response(JSON.stringify({
      success: false,
      error: error.message || 'An error occurred during transcript analysis'
    }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
});
