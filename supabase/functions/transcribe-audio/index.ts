// Setup type definitions for Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

// Import AWS SDK modules for transcription and S3
import { TranscribeClient, StartTranscriptionJobCommand, GetTranscriptionJobCommand, DeleteTranscriptionJobCommand } from 'npm:@aws-sdk/client-transcribe';
import { S3Client, PutObjectCommand, DeleteObjectCommand } from 'npm:@aws-sdk/client-s3';

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Get environment variables
const SUPABASE_URL = Deno.env.get('SUPABASE_URL');
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
const AWS_REGION = Deno.env.get('AWS_REGION');
const AWS_ACCESS_KEY_ID = Deno.env.get('AWS_ACCESS_KEY_ID');
const AWS_SECRET_ACCESS_KEY = Deno.env.get('AWS_SECRET_ACCESS_KEY');
const AWS_BUCKET = Deno.env.get('AWS_BUCKET');
const ANALYZE_TRANSCRIPT_URL = Deno.env.get('ANALYZE_TRANSCRIPT_URL');

// Debug: Log environment variable status (only whether they exist, not their values)
console.log('Environment variables check:');
console.log(`- SUPABASE_URL exists: ${!!SUPABASE_URL}`);
console.log(`- SUPABASE_SERVICE_ROLE_KEY exists: ${!!SUPABASE_SERVICE_ROLE_KEY}`);
console.log(`- AWS_REGION exists: ${!!AWS_REGION}`);
console.log(`- AWS_ACCESS_KEY_ID exists: ${!!AWS_ACCESS_KEY_ID}`);
console.log(`- AWS_SECRET_ACCESS_KEY exists: ${!!AWS_SECRET_ACCESS_KEY}`);
console.log(`- AWS_BUCKET exists: ${!!AWS_BUCKET}`);
console.log(`- ANALYZE_TRANSCRIPT_URL exists: ${!!ANALYZE_TRANSCRIPT_URL}`);

// Check if we have all required environment variables
if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('Missing required Supabase environment variables');
}

if (!AWS_REGION || !AWS_ACCESS_KEY_ID || !AWS_SECRET_ACCESS_KEY || !AWS_BUCKET) {
  console.error('Missing required AWS environment variables');
}

// Set up AWS configuration
const awsConfig = {
  region: AWS_REGION,
  credentials: {
    accessKeyId: AWS_ACCESS_KEY_ID || '',
    secretAccessKey: AWS_SECRET_ACCESS_KEY || ''
  }
};

console.log(`AWS Config - Using region: ${awsConfig.region}`);

// Set up clients
let transcribeClient;
let s3Client;

try {
  transcribeClient = new TranscribeClient(awsConfig);
  console.log('TranscribeClient created successfully');
} catch (error) {
  console.error('Error creating TranscribeClient:', error.message);
}

try {
  s3Client = new S3Client(awsConfig);
  console.log('S3Client created successfully');
} catch (error) {
  console.error('Error creating S3Client:', error.message);
}

// Create Supabase client
const supabaseAdmin = createClient(
  SUPABASE_URL || '',
  SUPABASE_SERVICE_ROLE_KEY || ''
);

// Helper function to download a file from a URL
async function downloadFile(url: string): Promise<ArrayBuffer> {
  console.log(`Downloading file from URL: ${url}`);
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to download file: ${response.statusText}`);
  }
  const buffer = await response.arrayBuffer();
  console.log(`Downloaded file size: ${buffer.byteLength} bytes`);
  return buffer;
}

// Helper function to upload a file to S3
async function uploadToS3(data: ArrayBuffer, key: string): Promise<string> {
  if (!AWS_BUCKET) {
    throw new Error('AWS_BUCKET environment variable is not set');
  }

  console.log(`Uploading to S3 bucket: ${AWS_BUCKET}, key: ${key}, size: ${data.byteLength} bytes`);

  const command = new PutObjectCommand({
    Bucket: AWS_BUCKET,
    Key: key,
    Body: new Uint8Array(data),
    ContentType: 'audio/wav'
  });

  try {
    await s3Client.send(command);
    console.log('Upload to S3 completed successfully');
    return `s3://${AWS_BUCKET}/${key}`;
  } catch (error) {
    console.error('Error uploading to S3:', error.message);
    throw error;
  }
}

// Helper function to send transcript for analysis
async function sendTranscriptForAnalysis(interviewId: string, transcript: string): Promise<void> {
  if (!ANALYZE_TRANSCRIPT_URL) {
    console.log('ANALYZE_TRANSCRIPT_URL not set, skipping analysis');
    return;
  }

  try {
    console.log(`Sending transcript to analysis service: ${ANALYZE_TRANSCRIPT_URL}`);

    const response = await fetch(ANALYZE_TRANSCRIPT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        interviewId,
        transcript
      })
    });

    if (!response.ok) {
      throw new Error(`Analysis service returned error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('Analysis response:', result);
    console.log('Transcript sent for analysis successfully');
  } catch (error) {
    console.error('Error sending transcript for analysis:', error);
    // We don't throw here because this is a non-critical step
    // The transcription process should be considered successful even if analysis fails
  }
}

// Helper function to wait until transcription job is complete
async function waitForTranscriptionJob(jobName: string, maxAttempts = 60): Promise<any> {
  let attempts = 0;

  while (attempts < maxAttempts) {
    console.log(`Checking transcription job status (attempt ${attempts + 1}/${maxAttempts}): ${jobName}`);
    const command = new GetTranscriptionJobCommand({
      TranscriptionJobName: jobName
    });

    try {
      const response = await transcribeClient.send(command);
      const job = response.TranscriptionJob;
      const status = job?.TranscriptionJobStatus;

      console.log(`Transcription job status: ${status}`);

      // Detailed logging of job information
      if (job) {
        console.log('Job details:');
        console.log(`- Job Name: ${job.TranscriptionJobName}`);
        console.log(`- Status: ${job.TranscriptionJobStatus}`);
        // Check for Transcript object and TranscriptFileUri
        console.log(`- Has Transcript object: ${!!job.Transcript}`);
        if (job.Transcript) {
          console.log(`- Has TranscriptFileUri: ${!!job.Transcript.TranscriptFileUri}`);
          console.log(`- Transcript URI: ${job.Transcript.TranscriptFileUri || 'Not available'}`);
        }
      } else {
        console.log('Warning: No job details returned');
      }

      if (status === 'COMPLETED') {
        console.log('Transcription job completed successfully');
        return job;
      } else if (status === 'FAILED') {
        console.error(`Transcription job failed: ${job?.FailureReason || 'Unknown reason'}`);
        throw new Error(`Transcription job failed: ${job?.FailureReason || 'Unknown reason'}`);
      }
    } catch (error) {
      console.error(`Error checking job status: ${error.message}`);
      // If this is a temporary error, we'll try again
      if (attempts >= maxAttempts - 1) {
        throw error; // Only throw if we're on the last attempt
      }
    }

    // Sleep for 5 seconds before checking again
    await new Promise(resolve => setTimeout(resolve, 5000));
    attempts++;
  }

  throw new Error('Transcription job timed out');
}

// Helper function to fetch and parse transcription results
async function getTranscriptionResults(transcriptUrl: string): Promise<string> {
  console.log(`Fetching transcription results from: ${transcriptUrl}`);

  try {
    const response = await fetch(transcriptUrl);
    console.log(`Transcript fetch response status: ${response.status}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch transcription results: ${response.statusText}`);
    }

    const text = await response.text();
    console.log(`Raw response text length: ${text.length}`);
    console.log(`Raw response preview: ${text.substring(0, 200)}...`);

    let data;
    try {
      data = JSON.parse(text);
    } catch (parseError) {
      console.error('Error parsing JSON from transcript response:', parseError);
      throw new Error(`Failed to parse transcript JSON: ${parseError.message}`);
    }

    if (!data.results || !data.results.transcripts) {
      console.error('Unexpected transcript format:', data);
      throw new Error('Transcript data is not in the expected format');
    }

    const transcript = data.results.transcripts.map((t: any) => t.transcript).join(' ');
    console.log(`Received transcript (${transcript.length} characters)`);
    return transcript;
  } catch (error) {
    console.error(`Error getting transcription results: ${error.message}`);
    throw error;
  }
}

// Main handler function
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Only accept POST requests
    if (req.method !== 'POST') {
      throw new Error('Method not allowed');
    }

    // Parse request body
    const { audioFileUrl, interviewId } = await req.json();

    if (!audioFileUrl || !interviewId) {
      throw new Error('Missing required parameters: audioFileUrl and interviewId are required');
    }

    console.log(`Processing audio file: ${audioFileUrl} for interview: ${interviewId}`);

    // Validate required AWS services are available
    if (!s3Client || !transcribeClient) {
      throw new Error('AWS services not properly initialized. Check environment variables.');
    }

    // Download the file from the provided URL
    const audioData = await downloadFile(audioFileUrl);

    // Generate a unique S3 key for the audio file
    const s3Key = `interviews/${interviewId}/audio-${Date.now()}.wav`;

    // Upload to S3
    const s3Uri = await uploadToS3(audioData, s3Key);

    console.log(`Uploaded to S3: ${s3Uri}`);

    // Generate a unique job name
    const jobName = `transcribe-${interviewId}-${Date.now()}`;

    // Start transcription job
    console.log(`Starting transcription job: ${jobName} with media URI: ${s3Uri}`);
    const startCommand = new StartTranscriptionJobCommand({
      TranscriptionJobName: jobName,
      Media: {
        MediaFileUri: s3Uri
      },
      MediaFormat: 'wav',
      LanguageCode: 'en-US'
    });

    try {
      await transcribeClient.send(startCommand);
      console.log(`Started transcription job: ${jobName}`);
    } catch (error) {
      console.error('Error starting transcription job:', error);
      throw new Error(`Failed to start transcription job: ${error.message}`);
    }

    // For immediate response - don't wait for completion
    const immediateResponse = {
      status: 'processing',
      message: 'Audio file uploaded and transcription job started',
      interviewId,
      jobName
    };

    // Process transcription asynchronously
    (async () => {
      try {
        // Wait for the transcription job to complete
        const job = await waitForTranscriptionJob(jobName);
        console.log('Complete job object:', JSON.stringify(job, null, 2));

        if (!job) {
          console.error('Job object is null or undefined after completion');
          return;
        }

        // Check for Transcript.TranscriptFileUri instead of just TranscriptFileUri
        if (!job.Transcript || !job.Transcript.TranscriptFileUri) {
          console.error('TranscriptFileUri is missing from completed job');
          if (job.Transcript) {
            console.log('Transcript object exists but TranscriptFileUri is missing');
          }
          return;
        }

        console.log(`Transcript URI found: ${job.Transcript.TranscriptFileUri}`);

        // Get transcription results
        try {
          const transcript = await getTranscriptionResults(job.Transcript.TranscriptFileUri);
          console.log(`Successfully retrieved transcript for interview ${interviewId} (${transcript.substring(0, 50)}...)`);

          // Update the interview record with the transcription
          console.log(`Updating interview ${interviewId} with transcript`);
          const { data, error: updateError } = await supabaseAdmin
            .from('interviews')
            .update({ user_transcription: transcript })
            .eq('id', interviewId)
            .select();

          if (updateError) {
            console.error('Error updating interview record:', updateError);
          } else {
            console.log(`Successfully updated interview ${interviewId} with transcription`);
            console.log('Update response:', data);

            // Send the transcript for analysis after successful database update
            await sendTranscriptForAnalysis(interviewId, transcript);
          }
        } catch (transcriptError) {
          console.error('Error processing transcript:', transcriptError);
        }

        // Clean up: Delete the source audio file from S3
        try {
          const deleteObjectCommand = new DeleteObjectCommand({
            Bucket: AWS_BUCKET,
            Key: s3Key
          });

          await s3Client.send(deleteObjectCommand);
          console.log(`Deleted audio file from S3: ${s3Key}`);
        } catch (deleteError) {
          console.error('Error deleting audio file from S3:', deleteError);
        }

        // Clean up: Delete the transcription job
        try {
          const deleteJobCommand = new DeleteTranscriptionJobCommand({
            TranscriptionJobName: jobName
          });

          await transcribeClient.send(deleteJobCommand);
          console.log(`Deleted transcription job: ${jobName}`);
        } catch (deleteJobError) {
          console.error('Error deleting transcription job:', deleteJobError);
        }
      } catch (processingError) {
        console.error('Error in background processing:', processingError);
      }
    })();

    // Return immediate response
    return new Response(JSON.stringify(immediateResponse), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('Error:', error);

    return new Response(JSON.stringify({
      error: error.message || 'An error occurred during transcription'
    }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
});
