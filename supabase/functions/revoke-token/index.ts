// Setup type definitions for Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Create Supabase admin client with service role key
const createSupabaseAdmin = () => {
  const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
  const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase environment variables');
  }

  return createClient(supabaseUrl, supabaseServiceKey);
};

// Function to extract and verify JWT from request headers
function extractToken(req) {
  const authHeader = req.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.split(' ')[1];
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Accept any JWT token, including anonymous ones
    const token = extractToken(req);
    if (!token) {
      return new Response(JSON.stringify({
        code: 401,
        message: 'Missing authorization header'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    // Only accept POST requests
    if (req.method !== 'POST') {
      throw new Error('Method not allowed');
    }

    // Parse request body
    const { token: shareToken } = await req.json();

    if (!shareToken) {
      throw new Error('Missing required parameter: token');
    }

    console.log(`Revoking token: ${shareToken}`);

    // Initialize Supabase admin client with service role key
    const supabaseAdmin = createSupabaseAdmin();

    // Revoke the token
    const { data, error } = await supabaseAdmin
      .from('share_tokens')
      .update({ revoked_at: new Date().toISOString() })
      .eq('token', shareToken)
      .is('revoked_at', null)
      .select();

    if (error) {
      console.error('Error revoking token:', error);
      throw error;
    }

    const wasTokenRevoked = data && data.length > 0;
    console.log(`Token revocation result: ${wasTokenRevoked ? 'Success' : 'No matching token found'}`);

    return new Response(JSON.stringify({
      success: true,
      message: wasTokenRevoked ? 'Token revoked successfully' : 'No matching active token found',
      wasRevoked: wasTokenRevoked
    }), {
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });

  } catch (error) {
    console.error('Error:', error);

    return new Response(JSON.stringify({
      success: false,
      error: error.message || 'An error occurred while revoking token'
    }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
});
