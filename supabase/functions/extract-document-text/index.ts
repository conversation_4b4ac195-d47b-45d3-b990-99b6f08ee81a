/**
 * Supabase Edge Function for extracting text from uploaded documents
 *
 * This extracts text from PDF, DOC, DOCX, and TXT files uploaded to Supabase Storage.
 */

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.21.0'
import * as mammoth from 'https://esm.sh/mammoth@1.6.0'

// Use CDN-served worker to avoid bundling issues
pdfjs.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@4.0.269/build/pdf.worker.min.js';

interface RequestBody {
  fileId?: string;
  bucketName: string;
  filePath: string;
}

// Set CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders,
      status: 204,
    })
  }

  try {
    // Parse request data
    const { bucketName, filePath } = await req.json()

    if (!bucketName || !filePath) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters: bucketName and filePath' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create a Supabase client with the Auth header from the request
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    )

    // Verify the user is authenticated
    const {
      data: { user },
      error: authError,
    } = await supabaseClient.auth.getUser()

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Download the file from storage
    const { data, error: downloadError } = await supabaseClient.storage
      .from(bucketName)
      .download(filePath)

    if (downloadError || !data) {
      return new Response(
        JSON.stringify({ error: `Error downloading file: ${downloadError?.message || 'Unknown error'}` }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Determine file type from path
    const fileExtension = filePath.split('.').pop()?.toLowerCase()
    let extractedText = ''

    // Process different file types
    if (fileExtension === 'docx') {
      // Convert the ArrayBuffer to Uint8Array for mammoth
      const arrayBuffer = await data.arrayBuffer()
      const result = await mammoth.extractRawText({ arrayBuffer })
      extractedText = result.value
    } else if (fileExtension === 'doc') {
      // Legacy .doc files are not directly supported
      return new Response(
        JSON.stringify({
          error: "Legacy .doc files are not supported. Please convert to .docx and try again."
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    } else if (fileExtension === 'txt') {
      // For text files, just read the text
      extractedText = await data.text()
    } else {
      return new Response(
        JSON.stringify({ error: `Unsupported file type: ${fileExtension}` }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Return the extracted text
    return new Response(
      JSON.stringify({ text: extractedText }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Error processing document:', error)
    return new Response(
      JSON.stringify({ error: `Error processing document: ${error.message || 'Unknown error'}` }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
