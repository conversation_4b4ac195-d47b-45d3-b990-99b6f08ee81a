import { createClient } from 'jsr:@supabase/supabase-js@2';
import 'jsr:@supabase/functions-js/edge-runtime.d.ts';

// CORS headers for development and production
const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Use specific domains in production: 'https://missinterview.com, http://localhost:3000'
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

Deno.serve(async (req: Request) => {
  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    // Initialize the Supabase client with service role - needed for admin operations
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? '';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '';
    const supabaseAdmin = createClient(supabaseUrl, supabaseKey);

    // Initialize authenticated client to use RLS policies
    const authHeader = req.headers.get('Authorization')!;
    const token = authHeader.replace('Bearer ', '');

    // Get the current user's ID from the token
    const { data: userData, error: userError } = await supabaseAdmin.auth.getUser(token);
    if (userError || !userData.user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    const { couponCode } = await req.json();

    if (!couponCode) {
      return new Response(JSON.stringify({ error: 'Coupon code is required' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Check if coupon code exists and is active
    const { data: couponData, error: couponError } = await supabaseAdmin
      .from('coupon_codes')
      .select('*')
      .eq('code', couponCode)
      .eq('is_active', true)
      .single();

    if (couponError || !couponData) {
      return new Response(JSON.stringify({ error: 'Invalid or expired coupon code' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Check if coupon has reached max uses
    if (couponData.max_uses && couponData.use_count >= couponData.max_uses) {
      return new Response(JSON.stringify({ error: 'This coupon code has reached its maximum usage limit' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Check if coupon has expired
    if (couponData.expires_at && new Date(couponData.expires_at) < new Date()) {
      return new Response(JSON.stringify({ error: 'This coupon code has expired' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Check if user has already used this coupon
    const { data: existingUse, error: existingUseError } = await supabaseAdmin
      .from('user_minutes')
      .select('id')
      .eq('user_id', userData.user.id)
      .eq('coupon_code_id', couponData.id)
      .limit(1);

    if (existingUseError) {
      return new Response(JSON.stringify({ error: 'Error checking coupon usage' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    if (existingUse && existingUse.length > 0) {
      return new Response(JSON.stringify({ error: 'You have already used this coupon code' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // All checks passed, add minutes to the user's account
    const { data: minutesData, error: minutesError } = await supabaseAdmin
      .from('user_minutes')
      .insert({
        user_id: userData.user.id,
        minutes: couponData.minutes,
        note: `Coupon Code: ${couponData.code}`,
        coupon_code_id: couponData.id
      })
      .select('*')
      .single();

    if (minutesError) {
      return new Response(JSON.stringify({ error: 'Error adding minutes to your account' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Increment the coupon use count
    const { error: updateError } = await supabaseAdmin
      .from('coupon_codes')
      .update({ use_count: couponData.use_count + 1 })
      .eq('id', couponData.id);

    if (updateError) {
      console.error('Error updating coupon use count:', updateError);
      // We don't return an error here since the minutes were already added
      // Just log the error for monitoring
    }

    // Return success response
    return new Response(JSON.stringify({
      success: true,
      message: `Success! ${couponData.minutes} minutes have been added to your account.`,
      minutes: couponData.minutes,
      data: minutesData
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (err) {
    console.error('Error processing coupon redemption:', err);

    return new Response(JSON.stringify({ error: 'Error processing your request' }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});
