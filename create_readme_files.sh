#!/bin/bash

# <PERSON>ript to create README.md files for all directories in the Supabase export package
# Usage: ./create_readme_files.sh

# Set base directory
BASE_DIR="supabase-export"

# Function to create README file with given content
create_readme() {
  local dir="$1"
  local content="$2"
  echo -e "$content" > "$dir/README.md"
  echo "Created README.md in $dir"
}

# Main README
MAIN_README="# Supabase Project Export Package

This package contains a complete export of a Supabase project, organized for easy import into a new Supabase instance.

## Directory Structure

- **database/** - Database schema, functions, triggers, and policies
- **storage/** - Storage bucket configurations and policies
- **edge-functions/** - Edge function code and configurations
- **scripts/** - Utility scripts for import and verification
- **docs/** - Documentation on how to use this package

## Usage

See the documentation in the \`docs/\` directory for detailed instructions on how to import this package into a new Supabase instance.
"

# Database README
DATABASE_README="# Database Components

This directory contains all database-related components from the Supabase project.

## Contents

- **tables/** - Table definitions, constraints, and indexes
- **functions/** - Database functions and procedures
- **triggers/** - Database triggers
- **policies/** - Row Level Security (RLS) policies
"

# Database Tables README
TABLES_README="# Database Tables

This directory contains SQL scripts for creating all database tables, including their constraints and indexes.

Each file follows the Supabase migration format and can be executed in order to recreate the table structure.
"

# Database Functions README
FUNCTIONS_README="# Database Functions

This directory contains SQL scripts for creating all database functions.

Each function is stored in a separate file with appropriate security settings and search path configurations.
"

# Database Triggers README
TRIGGERS_README="# Database Triggers

This directory contains SQL scripts for creating all database triggers.

Each trigger is stored with its associated function to ensure proper creation order.
"

# Database Policies README
POLICIES_README="# Row Level Security Policies

This directory contains SQL scripts for creating all Row Level Security (RLS) policies.

Policies are organized by table and include separate files for different operations (SELECT, INSERT, UPDATE, DELETE).
"

# Storage README
STORAGE_README="# Storage Configuration

This directory contains storage-related configurations from the Supabase project.

## Contents

- **buckets/** - Storage bucket definitions
- **policies/** - Storage access policies
"

# Storage Buckets README
BUCKETS_README="# Storage Buckets

This directory contains SQL scripts for creating storage buckets with their configuration settings.

Each bucket definition includes name, public/private settings, and file size limits.
"

# Storage Policies README
POLICIES_STORAGE_README="# Storage Policies

This directory contains SQL scripts for creating storage access policies.

Policies are organized by bucket and include separate files for different operations (SELECT, INSERT, DELETE).
"

# Edge Functions README
EDGE_FUNCTIONS_README="# Edge Functions

This directory contains all edge function code from the Supabase project.

Each function is stored in its own directory with the same structure as in the original project.
"

# Scripts README
SCRIPTS_README="# Utility Scripts

This directory contains utility scripts for importing and verifying the Supabase export package.

## Available Scripts

- **validate_structure.sh** - Verifies the export package structure
- **import_database.sh** - Imports database components into a new Supabase instance
- **deploy_edge_functions.sh** - Deploys edge functions to a new Supabase instance
"

# Docs README
DOCS_README="# Documentation

This directory contains comprehensive documentation on how to use this export package.

## Contents

- **import_guide.md** - Step-by-step guide for importing into a new Supabase instance
- **environment_variables.md** - List of required environment variables
- **verification.md** - How to verify successful import
"

# Create all README files
create_readme "$BASE_DIR" "$MAIN_README"
create_readme "$BASE_DIR/database" "$DATABASE_README"
create_readme "$BASE_DIR/database/tables" "$TABLES_README"
create_readme "$BASE_DIR/database/functions" "$FUNCTIONS_README"
create_readme "$BASE_DIR/database/triggers" "$TRIGGERS_README"
create_readme "$BASE_DIR/database/policies" "$POLICIES_README"
create_readme "$BASE_DIR/storage" "$STORAGE_README"
create_readme "$BASE_DIR/storage/buckets" "$BUCKETS_README"
create_readme "$BASE_DIR/storage/policies" "$POLICIES_STORAGE_README"
create_readme "$BASE_DIR/edge-functions" "$EDGE_FUNCTIONS_README"
create_readme "$BASE_DIR/scripts" "$SCRIPTS_README"
create_readme "$BASE_DIR/docs" "$DOCS_README"

echo "All README.md files created successfully!"

# Make the script executable
chmod +x "$0" 