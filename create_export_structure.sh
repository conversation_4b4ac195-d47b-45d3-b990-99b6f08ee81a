#!/bin/bash

# Script to create the directory structure for Supabase export package
# Usage: ./create_export_structure.sh

# Set base directory
BASE_DIR="supabase-export"

# Create root directory
mkdir -p "$BASE_DIR"
echo "Created root directory: $BASE_DIR"

# Create first-level directories
FIRST_LEVEL_DIRS=("database" "storage" "edge-functions" "scripts" "docs")
for dir in "${FIRST_LEVEL_DIRS[@]}"; do
  mkdir -p "$BASE_DIR/$dir"
  echo "Created directory: $BASE_DIR/$dir"
done

# Create database subdirectories
DATABASE_SUBDIRS=("tables" "functions" "triggers" "policies")
for dir in "${DATABASE_SUBDIRS[@]}"; do
  mkdir -p "$BASE_DIR/database/$dir"
  echo "Created directory: $BASE_DIR/database/$dir"
done

# Create storage subdirectories
STORAGE_SUBDIRS=("buckets" "policies")
for dir in "${STORAGE_SUBDIRS[@]}"; do
  mkdir -p "$BASE_DIR/storage/$dir"
  echo "Created directory: $BASE_DIR/storage/$dir"
done

echo "Directory structure created successfully!"

# Make the script executable
chmod +x "$0"

echo "Next step: Create README.md files for each directory" 