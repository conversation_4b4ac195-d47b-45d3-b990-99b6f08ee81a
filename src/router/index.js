/**
 * router/index.ts
 *
 * Automatic routes for `./src/pages/*.vue`
 */

// Composables
import { createRouter, createWebHistory } from 'vue-router/auto'
import { setupLayouts } from 'virtual:generated-layouts'
import { routes } from 'vue-router/auto-routes'
import { useAuthStore } from '@/stores/auth'

// Import interview components
import InterviewDetails from '@/pages/interview/[id].vue'
import EmptyInterviewState from '@/components/EmptyInterviewState.vue'
import InterviewsPage from '@/pages/interviews.vue'
import AdminPage from '@/pages/admin.vue'
import DashboardPage from '@/pages/dashboard.vue'
import PrivacyPolicyPage from '@/pages/privacy-policy.vue'
import TermsOfServicePage from '@/pages/terms-of-service.vue'
import DataDeletionPage from '@/pages/data-deletion.vue'
import SupportPage from '@/pages/support.vue'
import ProfilePage from '@/pages/profile.vue'
import ActivityPage from '@/pages/activity.vue'
import RewardsPage from '@/pages/rewards.vue'
import DocumentationPage from '@/pages/documentation.vue'
import SupportTicketsPage from '@/pages/support-tickets.vue'
// TEMPORARILY DISABLED - Messages feature is hidden for now
// import MessagesPage from '@/pages/messages.vue'
import TestMessagesPage from '@/pages/test-messages.vue'

const dashboardRoute = {
  path: '/dashboard',
  component: DashboardPage,
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}

const interviewsRoute = {
  path: '/interviews',
  component: InterviewsPage,
  meta: {
    requiresAuth: true,
    layout: 'default'
  },
  children: [
    {
      path: '',
      name: 'interviews-empty',
      component: EmptyInterviewState
    },
    {
      path: ':id',
      name: 'interview-details',
      component: InterviewDetails,
      props: true
    }
  ]
}

const adminRoute = {
  path: '/admin',
  component: AdminPage,
  meta: {
    requiresAuth: true,
    requiresAdmin: true,
    layout: 'default',
  },
  props: route => ({ tab: route.params.tab }),
  children: [
    {
      path: '',
      name: 'admin',
      component: AdminPage,
      props: true,
    },
    {
      path: ':tab',
      name: 'admin-tab',
      component: AdminPage,
      props: true,
    },
  ],
}

const privacyPolicyRoute = {
  path: '/privacy-policy',
  component: PrivacyPolicyPage,
  meta: {
    requiresAuth: false,
    layout: 'default'
  }
}

const termsOfServiceRoute = {
  path: '/terms-of-service',
  component: TermsOfServicePage,
  meta: {
    requiresAuth: false,
    layout: 'default'
  }
}

const dataDeletionRoute = {
  path: '/data-deletion',
  component: DataDeletionPage,
  meta: {
    requiresAuth: false,
    layout: 'default'
  }
}

const supportRoute = {
  path: '/support',
  component: SupportPage,
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}

const profileRoute = {
  path: '/profile',
  component: ProfilePage,
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}

const activityRoute = {
  path: '/activity',
  component: ActivityPage,
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}

const rewardsRoute = {
  path: '/rewards',
  component: RewardsPage,
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}

const documentationRoute = {
  path: '/documentation',
  component: DocumentationPage,
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}

const supportTicketsRoute = {
  path: '/support-tickets',
  component: SupportTicketsPage,
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}

// Add dynamic support ticket route
const supportTicketDetailRoute = {
  path: '/support-tickets/:id',
  component: () => import('@/pages/support-tickets/[id].vue'),
  props: true,
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}

// TEMPORARILY DISABLED - Messages feature is hidden for now
// const messagesRoute = {
//   path: '/messages',
//   component: MessagesPage,
//   meta: {
//     requiresAuth: true,
//     layout: 'default'
//   }
// }

const testMessagesRoute = {
  path: '/test-messages',
  component: TestMessagesPage,
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}

export const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    ...setupLayouts(routes.filter(route =>
      route.path !== '/interview/:id' &&
      route.path !== '/interviews' &&
      route.path !== '/admin' &&
      route.path !== '/admin/:tab' &&
      route.path !== '/dashboard' &&
      route.path !== '/privacy-policy' &&
      route.path !== '/terms-of-service' &&
      route.path !== '/data-deletion' &&
      route.path !== '/support' &&
      route.path !== '/profile' &&
      route.path !== '/activity' &&
      route.path !== '/rewards' &&
      route.path !== '/documentation' &&
      route.path !== '/support-tickets' &&
      route.path !== '/support-tickets/:id' &&
      route.path !== '/messages'
    )),
    setupLayouts([dashboardRoute])[0],
    setupLayouts([interviewsRoute])[0],
    setupLayouts([adminRoute])[0],
    setupLayouts([privacyPolicyRoute])[0],
    setupLayouts([termsOfServiceRoute])[0],
    setupLayouts([dataDeletionRoute])[0],
    setupLayouts([supportRoute])[0],
    setupLayouts([profileRoute])[0],
    setupLayouts([activityRoute])[0],
    setupLayouts([rewardsRoute])[0],
    setupLayouts([documentationRoute])[0],
    setupLayouts([supportTicketsRoute])[0],
    setupLayouts([supportTicketDetailRoute])[0],
    // TEMPORARILY DISABLED - Messages route is hidden for now
    // setupLayouts([messagesRoute])[0],
    setupLayouts([testMessagesRoute])[0],
  ],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      // If there's a saved position (e.g., when using browser back/forward buttons)
      return savedPosition
    }
    if (to.hash) {
      // If the route has a hash, scroll to the element with that ID
      return {
        el: to.hash,
        behavior: 'smooth',
        top: 80, // Offset for the fixed header
      }
    }
    // Otherwise scroll to the top of the page
    return { top: 0 }
  },
})

// Authentication guard
router.beforeEach(async (to, from, next) => {
  console.log('Route navigation:', { to: to.path, from: from.path });
  const authStore = useAuthStore()

  // Check for password reset URL pattern
  if (to.path === '/' && to.hash.includes('type=recovery')) {
    // Keep all the hash parameters and redirect to reset-password
    return next({
      path: '/reset-password',
      hash: to.hash
    })
  }

  // Redirect root path to dashboard for authenticated users
  if (to.path === '/' && authStore.isAuthenticated && !to.hash) {
    return next('/dashboard')
  }

  // Always initialize the auth store on navigation to ensure authentication state is current
  // This is especially important on page reloads
  try {
    // If initialization is already in progress, wait for it to complete
    if (authStore.loading) {
      // Wait for loading to finish before proceeding
      const checkLoading = () => {
        return new Promise(resolve => {
          const interval = setInterval(() => {
            if (!authStore.loading) {
              clearInterval(interval)
              resolve()
            }
          }, 50)
        })
      }
      await checkLoading()
    } else if (!authStore.isAuthenticated) {
      // Only initialize if not already authenticated
      await authStore.initialize()
    }
  } catch (error) {
    console.error('Error initializing auth store:', error)
  }

  const requiresAuth = to.meta.requiresAuth === true // Only require auth when explicitly set to true
  const requiresAdmin = to.meta.requiresAdmin === true

  if (requiresAuth && !authStore.isAuthenticated) {
    console.log('Route requires auth but user is not authenticated, redirecting to login')
    return next('/login')
  }

  if (requiresAdmin && !authStore.isAdmin) {
    console.log('Route requires admin but user is not admin, redirecting to dashboard')
    return next('/dashboard')
  }

  next()
})

// Workaround for https://github.com/vitejs/vite/issues/11804
router.onError((err, to) => {
  if (err?.message?.includes?.('Failed to fetch dynamically imported module')) {
    if (!localStorage.getItem('vuetify:dynamic-reload')) {
      console.log('Reloading page to fix dynamic import error')
      localStorage.setItem('vuetify:dynamic-reload', 'true')
      location.assign(to.fullPath)
    } else {
      console.error('Dynamic import error, reloading page did not fix it', err)
    }
  } else {
    console.error(err)
  }
})

// Add a catch-all route for admin user paths that might be missed
// This is a safety measure for direct navigation
console.log('Adding catch-all route for admin user paths')
router.addRoute({
  path: '/admin/user/:id(.*)',
  redirect: to => {
    console.log('Redirecting from catch-all to admin user edit:', to.params.id)
    return {
      name: 'admin-user-edit',
      params: { id: to.params.id }
    }
  }
})

router.isReady().then(() => {
  localStorage.removeItem('vuetify:dynamic-reload')
})

// Note: These routes are now handled by the router-fix.js plugin
// which applies the proper layouts and ensures no route conflicts

export default router
