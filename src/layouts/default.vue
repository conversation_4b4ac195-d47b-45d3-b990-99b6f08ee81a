<template>
  <v-app
    :theme="themeStore.theme"
    class="app-container"
  >
    <!-- Check if we're in self-destruct mode -->
    <template v-if="isSelfDestruct">
      <!-- Use v-sheet instead of a div for better theme color support -->
      <v-sheet
        color="primary"
        class="non-sticky-header"
      >
        <v-container
          class="py-0 fill-height px-0 mx-0"
          fluid
        >
          <v-row
            align="center"
            justify="center"
            no-gutters
          >
            <!-- Center section with logo only -->
            <v-col class="d-flex justify-center">
              <div class="text-decoration-none text-white d-flex align-center">
                <v-img
                  src="@/assets/MISS-INTERVIEW-2-WHITE.png"
                  height="35"
                  width="200"
                  contain
                />
              </div>
            </v-col>
          </v-row>
        </v-container>
      </v-sheet>

      <v-main>
        <router-view />
      </v-main>
      <!-- No footer in self-destruct mode -->
    </template>

    <!-- Regular layout -->
    <template v-else>
      <v-app-bar
        app
        color="primary"
        height="64"
      >
        <v-container
          class="pa-0"
          fluid
        >
          <v-row
            align="center"
            no-gutters
          >
            <!-- Left section - Mobile menu icon and desktop navigation -->
            <v-col
              cols="4"
              class="d-flex align-center"
            >
              <!-- Mobile Menu Icon (visible on sm and down) -->
              <v-app-bar-nav-icon
                variant="text"
                class="hidden-md-and-up text-white"
                @click.stop="mobileMenuOpen = !mobileMenuOpen"
              />

              <!-- Desktop navigation links (visible on md and up) -->
              <div class="d-none d-md-flex">
                <v-btn
                  variant="text"
                  class="text-white"
                  @click="navigateTo('/', '#home')"
                >
                  Home
                </v-btn>
                <v-btn
                  variant="text"
                  class="text-white"
                  @click="navigateTo('/', '#pricing')"
                >
                  Pricing
                </v-btn>
                <v-btn
                  variant="text"
                  class="text-white"
                  @click="navigateTo('/', '#contact')"
                >
                  Contact
                </v-btn>
                <v-btn
                  v-if="authStore.isAuthenticated"
                  variant="text"
                  class="text-white"
                  to="/support"
                >
                  Support
                </v-btn>
                <!-- TEMPORARILY DISABLED - Messages feature is hidden for now -->
                <!-- <v-btn
                  v-if="authStore.isAuthenticated"
                  variant="text"
                  class="text-white"
                  to="/messages"
                >
                  Messages
                </v-btn> -->
              </div>
            </v-col>

            <!-- Center column with logo -->
            <v-col
              cols="4"
              class="d-flex justify-center"
            >
              <router-link
                :to="authStore.isAuthenticated ? '/dashboard' : '/'"
                class="text-decoration-none text-white d-flex align-center"
              >
                <v-img
                  src="@/assets/MISS-INTERVIEW-2-WHITE.png"
                  height="35"
                  width="200"
                  contain
                />
              </router-link>
            </v-col>

            <!-- Right section with auth buttons -->
            <v-col
              cols="4"
              class="d-flex align-center justify-end"
            >
              <template v-if="authStore.isAuthenticated">
                <!-- Balance chip - now clickable -->
                <v-chip
                  class="mr-3 balance-chip"
                  color="white"
                  text-color="primary"
                  variant="elevated"
                  label
                  pill
                  size="small"
                  style="cursor: pointer"
                  @click="showPurchaseDialog = true"
                >
                  <v-icon
                    start
                    size="small"
                    color="primary"
                  >
                    mdi-clock-outline
                  </v-icon>
                  {{ minutesStore.formattedBalance }} min
                </v-chip>

                <!-- User dropdown menu -->
                <v-menu
                  v-model="menu"
                  :close-on-content-click="false"
                  location="bottom end"
                  min-width="200"
                >
                  <template #activator="{ props }">
                    <v-btn
                      icon
                      v-bind="props"
                      class="d-flex align-center mr-4"
                    >
                      <v-avatar
                        size="32"
                        color="transparent"
                      >
                        <v-icon color="white">
                          mdi-account-circle
                        </v-icon>
                      </v-avatar>
                    </v-btn>
                  </template>

                  <v-card>
                    <v-list>
                      <v-list-item prepend-icon="mdi-account">
                        <template #title>
                          <div class="text-subtitle-1 d-flex align-center">
                            {{ authStore.user?.email }}
                            <v-chip
                              v-if="authStore.user?.user_metadata?.role"
                              class="ml-2"
                              size="small"
                              color="primary"
                              label
                            >
                              {{ authStore.user.user_metadata.role }}
                            </v-chip>
                          </div>
                        </template>
                      </v-list-item>

                      <v-divider />

                      <v-list-item
                        prepend-icon="mdi-view-dashboard"
                        title="Dashboard"
                        to="/dashboard"
                        @click="menu = false"
                      />

                      <v-list-item @click="toggleMode">
                        <template #prepend>
                          <v-icon>{{ themeStore.mode === 'dark' ? 'mdi-weather-sunny' : 'mdi-weather-night' }}</v-icon>
                        </template>
                        <template #title>
                          {{ themeStore.mode === 'dark' ? 'Light Mode' : 'Dark Mode' }}
                        </template>
                      </v-list-item>

                      <!-- Theme selection dropdown -->
                      <v-list-item>
                        <template #prepend>
                          <v-icon>mdi-palette</v-icon>
                        </template>
                        <template #title>
                          <v-select
                            v-model="themeStore.themeName"
                            :items="themeOptions"
                            item-title="display"
                            item-value="name"
                            label="Theme Style"
                            hide-details
                            density="compact"
                            style="min-width: 160px;"
                            variant="tonal"
                            @update:model-value="onThemeChange"
                          />
                        </template>
                      </v-list-item>

                      <v-divider />

                      <!-- Admin option (only visible to admin users) -->
                      <template v-if="authStore.user?.user_metadata?.role === 'admin'">
                        <v-list-item
                          prepend-icon="mdi-shield-account"
                          title="Admin"
                          to="/admin"
                          @click="menu = false"
                        />
                        <v-divider />
                      </template>

                      <v-list-item
                        prepend-icon="mdi-logout"
                        title="Logout"
                        @click="logout"
                      />
                    </v-list>
                  </v-card>
                </v-menu>
              </template>
              <template v-else>
                <v-btn
                  variant="text"
                  to="/login"
                  class="text-white mr-4"
                >
                  <v-icon class="mr-1">
                    mdi-login
                  </v-icon>
                  Login
                </v-btn>
              </template>
            </v-col>
          </v-row>
        </v-container>
      </v-app-bar>

      <!-- Mobile navigation drawer -->
      <v-navigation-drawer
        v-model="mobileMenuOpen"
        temporary
        app
        location="left"
        class="hidden-md-and-up"
      >
        <v-list nav>
          <!-- Show app navigation for authenticated users -->
          <template v-if="authStore.isAuthenticated">
            <v-list-item
              prepend-icon="mdi-view-dashboard"
              title="Dashboard"
              to="/dashboard"
              @click="mobileMenuOpen = false"
            />
            <v-list-item
              prepend-icon="mdi-clipboard-text-outline"
              title="Interviews"
              to="/interviews"
              @click="mobileMenuOpen = false"
            />
            <v-list-item
              prepend-icon="mdi-account-details"
              title="Profile"
              to="/profile"
              @click="mobileMenuOpen = false"
            />
            <v-list-item
              prepend-icon="mdi-history"
              title="Activity"
              to="/activity"
              @click="mobileMenuOpen = false"
            />
            <v-list-item
              prepend-icon="mdi-star"
              title="Rewards"
              to="/rewards"
              @click="mobileMenuOpen = false"
            />
            <v-list-item
              prepend-icon="mdi-account-cog"
              title="Account"
              to="/account"
              @click="mobileMenuOpen = false"
            />

            <v-divider class="my-2" />

            <v-list-item
              prepend-icon="mdi-book-open-variant"
              title="Documentation"
              to="/documentation"
              @click="mobileMenuOpen = false"
            />
            <v-list-item
              prepend-icon="mdi-help-circle"
              title="Support Tickets"
              to="/support-tickets"
              @click="mobileMenuOpen = false"
            />

            <!-- Admin option (only visible to admin users) -->
            <template v-if="authStore.user?.user_metadata?.role === 'admin'">
              <v-divider class="my-2" />

              <v-list-item
                prepend-icon="mdi-shield-account"
                title="Admin"
                to="/admin"
                @click="mobileMenuOpen = false"
              />
            </template>
          </template>

          <!-- Show public navigation for non-authenticated users -->
          <template v-else>
            <v-list-item
              prepend-icon="mdi-home"
              title="Home"
              @click="navigateTo('/', '#home'); mobileMenuOpen = false;"
            />
            <v-list-item
              prepend-icon="mdi-cash"
              title="Pricing"
              @click="navigateTo('/', '#pricing'); mobileMenuOpen = false;"
            />
            <v-list-item
              prepend-icon="mdi-email"
              title="Contact"
              @click="navigateTo('/', '#contact'); mobileMenuOpen = false;"
            />
          </template>
        </v-list>
      </v-navigation-drawer>

      <!-- Desktop navigation drawer -->
      <v-navigation-drawer
        v-if="authStore.isAuthenticated && !mobile && route.meta.requiresAuth"
        v-model="drawerOpen"
        :rail="rail"
        :expand-on-hover="false"
        :permanent="true"
        app
        location="left"
        :width="240"
        :rail-width="56"
        elevation="0"
        class="border-e"
      >
        <AppNavigation :rail="rail" @update:rail="rail = $event" />
      </v-navigation-drawer>

      <v-main>
        <router-view />
      </v-main>

      <!-- App Footer for protected routes (using Vuetify's app layout) -->
      <v-footer
        v-if="!isPublicPage"
        app
        color="primary"
        class="text-white"
        :height="56"
      >
        <v-container fluid class="py-0 d-flex align-center h-100">
          <v-row align="center" justify="center" no-gutters>
            <v-col cols="12" class="text-center">
              <span class="text-caption">
                © {{ new Date().getFullYear() }} MissInterview.com. All rights reserved.
              </span>
            </v-col>
          </v-row>
        </v-container>
      </v-footer>

      <!-- Regular Footer for public pages (not using app layout) -->
      <AppFooter
        v-if="isPublicPage"
        mode="full"
        :sticky="false"
        class="app-footer"
      />

      <!-- Purchase Minutes Dialog -->
      <PurchaseMinutesDialog
        v-model="showPurchaseDialog"
        :loading="purchaseLoading"
        @purchase="handlePurchase"
        @coupon-redeemed="refreshMinutesBalance"
      />
    </template>
  </v-app>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useThemeStore, availableThemes } from '@/stores/theme'
import { useMinutesStore } from '@/stores/minutes'
import { useRouter, useRoute } from 'vue-router'
import { useTheme } from 'vuetify'
import AppFooter from '@/components/AppFooter.vue'
import PurchaseMinutesDialog from '@/components/PurchaseMinutesDialog.vue'
import AppNavigation from '@/components/AppNavigation.vue'
import { useDisplay } from 'vuetify'

const authStore = useAuthStore()
const themeStore = useThemeStore()
const minutesStore = useMinutesStore()
const router = useRouter()
const route = useRoute()
const menu = ref(false)
const vuetifyTheme = useTheme()
const mobileMenuOpen = ref(false)
const showPurchaseDialog = ref(false)
const purchaseLoading = ref(false)

// Display composable for responsive design
const { mobile } = useDisplay()

// Navigation drawer state
const drawerOpen = ref(true)
const rail = ref(false)

// Load rail state from localStorage
const savedRailState = localStorage.getItem('navigation-rail-mode')
if (savedRailState !== null) {
  rail.value = JSON.parse(savedRailState)
}

// Watch rail state changes and save to localStorage
watch(rail, (newRailState) => {
  localStorage.setItem('navigation-rail-mode', JSON.stringify(newRailState))
}, { immediate: true })

// Check if we're in self-destruct mode
const isSelfDestruct = computed(() => {
  return route.query.self_destruct === 'true' && route.path.startsWith('/shared/')
})

const themeOptions = availableThemes

// Watch for changes in themeName or mode and update Vuetify
watch([
  () => themeStore.themeName,
  () => themeStore.mode
], ([newThemeName, newMode]) => {
  vuetifyTheme.global.name.value = `${newThemeName}-${newMode}`
})

function onThemeChange(newThemeName) {
  themeStore.setThemeName(newThemeName)
  vuetifyTheme.global.name.value = `${newThemeName}-${themeStore.mode}`
}

// Initialize minutes store subscription when authenticated
watch(() => authStore.user, (newUser) => {
  if (newUser) {
    console.log('Supabase User Object:', JSON.stringify(newUser, null, 2))
    minutesStore.fetchUserMinutes(newUser.id)
    minutesStore.subscribeToUserMinutes(newUser.id)
  } else {
    minutesStore.unsubscribeFromMinutes()
  }
}, { immediate: true })

// Handle URL theme parameters
onMounted(() => {
  // Check for theme parameters in URL
  const { theme, mode } = route.query

  // Apply theme name if valid
  if (theme && availableThemes.some(t => t.name === theme)) {
    themeStore.setThemeName(theme)
  }

  // Apply mode if valid
  if (mode === 'light' || mode === 'dark') {
    themeStore.setMode(mode)
  }

  // Apply theme settings
  vuetifyTheme.global.name.value = `${themeStore.themeName}-${themeStore.mode}`
})

// Determine if the current page is a public page
const isPublicPage = computed(() => {
  // List of public routes that should have the full footer
  const publicRoutes = ['/', '/reset-password']

  // Check if the current path exactly matches a public route or starts with a hash (for home page sections)
  // Login page is excluded so it gets the simple footer
  return publicRoutes.includes(route.path) || route.hash !== ''
})

// Toggle theme mode (light/dark)
function toggleMode() {
  themeStore.toggleMode()
  vuetifyTheme.global.name.value = `${themeStore.themeName}-${themeStore.mode}`
}

// Navigation function for the top nav links
function navigateTo(path, hash) {
  router.push({ path, hash })
}

async function logout() {
  menu.value = false
  const { success } = await authStore.logout()
  if (success) {
    router.push('/login')
  }
}

// Handle purchase
function handlePurchase() {
  purchaseLoading.value = true
  // Purchase in progress handled by PurchaseMinutesDialog component
  // The dialog will redirect to Stripe checkout
}

function refreshMinutesBalance() {
  minutesStore.fetchUserMinutes(authStore.user.id)
}
</script>

<style>
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.border-e {
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

/* Ensure v-main fills available space */
.v-main {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
}

.v-main__wrap {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1 0 auto;
}

/* Non-sticky header for self-destruct mode */
.non-sticky-header {
  height: 64px;
  display: flex;
  align-items: center;
  width: 100%;
  z-index: 4;
  border-radius: 0;
}

@media (max-width: 600px) and (orientation: portrait) {
  .balance-chip {
    display: none !important;
  }
}
</style>
