/**
 * plugins/vuetify.js
 *
 * Framework documentation: https://vuetifyjs.com`
 */

// Styles
import '@mdi/font/css/materialdesignicons.css'
import 'vuetify/styles'

// Composables
import { createVuetify } from 'vuetify'

// https://vuetifyjs.com/en/introduction/why-vuetify/#feature-guides
export default createVuetify({
  theme: {
    defaultTheme: 'default-light',
    variables: ['sidebar'],
    themes: {
      // Default (Rose Pink)
      'default-light': {
        colors: {
          primary: '#E091C9',    // Pink Rose
          secondary: '#F9C0D3',  // Soft Pink
          accent: '#6495ED',     // Cornflower Blue
          error: '#D32F2F',      // Darker Red
          info: '#A3C4F3',       // Periwinkle Blue
          success: '#388E3C',    // Darker Green
          warning: '#FFE4C0',    // Peach
          background: '#FFF0F5', // Lavender Blush
          surface: '#FFFFFF',    // Card/Surface
          'surface-variant': '#373737', // Dark for tooltip contrast
          sidebar: '#e0e0e0',    // Custom sidebar color
        },
      },
      'default-dark': {
        colors: {
          primary: '#CC70AA',    // Deep Pink
          secondary: '#A288E3',  // Purple
          accent: '#4682B4',     // Steel Blue
          error: '#FF5C8D',      // Strawberry
          info: '#80B4E5',       // Sky Blue
          success: '#9ED9B5',    // Sage Green
          warning: '#FFBE86',    // Apricot
          background: '#1E1E1E', // Neutral charcoal instead of plum
          surface: '#252525',    // Neutral dark gray
          'surface-variant': '#323232', // Neutral gray sidebar
          sidebar: '#181818',    // Custom sidebar color
        },
      },
      // Rose Quartz
      'roseQuartz-light': {
        colors: {
          primary: '#C94F7C',
          secondary: '#92A8D1',
          accent: '#F7786B',
          error: '#C94F7C',
          info: '#B5B9D9',
          success: '#88B04B',
          warning: '#FFD1DC',
          background: '#FFF5F7',
          surface: '#FFFFFF',
          'surface-variant': '#373737', // Dark for tooltip contrast
          sidebar: '#e0e0e0',
        },
      },
      'roseQuartz-dark': {
        colors: {
          primary: '#C94F7C',
          secondary: '#6B5B95',
          accent: '#F67280',
          error: '#FF6F91',
          info: '#6B7FD7',
          success: '#B5EAD7',
          warning: '#FFB7B2',
          background: '#1E1E1E', // Neutral charcoal
          surface: '#252525',    // Neutral dark gray
          'surface-variant': '#323232', // Neutral gray
          sidebar: '#181818',
        },
      },
      // Lavender Dream
      'lavenderDream-light': {
        colors: {
          primary: '#7E57C2',
          secondary: '#E1BEE7',
          accent: '#9575CD',
          error: '#D32F2F',
          info: '#CE93D8',
          success: '#81C784',
          warning: '#FFF59D',
          background: '#F3E5F5',
          surface: '#FFFFFF',
          'surface-variant': '#373737', // Dark for tooltip contrast
          sidebar: '#e0e0e0',
        },
      },
      'lavenderDream-dark': {
        colors: {
          primary: '#7E57C2',
          secondary: '#512DA8',
          accent: '#B388FF',
          error: '#FF5252',
          info: '#9575CD',
          success: '#A5D6A7',
          warning: '#FFD54F',
          background: '#1E1E1E', // Neutral charcoal
          surface: '#252525',    // Neutral dark gray
          'surface-variant': '#323232', // Neutral gray
          sidebar: '#181818',
        },
      },
      // Peach Blossom
      'peachBlossom-light': {
        colors: {
          primary: '#FF6F61',
          secondary: '#FFD1BA',
          accent: '#FFDAC1',
          error: '#FF6F61',
          info: '#B5EAD7',
          success: '#C7CEEA',
          warning: '#FFFACD',
          background: '#FFF5E1',
          surface: '#FFFFFF',
          'surface-variant': '#373737', // Dark for tooltip contrast
          sidebar: '#e0e0e0',
        },
      },
      'peachBlossom-dark': {
        colors: {
          primary: '#FF6F61',
          secondary: '#B56576',
          accent: '#FFB4A2',
          error: '#D7263D',
          info: '#B5EAD7',
          success: '#C7CEEA',
          warning: '#FFD6BA',
          background: '#1E1E1E', // Neutral charcoal
          surface: '#252525',    // Neutral dark gray
          'surface-variant': '#323232', // Neutral gray
          sidebar: '#181818',
        },
      },
      // Mint Cream
      'mintCream-light': {
        colors: {
          primary: '#379683',
          secondary: '#DCEDC1',
          accent: '#FFD3B6',
          error: '#FF8B94',
          info: '#B2DFDB',
          success: '#81C784',
          warning: '#FFF9C4',
          background: '#F0FFF0',
          surface: '#FFFFFF',
          'surface-variant': '#373737', // Dark for tooltip contrast
          sidebar: '#e0e0e0',
        },
      },
      'mintCream-dark': {
        colors: {
          primary: '#379683',
          secondary: '#557A95',
          accent: '#7395AE',
          error: '#FF6F61',
          info: '#B2DFDB',
          success: '#A8E6CF',
          warning: '#FFD3B6',
          background: '#1E1E1E', // Neutral charcoal
          surface: '#252525',    // Neutral dark gray
          'surface-variant': '#323232', // Neutral gray
          sidebar: '#181818',
        },
      },
      // Sunset Glow
      'sunsetGlow-light': {
        colors: {
          primary: '#FF7043',
          secondary: '#FFCC80',
          accent: '#FF7043',
          error: '#D7263D',
          info: '#FFD700',
          success: '#A3E635',
          warning: '#FFFACD',
          background: '#FFF8E1',
          surface: '#FFFFFF',
          'surface-variant': '#373737', // Dark for tooltip contrast
          sidebar: '#e0e0e0',
        },
      },
      'sunsetGlow-dark': {
        colors: {
          primary: '#FF7043',
          secondary: '#FFB347',
          accent: '#FFD700',
          error: '#D7263D',
          info: '#FFCC80',
          success: '#A3E635',
          warning: '#FFD6BA',
          background: '#1E1E1E', // Neutral charcoal
          surface: '#252525',    // Neutral dark gray
          'surface-variant': '#323232', // Neutral gray
          sidebar: '#181818',
        },
      },
      // Corporate Blue
      'corporateBlue-light': {
        colors: {
          primary: '#1976D2',
          secondary: '#90CAF9',
          accent: '#1565C0',
          error: '#D32F2F',
          info: '#64B5F6',
          success: '#388E3C',
          warning: '#FFD54F',
          background: '#F5F7FA',
          surface: '#FFFFFF',
          'surface-variant': '#373737', // Dark for tooltip contrast
          sidebar: '#e0e0e0',
        },
      },
      'corporateBlue-dark': {
        colors: {
          primary: '#1565C0',
          secondary: '#1976D2',
          accent: '#90CAF9',
          error: '#FF5252',
          info: '#64B5F6',
          success: '#388E3C',
          warning: '#FFD54F',
          background: '#1E1E1E', // Neutral charcoal
          surface: '#252525',    // Neutral dark gray
          'surface-variant': '#323232', // Neutral gray
          sidebar: '#181818',
        },
      },
      // Midnight Navy
      'midnightNavy-light': {
        colors: {
          primary: '#283593',
          secondary: '#5C6BC0',
          accent: '#8C9EFF',
          error: '#D32F2F',
          info: '#7986CB',
          success: '#43A047',
          warning: '#FFD54F',
          background: '#E8EAF6',
          surface: '#FFFFFF',
          'surface-variant': '#373737', // Dark for tooltip contrast
          sidebar: '#e0e0e0',
        },
      },
      'midnightNavy-dark': {
        colors: {
          primary: '#283593', // Lighter indigo for interactive elements
          secondary: '#5C6BC0', // Medium indigo for secondary elements
          accent: '#8C9EFF', // Bright indigo accent
          error: '#FF5252',
          info: '#8C9EFF',
          success: '#43A047',
          warning: '#FFD54F',
          background: '#121212', // Even darker background
          surface: '#0D0D0D',    // Much darker surface for higher contrast
          'surface-variant': '#1A1A1A', // Darker surface variant
          sidebar: '#080808',    // Even darker sidebar
        },
      },
      // Emerald Mist
      'emeraldMist-light': {
        colors: {
          primary: '#43A047',
          secondary: '#A5D6A7',
          accent: '#26A69A',
          error: '#D32F2F',
          info: '#80CBC4',
          success: '#388E3C',
          warning: '#FFD54F',
          background: '#E8F5E9',
          surface: '#FFFFFF',
          'surface-variant': '#373737', // Dark for tooltip contrast
          sidebar: '#e0e0e0',
        },
      },
      'emeraldMist-dark': {
        colors: {
          primary: '#388E3C',
          secondary: '#43A047',
          accent: '#26A69A',
          error: '#FF5252',
          info: '#80CBC4',
          success: '#A5D6A7',
          warning: '#FFD54F',
          background: '#1E1E1E',
          surface: '#252525',
          'surface-variant': '#323232',
          sidebar: '#181818',
        },
      },
      // Graphite Gray
      'graphiteGray-light': {
        colors: {
          primary: '#616161',
          secondary: '#BDBDBD',
          accent: '#757575',
          error: '#D32F2F',
          info: '#90A4AE',
          success: '#388E3C',
          warning: '#FFD54F',
          background: '#F5F5F5',
          surface: '#FFFFFF',
          'surface-variant': '#373737', // Dark for tooltip contrast
          sidebar: '#e0e0e0',
        },
      },
      'graphiteGray-dark': {
        colors: {
          primary: '#616161', // Medium gray for interactive elements
          secondary: '#757575', // Darker gray for secondary elements
          accent: '#BDBDBD', // Light gray accent
          error: '#FF5252',
          info: '#90A4AE',
          success: '#388E3C',
          warning: '#FFD54F',
          background: '#1E1E1E', // Neutral charcoal
          surface: '#252525', // Neutral dark gray
          'surface-variant': '#323232', // Neutral gray sidebar
          sidebar: '#181818',
        },
      },
      // Onyx Night
      'onyxNight-light': {
        colors: {
          primary: '#212121', // Onyx
          secondary: '#757575', // Grey
          accent: '#00B8D4', // Cyan accent
          error: '#D32F2F',
          info: '#90CAF9',
          success: '#388E3C',
          warning: '#FFD54F',
          background: '#F5F5F5', // Soft light grey
          surface: '#FFFFFF',
          'surface-variant': '#373737', // Dark for tooltip contrast
          sidebar: '#E0E0E0',
        },
      },
      'onyxNight-dark': {
        colors: {
          primary: '#757575', // Darker gray for interactive elements
          secondary: '#616161', // Adjusted secondary to be darker
          accent: '#00B8D4', // Cyan accent
          error: '#FF5252',
          info: '#90CAF9',
          success: '#81C784',
          warning: '#FFD54F',
          background: '#1E1E1E', // Neutral charcoal
          surface: '#252525',    // Neutral dark gray
          'surface-variant': '#323232', // Neutral gray sidebar
          sidebar: '#181818',    // Sidebar dark grey
        },
      },
    },
  },
})
