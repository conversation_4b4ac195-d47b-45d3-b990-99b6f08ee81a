import { router } from '@/router';
import { setupLayouts } from 'virtual:generated-layouts';

// Debug function to log route information
function logRoutes(message) {
  console.group(`ROUTER DEBUG: ${message}`);
  console.log('Current routes:', router.getRoutes().map(r => ({
    name: r.name,
    path: r.path,
    meta: r.meta
  })));
  console.groupEnd();
}

export function fixRoutes() {
  console.log('🔄 Running fixRoutes function to set up routes');
  logRoutes('Before removing routes');

  // Remove existing routes that might conflict
  const existingRoutes = router.getRoutes()
  existingRoutes.forEach(route => {
    if (route.name === 'admin-user-edit' ||
        route.path === '/admin/user/:id' ||
        route.path.includes('/admin/user/') ||
        route.name === 'support-tickets-id' ||
        route.path === '/support-tickets/:id') {
      router.removeRoute(route.name)
      console.log('🗑️ Removed existing route:', route.path, route.name);
    }
  })

  // Add the interview/[id] route explicitly
  try {
    router.removeRoute('interview-id');
    console.log('🗑️ Removed existing interview route');
  } catch {
    // Route might not exist yet, that's fine
    console.log('📝 No existing interview route to remove');
  }

  // Create the route with layout metadata
  const interviewRoute = {
    name: 'interview-id',
    path: '/interview/:id',
    component: () => import('@/pages/interview/[id].vue'),
    meta: {
      requiresAuth: true,
      layout: 'default'
    }
  };

  // Apply the layout and add the route
  router.addRoute(setupLayouts([interviewRoute])[0]);
  console.log('✅ Added interview route');

  // Add the support tickets/[id] route explicitly
  try {
    router.removeRoute('support-tickets-id');
    console.log('🗑️ Removed existing support tickets route');
  } catch {
    // Route might not exist yet, that's fine
    console.log('📝 No existing support tickets route to remove');
  }

  // Create the support tickets route with layout metadata
  const supportTicketsRoute = {
    name: 'support-tickets-id',
    path: '/support-tickets/:id',
    component: () => import('@/pages/support-tickets/[id].vue'),
    props: true,
    meta: {
      requiresAuth: true,
      layout: 'default'
    }
  };

  // Apply the layout and add the route
  router.addRoute(setupLayouts([supportTicketsRoute])[0]);
  console.log('✅ Added support tickets route');

  // Add the admin user edit route explicitly with higher priority
  // First remove any existing route with this name to avoid conflicts
  try {
    router.removeRoute('admin-user-edit');
    console.log('🗑️ Removed existing admin-user-edit route');
  } catch {
    // Route might not exist yet, that's fine
    console.log('📝 No existing admin-user-edit route to remove');
  }

  // Create the route with layout metadata
  const adminUserEditRoute = {
    name: 'admin-user-edit',
    path: '/admin/user/:id',
    component: () => import('@/pages/admin/user/edit_user.vue'),
    props: route => ({
      id: route.params.id,
      // Send any state data as props
      userObject: route.state?.userObject
    }),
    meta: {
      requiresAuth: true,
      requiresAdmin: true,
      layout: 'default'
    }
  };

  // Apply the layout and add the route
  router.addRoute(setupLayouts([adminUserEditRoute])[0]);
  console.log('✅ Added admin-user-edit route with props:', adminUserEditRoute);

  // Add a catch-all route for admin/user/* paths to ensure it's handled
  router.addRoute({
    name: 'admin-user-catch-all',
    path: '/admin/user/*',
    redirect: to => {
      const id = to.path.split('/').pop()
      console.log('🔄 Redirecting from catch-all to specific user:', id)
      return { path: `/admin/user/${id}` }
    }
  });
  console.log('✅ Added admin user catch-all route');

  logRoutes('After fixing routes');
}
