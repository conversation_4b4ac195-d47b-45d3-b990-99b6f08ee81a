/**
 * plugins/index.js
 *
 * Automatically included in `./src/main.js`
 */

// Plugins
import vuetify from './vuetify'
import pinia from '@/stores'
import router from '@/router'
import { supabase } from './supabase'
import { fixRoutes } from './router-fix'
import { createGtag } from "vue-gtag"

// Make supabase available globally
window.supabase = supabase

// Fix routes immediately
console.log('Fixing routes immediately during plugin initialization')
fixRoutes()

export function registerPlugins(app) {
  app
    .use(vuetify)
    .use(pinia)
    .use(router)
    .use(createGtag({
      tagId: "G-Q42MC6GGQ5"
    }))
}

// Also run the router fix after router is ready to ensure all routes are properly registered
router.isReady().then(() => {
  console.log('Router is ready, fixing routes again to ensure everything is registered')
  fixRoutes()
  console.log('Routes fixed')
})
