/**
 * src/styles/settings.scss
 *
 * Configures SASS variables and Vuetify overwrites
 */

// https://vuetifyjs.com/features/sass-variables/`
// @use 'vuetify/settings' with (
//   $color-pack: false
// );

// Import Google Fonts
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500&display=swap');

// Global typography styles
.v-application {
  // Apply Playfair Display to all headings
  .text-h1,
  .text-h2,
  .text-h3,
  .text-h4,
  .text-h5,
  .text-h6,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Playfair Display', Georgia, 'Times New Roman', serif !important;
    letter-spacing: -0.5px;
  }

  .text-h1,
  .text-h2,
  h1,
  h2 {
    line-height: 1.2;
  }

  .font-italic {
    font-style: italic !important;
    letter-spacing: 0.5px;
  }
}
