<template>
  <div>
    <v-dialog
      v-model="dialogModel"
      fullscreen
      persistent
      :scrim="false"
      class="mobile-fullscreen-dialog"
    >
      <VoiceAssistant @close="closeDialog" />
    </v-dialog>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import VoiceAssistant from '@/components/VoiceAssistant.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogModel = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

function closeDialog() {
  dialogModel.value = false
}
</script>

<style scoped>
/* Ensure dialog is truly fullscreen on mobile */
:deep(.mobile-fullscreen-dialog) {
  margin: 0 !important;
  padding: 0 !important;
  height: 100% !important;
  width: 100% !important;
  max-width: 100% !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

:deep(.mobile-fullscreen-dialog .v-overlay__content) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

:deep(.mobile-fullscreen-dialog .v-card) {
  height: 100vh !important;
  width: 100% !important;
  max-width: 100% !important;
  border-radius: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}
</style>
