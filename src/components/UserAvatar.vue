<template>
  <v-avatar :size="size" :color="color">
    <v-img
      v-if="avatarUrl"
      :src="avatarUrl"
      :alt="initials"
    />
    <span v-else>{{ initials }}</span>
  </v-avatar>
</template>

<script setup>
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

const props = defineProps({
  user: {
    type: Object,
    default: null
  },
  size: {
    type: [String, Number],
    default: 32
  },
  color: {
    type: String,
    default: 'primary'
  }
})

const authStore = useAuthStore()

const avatarUrl = computed(() => {
  if (!props.user) return null
  return authStore.getUserAvatarFromData(props.user)
})

const initials = computed(() => {
  if (!props.user) return '?'
  return authStore.getUserInitials(props.user)
})
</script>
