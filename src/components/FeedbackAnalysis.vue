<template>
  <div class="feedback-analysis">
    <h3 class="text-h6 mb-3 d-flex align-center">
      Feedback
      <v-tooltip
        location="top"
        text="Detailed feedback on your interview performance across multiple categories."
        max-width="300"
      >
        <template #activator="{ props }">
          <v-icon
            v-bind="props"
            icon="mdi-information-outline"
            size="small"
            class="ml-2"
          />
        </template>
      </v-tooltip>
    </h3>

    <!-- Loading state -->
    <v-card
      v-if="loading"
      variant="tonal"
      class="pa-3 text-center"
      bg-color="surface"
    >
      <v-progress-circular
        indeterminate
        color="primary"
        class="mb-3"
      />
      <p>Feedback is being generated...</p>
    </v-card>

    <!-- No data state -->
    <v-card
      v-else-if="!gradingData"
      variant="tonal"
      class="pa-3 text-center"
      bg-color="surface"
    >
      <p>No feedback available for this interview.</p>
    </v-card>

    <!-- Data available state -->
    <v-card
      v-else
      variant="tonal"
      class="pa-4"
      bg-color="surface"
    >
      <!-- Overall score card -->
      <v-card
        class="mb-4 pa-4"
        :color="getScoreColor(gradingData.overall.score, 'bg')"
      >
        <div class="d-flex align-center flex-wrap">
          <div class="score-container text-center me-4 mb-3 mb-sm-0">
            <div
              class="score-circle d-flex align-center justify-center mb-1 mx-auto"
              :class="getScoreColor(gradingData.overall.score, 'circle')"
            >
              <span class="text-h4 font-weight-bold">{{ gradingData.overall.score }}</span>
            </div>
            <span class="text-caption font-weight-medium">{{ getScoreLabel(gradingData.overall.score) }}</span>
          </div>

          <div class="performance-text flex-grow-1">
            <h4 class="text-subtitle-1 font-weight-bold mb-2">
              Overall Performance
            </h4>
            <div
              v-if="gradingData.overall.positive"
              class="positive-feedback mb-2"
            >
              <div class="d-flex">
                <v-icon
                  icon="mdi-thumb-up"
                  color="success"
                  class="me-2 mt-1"
                  size="small"
                />
                <span class="text-body-2">{{ gradingData.overall.positive }}</span>
              </div>
            </div>
            <div
              v-if="gradingData.overall.opportunities"
              class="improvement-feedback"
            >
              <div class="d-flex">
                <v-icon
                  icon="mdi-lightbulb"
                  color="warning"
                  class="me-2 mt-1"
                  size="small"
                />
                <span class="text-body-2">{{ gradingData.overall.opportunities }}</span>
              </div>
            </div>
          </div>
        </div>
      </v-card>

      <!-- Categories breakdown -->
      <h4 class="text-subtitle-1 font-weight-bold mb-4">
        Category Breakdown
      </h4>

      <div
        v-if="!categoryNames.length"
        class="text-center py-3"
      >
        <p class="text-body-1">
          No category feedback available.
        </p>
      </div>

      <v-expansion-panels
        v-else
        v-model="openPanels"
        variant="accordion"
        class="mb-4"
        multiple
      >
        <v-expansion-panel
          v-for="(category, index) in categoryNames"
          :key="category"
          :value="index"
          class="mb-3 category-panel"
        >
          <v-expansion-panel-title class="category-title">
            <div class="d-flex align-center justify-space-between w-100">
              <span class="text-subtitle-1 font-weight-bold">
                {{ category }}
              </span>
              <div class="d-flex align-center">
                <v-rating
                  :model-value="getCategoryScore(category) / 2"
                  readonly
                  color="amber"
                  empty-icon="$mdiStarOutline"
                  empty-icon-color="transparent"
                  half-increments
                  density="compact"
                  size="small"
                />
                <span
                  class="ms-2 text-caption font-weight-bold"
                  :class="getScoreColor(getCategoryScore(category), 'text')"
                >
                  {{ getCategoryScore(category) }}/10
                </span>
              </div>
            </div>
          </v-expansion-panel-title>
          <v-divider class="mb-3" />
          <v-expansion-panel-text>
            <div
              v-if="getCategoryFeedback(category, 'positive')"
              class="positive-feedback mb-3"
            >
              <div class="d-flex">
                <v-icon
                  icon="mdi-thumb-up"
                  color="success"
                  class="me-2 mt-1"
                  size="small"
                />
                <div>
                  <div class="text-subtitle-2 font-weight-medium mb-1">
                    Strengths
                  </div>
                  <span class="text-body-2">{{ getCategoryFeedback(category, 'positive') }}</span>
                </div>
              </div>
            </div>
            <div
              v-if="getCategoryFeedback(category, 'opportunities')"
              class="improvement-feedback"
            >
              <div class="d-flex">
                <v-icon
                  icon="mdi-lightbulb"
                  color="warning"
                  class="me-2 mt-1"
                  size="small"
                />
                <div>
                  <div class="text-subtitle-2 font-weight-medium mb-1">
                    Opportunities
                  </div>
                  <span class="text-body-2">{{ getCategoryFeedback(category, 'opportunities') }}</span>
                </div>
              </div>
            </div>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>

      <!-- Category score visualization -->
      <h4 class="text-subtitle-1 font-weight-bold mb-3">
        Performance By Category
      </h4>

      <div v-if="categoryNames.length">
        <div
          v-for="category in categoryNames"
          :key="`score-${category}`"
          class="mb-3"
        >
          <div class="d-flex justify-space-between align-center mb-1">
            <span class="text-body-2 font-weight-medium">
              {{ category }}
            </span>
            <span class="text-body-2">
              {{ getCategoryScore(category) }}/10
            </span>
          </div>
          <v-progress-linear
            :model-value="(getCategoryScore(category) / 10) * 100"
            height="12"
            rounded
            :color="getScoreBarColor(getCategoryScore(category))"
          >
            <template #default>
              <span class="text-caption text-white">{{ getCategoryScore(category) }}</span>
            </template>
          </v-progress-linear>
        </div>
      </div>

      <!-- Tips section -->
      <div class="mt-6">
        <h4 class="text-subtitle-1 font-weight-bold mb-2">
          Tips for Improvement
        </h4>
        <ul class="pl-4">
          <li class="mb-1">
            Review your lowest-scoring categories for the biggest opportunities to improve
          </li>
          <li class="mb-1">
            Practice with feedback in mind - focus on one area at a time
          </li>
          <li class="mb-1">
            Record yourself and review against the feedback provided
          </li>
          <li class="mb-1">
            Consider working with a coach on your specific development areas
          </li>
        </ul>
      </div>
    </v-card>
  </div>
</template>

<script setup>
import { computed, toRefs, ref, watch } from 'vue'

const props = defineProps({
  analysis: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const { analysis, loading } = toRefs(props)

// Extract grading data from analysis
const gradingData = computed(() => {
  if (!analysis.value || !analysis.value.grading) {
    return null
  }
  return analysis.value.grading
})

// Get all category names
const categoryNames = computed(() => {
  if (!gradingData.value || !gradingData.value.categories) {
    return []
  }
  return Object.keys(gradingData.value.categories)
})

// Initialize panels as open, but allow toggling
const openPanels = ref([])

// Initialize all panels as open when categoryNames changes
const initOpenPanels = () => {
  openPanels.value = categoryNames.value.map((_, index) => index)
}

// Watch for changes to initialize panels when data is loaded
watch(categoryNames, () => {
  if (categoryNames.value.length > 0) {
    initOpenPanels()
  }
}, { immediate: true })

// Get score for a specific category
function getCategoryScore(category) {
  if (!gradingData.value || !gradingData.value.categories || !gradingData.value.categories[category]) {
    return 0
  }
  return gradingData.value.categories[category].score
}

// Get feedback for a specific category
function getCategoryFeedback(category, feedbackType) {
  if (!gradingData.value || !gradingData.value.categories || !gradingData.value.categories[category]) {
    return ''
  }
  return gradingData.value.categories[category].feedback[feedbackType]
}

// Get color based on score
function getScoreColor(score, type) {
  if (score >= 8) {
    return type === 'circle' ? 'bg-success' :
           type === 'bg' ? 'success-lighten-5' :
           'text-success'
  }
  if (score >= 6) {
    return type === 'circle' ? 'bg-info' :
           type === 'bg' ? 'info-lighten-5' :
           'text-info'
  }
  if (score >= 4) {
    return type === 'circle' ? 'bg-warning' :
           type === 'bg' ? 'warning-lighten-5' :
           'text-warning'
  }
  return type === 'circle' ? 'bg-error' :
         type === 'bg' ? 'error-lighten-5' :
         'text-error'
}

// Get bar color based on score
function getScoreBarColor(score) {
  if (score >= 8) return 'success'
  if (score >= 6) return 'info'
  if (score >= 4) return 'warning'
  return 'error'
}

// Get label based on score
function getScoreLabel(score) {
  if (score >= 9) return 'Excellent!'
  if (score >= 8) return 'Very Good!'
  if (score >= 7) return 'Good!'
  if (score >= 6) return 'Solid!'
  if (score >= 5) return 'Average'
  if (score >= 4) return 'Needs Work'
  if (score >= 3) return 'Improvement Needed'
  return 'Significant Work Needed'
}
</script>

<style scoped>
.feedback-analysis {
  margin-bottom: 24px;
}

.score-circle {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  color: white;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
}

.bg-success {
  background-color: var(--v-theme-success);
}

.bg-info {
  background-color: var(--v-theme-info);
}

.bg-warning {
  background-color: var(--v-theme-warning);
}

.bg-error {
  background-color: var(--v-theme-error);
}

.bg-grey {
  background-color: var(--v-theme-secondary);
}

.score-container {
  min-width: 80px;
}

.performance-text {
  border-left: 1px solid rgba(var(--v-theme-on-surface), 0.12);
  padding-left: 24px;
}

.category-panel {
  border: 1px solid rgba(var(--v-theme-primary), 0.1);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.category-panel:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-title {
  background-color: rgba(var(--v-theme-primary), 0.03);
}

@media (max-width: 600px) {
  .performance-text {
    border-left: none;
    padding-left: 0;
    margin-top: 16px;
  }

  .score-container {
    margin-bottom: 16px;
  }
}
</style>
