<template>
  <v-footer
    :class="['bg-primary', 'text-white', mode === 'simple' ? 'footer-simple' : '', sticky ? 'footer-sticky' : '']">
    <v-container
      fluid
      class="mx-0 px-4"
    >
      <v-row
        v-if="mode === 'full'"
        class="my-6"
      >
        <v-col
          cols="12"
          md="4"
        >
          <h3 class="text-h6 mb-4">
            MissInterview
          </h3>
          <p class="text-body-2 mb-4">
            The AI-powered voice coach that helps pageant contestants perfect their interview skills and win with confidence.
          </p>
          <div class="d-flex">
            <v-btn
              icon="mdi-facebook"
              variant="text"
              color="white"
              class="mr-2"
            />
            <v-btn
              icon="mdi-twitter"
              variant="text"
              color="white"
              class="mr-2"
            />
            <v-btn
              icon="mdi-instagram"
              variant="text"
              color="white"
              class="mr-2"
            />
            <v-btn
              icon="mdi-linkedin"
              variant="text"
              color="white"
            />
          </div>
        </v-col>

        <v-col
          cols="12"
          md="2"
        >
          <h3 class="text-h6 mb-4">
            Quick Links
          </h3>
          <v-list class="transparent-list pa-0">
            <v-list-item
              :to="{ path: '/', hash: '#home' }"
              class="pa-0 mb-2"
              title="Home"
              density="compact"
              color="white"
            />
            <v-list-item
              :to="{ path: '/', hash: '#about' }"
              class="pa-0 mb-2"
              title="About Us"
              density="compact"
              color="white"
            />
            <v-list-item
              :to="{ path: '/', hash: '#services' }"
              class="pa-0 mb-2"
              title="Services"
              density="compact"
              color="white"
            />
            <v-list-item
              :to="{ path: '/', hash: '#contact' }"
              class="pa-0 mb-2"
              title="Contact"
              density="compact"
              color="white"
            />
          </v-list>
        </v-col>

        <v-col
          cols="12"
          md="2"
        >
          <h3 class="text-h6 mb-4">
            Services
          </h3>
          <v-list class="transparent-list pa-0">
            <v-list-item
              :to="{ path: '/', hash: '#practice' }"
              class="pa-0 mb-2"
              title="Practice Sessions"
              density="compact"
              color="white"
            />
            <v-list-item
              :to="{ path: '/', hash: '#coaching' }"
              class="pa-0 mb-2"
              title="Private Coaching"
              density="compact"
              color="white"
            />
            <v-list-item
              :to="{ path: '/', hash: '#workshops' }"
              class="pa-0 mb-2"
              title="Group Workshops"
              density="compact"
              color="white"
            />
            <v-list-item
              :to="{ path: '/', hash: '#mental' }"
              class="pa-0 mb-2"
              title="Mental Preparation"
              density="compact"
              color="white"
            />
          </v-list>
        </v-col>

        <v-col
          cols="12"
          md="4"
        >
          <h3 class="text-h6 mb-4">
            Subscribe to our newsletter
          </h3>
          <p class="text-body-2 mb-4">
            Get the latest pageant interview tips and exclusive offers directly to your inbox.
          </p>
          <v-row>
            <v-col cols="8">
              <v-text-field
                density="compact"
                placeholder="Your email"
                bg-color="white"
                flat
                single-line
                hide-details
              />
            </v-col>
            <v-col cols="4">
              <v-btn
                color="black"
                class="text-white"
                height="40"
              >
                Subscribe
              </v-btn>
            </v-col>
          </v-row>
        </v-col>
      </v-row>

      <v-divider
        v-if="mode === 'full'"
        color="white"
        class="mb-2"
      />

      <v-row
        class="py-0"
        :class="{ 'pt-2': mode === 'full' }"
        dense
        no-gutters
      >
        <v-col
          cols="12"
          sm="6"
          class="text-center text-sm-start py-0"
          :order="2"
          order-sm="1"
        >
          <div class="text-caption">
            &copy; {{ (new Date()).getFullYear() }} MissInterview.com. All rights reserved.
          </div>
        </v-col>
        <v-col
          cols="12"
          sm="6"
          class="text-center text-sm-end py-0"
          :order="1"
          order-sm="2"
        >
          <div class="d-flex flex-row justify-center justify-sm-end white-space-nowrap">
            <router-link
              to="/privacy-policy"
              class="text-decoration-none text-white text-caption mx-2"
            >
              Privacy Policy
            </router-link>
            <router-link
              to="/terms-of-service"
              class="text-decoration-none text-white text-caption mx-2"
            >
              Terms of Service
            </router-link>
            <router-link
              to="/data-deletion"
              class="text-decoration-none text-white text-caption mx-2"
            >
              Data Deletion
            </router-link>
            <router-link
              :to="{ path: '/', hash: '#cookies' }"
              class="text-decoration-none text-white text-caption mx-2"
            >
              Cookie Policy
            </router-link>
          </div>
        </v-col>
      </v-row>
    </v-container>
  </v-footer>
</template>

<script setup>
defineProps({
  mode: {
    type: String,
    default: 'full',
    validator: (value) => ['full', 'simple'].includes(value)
  },
  sticky: {
    type: Boolean,
    default: true
  }
});
</script>

<style scoped>
.transparent-list {
  background-color: transparent !important;
}

.transparent-list :deep(.v-list-item) {
  min-height: 32px;
}

.transparent-list :deep(.v-list-item__overlay) {
  display: none;
}

.transparent-list :deep(.v-list-item-title) {
  color: white;
  font-size: 0.875rem;
}

.footer-simple {
  max-height: 56px;
  min-height: 40px;
  height: auto;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  display: flex;
  align-items: center;
}

.footer-simple .v-container {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.footer-sticky {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 1000 !important;
  margin: 0 !important;
}

.footer-non-sticky {
  position: relative !important;
  bottom: auto !important;
  margin: 0 !important;
}
</style>
