<template>
  <div class="openai-voice-assistant">
    <v-select
      v-if="availableAudioDevices.length > 1 && !isActive"
      v-model="selectedAudioDeviceId"
      :items="availableAudioDevices"
      item-title="label"
      item-value="deviceId"
      label="Select Microphone"
      variant="outlined"
      density="compact"
      class="mb-3 mx-auto"
      style="max-width: 300px;"
      hide-details
    />

    <div class="d-flex align-center justify-center">
      <v-btn
        icon
        :color="isActive ? 'red' : 'success'"
        size="large"
        class="pulse-animation"
        :disabled="isProcessing"
        @click="toggleAssistant"
      >
        <v-icon size="32">
          {{ isActive ? 'mdi-microphone' : 'mdi-microphone-outline' }}
        </v-icon>
        <v-tooltip
          activator="parent"
          location="top"
        >
          {{ isActive ? 'Stop OpenAI Voice Assistant' : 'Start OpenAI Voice Assistant' }}
        </v-tooltip>
      </v-btn>

      <v-btn
        v-if="isConversationActive"
        icon
        color="error"
        size="large"
        class="ml-3"
        :disabled="isProcessing"
        @click="stopConversation"
      >
        <v-icon size="32">
          mdi-stop-circle
        </v-icon>
        <v-tooltip
          activator="parent"
          location="top"
        >
          Stop Conversation
        </v-tooltip>
      </v-btn>
    </div>

    <div
      v-if="isProcessing"
      class="mt-2 text-center"
    >
      <v-progress-circular
        indeterminate
        color="success"
        size="24"
      />
      <div class="text-caption mt-1">
        Processing...
      </div>
    </div>

    <v-alert
      v-if="transcript"
      color="primary"
      variant="tonal"
      class="mt-3 width-100"
      title="You said:"
      density="comfortable"
      border="start"
    >
      {{ transcript }}
    </v-alert>

    <v-alert
      v-if="response"
      color="success"
      variant="tonal"
      class="mt-3 width-100"
      title="Assistant:"
      density="comfortable"
      border="start"
    >
      {{ response }}
    </v-alert>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useAuthStore } from '@/stores/auth';

// Define props - add interviewId
const props = defineProps({
  apiKey: {
    type: String,
    required: true
  },
  systemPrompt: {
    type: String,
    default: 'You are a helpful assistant.'
  },
  interviewId: {
    type: String,
    default: ''
  }
});

// Get auth store for user ID
const authStore = useAuthStore();

// State variables
const isActive = ref(false);
const isProcessing = ref(false);
const isConversationActive = ref(false); // New state to track ongoing conversation
const transcript = ref('');
const response = ref('');
const availableAudioDevices = ref([]);
const selectedAudioDeviceId = ref(''); // Stores the selected device ID
let silenceCheckCounter = 0;
let audioContext = null;
let mediaStream = null;
let mediaRecorder = null;
let audioChunks = [];
let analyser = null; // For silence detection
let audioDataArray = null; // For silence detection
let animationFrameId = null; // For silence detection loop
let silenceStartTime = null; // For silence detection
const SILENCE_THRESHOLD = 10; // Adjust based on testing (values from 0-255 for frequency data)
const REQUIRED_SILENCE_DURATION = 2000; // 2 seconds in milliseconds (changed from 5000)
const LOCALSTORAGE_MICROPHONE_KEY = 'openai-voice-assistant-selected-microphone';

// Initialize audio context on component mount
onMounted(async () => {
  initializeAudioContext();

  // Get initial permissions and list devices
  try {
    // Request permission early to potentially get device labels
    const tempStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    tempStream.getTracks().forEach(track => track.stop()); // Stop the temporary stream
    await getAudioInputDevices();
  } catch (err) {
    console.error('Error getting initial media permissions or devices:', err);
    // Handle lack of permission, e.g., show a message
    response.value = 'Error: Could not access microphone. Please grant permission.';
  }

  // Listen for device changes
  navigator.mediaDevices.addEventListener('devicechange', getAudioInputDevices);
});

// Watch for changes to selectedAudioDeviceId and save to localStorage
watch(selectedAudioDeviceId, (newDeviceId) => {
  if (newDeviceId) {
    console.log('Saving selected microphone to localStorage:', newDeviceId);
    localStorage.setItem(LOCALSTORAGE_MICROPHONE_KEY, newDeviceId);
  }
});

// Clean up on component unmount
onUnmounted(() => {
  stopRecording(); // stopRecording also cancels the animation frame
  if (audioContext && audioContext.state !== 'closed') {
    audioContext.close();
  }
  // No need to clear interval here anymore
  navigator.mediaDevices.removeEventListener('devicechange', getAudioInputDevices);
});

// Initialize audio context
function initializeAudioContext() {
  try {
    audioContext = new (window.AudioContext || window.webkitAudioContext)();
  } catch (error) {
    console.error('Error initializing AudioContext:', error);
  }
}

// Start recording audio
async function startRecording() {
  if (!audioContext) {
    initializeAudioContext();
  }

  try {
    isProcessing.value = true;

    // Check if getUserMedia is supported
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      throw new Error('Your browser does not support audio recording. Please try a different browser.');
    }

    // Request microphone access using the selected device
    console.log(`Requesting microphone access for device: ${selectedAudioDeviceId.value || 'default'}...`);
    const constraints = {
        audio: selectedAudioDeviceId.value
            ? { deviceId: { exact: selectedAudioDeviceId.value } }
            : true // Fallback to default device if none selected
    };
    mediaStream = await navigator.mediaDevices.getUserMedia(constraints);

    // Create media recorder
    mediaRecorder = new MediaRecorder(mediaStream);
    audioChunks = [];

    // --- Silence Detection Setup ---
    if (audioContext) {
      console.log('startRecording: AudioContext state before analyser setup:', audioContext.state); // <-- ADD LOG
      const source = audioContext.createMediaStreamSource(mediaStream);
      analyser = audioContext.createAnalyser();
      analyser.fftSize = 2048; // Standard FFT size
      const bufferLength = analyser.frequencyBinCount;
      audioDataArray = new Uint8Array(bufferLength);
      source.connect(analyser);
      // Note: We don't connect the analyser to the destination,
      // as we only want to analyze, not playback the input.
    } else {
        console.warn('AudioContext not available, silence detection disabled.');
        analyser = null; // Ensure analyser is null if context failed
    }

    // Reset silence timer state
    silenceStartTime = null;
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }
    // --- End Silence Detection Setup ---

    // Set up event handlers
    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunks.push(event.data);
      }
    };

    mediaRecorder.onstop = async () => {
      try {
        // Process the recorded audio only if we have chunks
        if (audioChunks.length > 0) {
          const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
          await processAudio(audioBlob);
        } else {
          console.warn('No audio data recorded');
          isProcessing.value = false;
        }
      } catch (error) {
        console.error('Error processing recording:', error);
        isProcessing.value = false;
      }
    };

    // Start recording
    mediaRecorder.start(1000); // Collect data in 1-second chunks
    isActive.value = true;
    isProcessing.value = false;

    // Start checking for silence if analyser is ready
    console.log('Checking if analyser is ready before starting silence detection:', analyser);
    if (analyser) {
        startSilenceCheckLoop();
    }

  } catch (error) {
    console.error('Error starting recording:', error);
    isProcessing.value = false;

    // Show error message to user
    if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
      response.value = 'Error: Microphone access was denied. Please allow microphone access to use the voice assistant.';
    } else {
      response.value = `Error: ${error.message}`;
    }

    if (mediaStream) {
      mediaStream.getTracks().forEach(track => track.stop());
      mediaStream = null;
    }

    // Clean up silence detection - MOVED INTO CATCH BLOCK
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
      console.log('startRecording (error): Cancelled animation frame');
    }
    silenceStartTime = null;
    if (analyser) {
      // Analyser disconnects automatically when the source track stops.
      analyser = null;
    }
  }
}

// Stop recording audio
function stopRecording() {
  if (mediaRecorder && mediaRecorder.state !== 'inactive') {
    mediaRecorder.stop();
    isActive.value = false;
  }

  if (mediaStream) {
    mediaStream.getTracks().forEach(track => track.stop());
    mediaStream = null;
  }

  console.log('stopRecording: Entered function');

  // Clean up silence detection
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
    console.log('stopRecording: Cancelled animation frame');
  }
  silenceStartTime = null;
  if (analyser) {
    // Analyser disconnects automatically when the source track stops.
    analyser = null;
  }
}

// Start the silence detection loop using requestAnimationFrame
function startSilenceCheckLoop() {
  console.log('startSilenceCheckLoop: Starting requestAnimationFrame loop');
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId); // Cancel previous loop if any
  }
  silenceCheckCounter = 0; // Reset counter for new recording
  silenceStartTime = null; // Reset silence start time
  animationFrameId = requestAnimationFrame(checkSilenceLoop);
}

// Analyse audio levels for silence (called via requestAnimationFrame)
function checkSilenceLoop() {
  silenceCheckCounter++;
  console.log(`checkSilenceLoop: [${silenceCheckCounter}] checking... AudioContext state: ${audioContext?.state}`);

  // Guard: Stop loop if analyser gone or recording stopped
  if (!analyser || !isActive.value) {
    console.log(`checkSilenceLoop: [${silenceCheckCounter}] analyser or isActive is false. Analyser: ${!!analyser}, isActive: ${isActive.value}. Stopping loop.`);
    if (animationFrameId) { // Ensure we clear the ID if we stop the loop here
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
    }
    return;
  }

  console.log('checkSilenceLoop: Actively checking audio level...');

  analyser.getByteFrequencyData(audioDataArray);
  let sum = 0;
  for (let i = 0; i < audioDataArray.length; i++) {
    sum += audioDataArray[i];
  }
  const average = sum / audioDataArray.length;

  console.log('Average audio level:', average);

  if (average < SILENCE_THRESHOLD) {
    if (silenceStartTime === null) {
      silenceStartTime = Date.now();
      console.log('Silence started at:', silenceStartTime);
    } else {
      const silenceDuration = Date.now() - silenceStartTime;
      console.log('Current silence duration:', silenceDuration);
      if (silenceDuration >= REQUIRED_SILENCE_DURATION) {
        console.log('Silence detected for required duration. Stopping recording.');
        stopRecording(); // Stop recording and process audio
        // No need to request next frame, stopRecording cancels it
        return; // Exit loop
      }
    }
  } else {
    if (silenceStartTime !== null) {
        console.log('Sound detected, resetting silence timer.');
        silenceStartTime = null;
    }
  }

  // Request the next frame to continue the loop if still active
  if (isActive.value) { // Double check isActive before scheduling next frame
      animationFrameId = requestAnimationFrame(checkSilenceLoop);
  }
}

// Process the recorded audio with OpenAI API
async function processAudio(blob) {
  // Validate input parameter
  console.log('Processing audio blob:', blob);
  if (!blob || !(blob instanceof Blob) || blob.size === 0) {
    console.error('Invalid audio blob:', blob);
    throw new Error('Empty or invalid audio blob');
  }

  isProcessing.value = true;

  try {
    // Create form data with the audio blob
    console.log('Preparing form data for audio chat endpoint...');
    const formData = new FormData();
    formData.append('file', blob, 'recording.webm');

    // Add the user ID from the auth store if authenticated
    if (authStore.user) {
      formData.append('userId', authStore.user.id);
      console.log('Adding userId to request:', authStore.user.id);
    } else {
      console.warn('No authenticated user found, request will be sent without userId');
    }

    // Add interview ID if available
    if (props.interviewId) {
      formData.append('interviewId', props.interviewId);
      console.log('Adding interviewId to request:', props.interviewId);
    }

    // Send audio to n8n workflow endpoint
    console.log('Sending audio to n8n workflow endpoint...');
    const response_endpoint = 'https://automation.aiedgemedia.com/webhook/audio-chat';
    let audioResponse;

    try {
      audioResponse = await fetch(response_endpoint, {
        method: 'POST',
        body: formData
      });

      console.log('Audio chat response status:', audioResponse.status);

      if (!audioResponse.ok) {
        const errorText = await audioResponse.text();
        throw new Error(`Audio chat failed: ${audioResponse.status} ${audioResponse.statusText}\n${errorText}`);
      }
    } catch (error) {
      console.error('Audio chat API error:', error);
      throw new Error(`Audio processing failed: ${error.message}`);
    }

    // Get the audio response
    try {
      const audioBlob = await audioResponse.blob();
      console.log('Audio response blob received, size:', audioBlob.size);

      if (audioBlob.size === 0) {
        throw new Error('Received empty audio from API');
      }

      // Extract transcript and response from headers if available, or set to default values
      const transcriptHeader = audioResponse.headers.get('X-Transcript') || 'Transcription not available';
      const responseHeader = audioResponse.headers.get('X-Response') || 'Response not available';

      transcript.value = transcriptHeader;
      response.value = responseHeader;

      const audioUrl = URL.createObjectURL(audioBlob);
      const audio = new Audio(audioUrl);

      // Add event listeners to the audio element
      audio.onplay = () => console.log('Audio playback started');
      audio.onended = () => {
        console.log('Audio playback completed');

        // Auto-start recording for reply after playback finishes
        // Only if conversation is still active and not currently active or processing
        if (isConversationActive.value && !isActive.value && !isProcessing.value) {
          console.log('Auto-starting recording after audio response completed');
          setTimeout(() => startRecording(), 500); // Short delay before starting
        } else if (!isConversationActive.value) {
          console.log('Not auto-starting recording because conversation was manually stopped');
        }
      };
      audio.onerror = (e) => console.error('Audio playback error:', e);

      // Play the audio
      try {
        await audio.play();
      } catch (playError) {
        console.error('Failed to play audio:', playError);
        // Try playing again after a short delay (sometimes helps with browser autoplay policies)
        setTimeout(() => {
          audio.play().catch(e => console.error('Retry play failed:', e));
        }, 1000);
      }
    } catch (audioError) {
      console.error('Audio processing error:', audioError);
      throw new Error(`Audio playback failed: ${audioError.message}`);
    }
  } catch (error) {
    console.error('Error in processAudio:', error);
    response.value = `Error: ${error.message}`;
    throw error; // Re-throw to be caught by the caller
  } finally {
    isProcessing.value = false;
  }
}

// Toggle the voice assistant on/off
function toggleAssistant() {
  console.log(`toggleAssistant: Clicked! isActive is currently: ${isActive.value}`);
  if (isActive.value) {
    stopRecording();
  } else {
    // When manually starting, activate conversation mode
    isConversationActive.value = true;
    startRecording();
  }
}

// Stop the entire conversation cycle
function stopConversation() {
  console.log('stopConversation: Ending conversation loop');
  isConversationActive.value = false;

  // If currently recording, stop that too
  if (isActive.value) {
    stopRecording();
  }
}

// Get list of available audio input devices
async function getAudioInputDevices() {
  try {
    console.log('Enumerating audio input devices...');
    await navigator.mediaDevices.getUserMedia({ audio: true }); // Ensure permissions are granted
    const devices = await navigator.mediaDevices.enumerateDevices();
    availableAudioDevices.value = devices
      .filter(device => device.kind === 'audioinput')
      .map(device => ({
        label: device.label || `Microphone ${availableAudioDevices.value.length + 1}`,
        deviceId: device.deviceId
      }));
    console.log('Available audio devices:', availableAudioDevices.value);

    // Try to get saved device ID from localStorage
    const savedDeviceId = localStorage.getItem(LOCALSTORAGE_MICROPHONE_KEY);

    if (savedDeviceId) {
      console.log('Found saved microphone device ID:', savedDeviceId);
      // Check if saved device is available in current devices
      const deviceExists = availableAudioDevices.value.some(device => device.deviceId === savedDeviceId);

      if (deviceExists) {
        console.log('Using saved microphone device ID');
        selectedAudioDeviceId.value = savedDeviceId;
        return; // Early return to avoid setting to first device
      } else {
        console.log('Saved microphone device is no longer available');
        // Continue to select first available device
      }
    }

    // If no device is selected, or the selected one is no longer available, select the first one
    if (availableAudioDevices.value.length > 0 && (!selectedAudioDeviceId.value || !availableAudioDevices.value.some(device => device.deviceId === selectedAudioDeviceId.value))) {
        selectedAudioDeviceId.value = availableAudioDevices.value[0].deviceId;
        console.log('Selected audio device set to:', selectedAudioDeviceId.value);
    }
  } catch (error) {
    console.error('Error enumerating audio devices:', error);
    availableAudioDevices.value = []; // Clear list on error
    // Handle specific errors like NotAllowedError if needed
    if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
        response.value = 'Error: Microphone access denied. Cannot list devices.';
    } else {
        response.value = 'Error: Could not list audio devices.';
    }
  }
}
</script>

<style scoped>
.openai-voice-assistant {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
}

.width-100 {
  width: 100%;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--v-theme-success), 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--v-theme-success), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--v-theme-success), 0);
  }
}
</style>
