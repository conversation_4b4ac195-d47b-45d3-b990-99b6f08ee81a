<template>
  <div class="voice-assistant">
    <v-card class="full-height-card">
      <v-card-title class="d-flex align-center landscape-compact">
        <div
          v-if="!editingTitle"
          class="d-flex align-center"
        >
          <span
            class="title-text"
            @click="startEditingTitle"
          >{{ interviewTitle }}</span>
          <v-btn
            icon
            variant="text"
            density="compact"
            class="ml-1 muted-icon"
            @click="startEditingTitle"
          >
            <v-icon size="small">
              mdi-pencil
            </v-icon>
          </v-btn>
        </div>
        <v-text-field
          v-else
          v-model="interviewTitle"
          variant="underlined"
          hide-details
          density="compact"
          placeholder="Interview Title"
          class="title-input"
          autofocus
          @blur="stopEditingTitle"
          @keyup.enter="stopEditingTitle"
        />
      </v-card-title>
      <v-divider />
      <v-card-text class="pa-8 d-flex flex-column justify-center align-center landscape-content">
        <!-- Status Message -->
        <v-alert
          v-if="statusMessage"
          :type="statusType"
          class="mb-4 landscape-alert"
          closable
          density="compact"
          @click:close="statusMessage = ''"
        >
          {{ statusMessage }}
        </v-alert>

        <v-form
          ref="form"
          class="w-100 d-flex flex-column align-center landscape-form"
        >
          <!-- Two-column layout container for landscape mode -->
          <div class="landscape-container">
            <!-- Left column: Balance and Timer Display -->
            <div class="landscape-left-column">
              <div class="balance-timer-row">
                <v-card
                  class="balance-card"
                  elevation="2"
                >
                  <v-card-text class="text-center landscape-card-content">
                    <v-icon
                      size="32"
                      color="primary"
                      class="mb-2 landscape-icon"
                    >
                      mdi-clock-outline
                    </v-icon>
                    <div class="text-h6 font-weight-bold mb-1 landscape-text">
                      Minutes Balance
                    </div>
                    <div class="text-h5 landscape-text">
                      {{ minutesStore.formattedBalance }}
                    </div>
                    <v-btn
                      v-if="minutesStore.balance <= 0"
                      color="primary"
                      variant="tonal"
                      class="mt-4 landscape-button"
                      size="small"
                      @click="showPurchaseDialog = true"
                    >
                      Buy Minutes
                    </v-btn>
                  </v-card-text>
                </v-card>

                <v-card
                  class="duration-card"
                  :class="{ 'opacity-50': !isActive }"
                  elevation="2"
                >
                  <v-card-text class="text-center landscape-card-content">
                    <v-icon
                      size="32"
                      :color="isActive ? 'primary' : 'grey'"
                      class="mb-2 landscape-icon"
                    >
                      mdi-timer-outline
                    </v-icon>
                    <div class="text-h6 font-weight-bold mb-1 landscape-text">
                      Call Duration
                    </div>
                    <div class="d-flex align-center justify-center">
                      <div class="text-h5 landscape-text">
                        {{ isActive ? formattedCallDuration : '0:00' }}
                      </div>
                      <v-chip
                        v-if="timeLimit"
                        size="small"
                        color="primary"
                        variant="elevated"
                        class="ml-2 font-weight-medium"
                        :disabled="isActive"
                      >
                        {{ timeLimit }}m
                      </v-chip>
                    </div>
                    <v-menu>
                      <template #activator="{ props }">
                        <v-btn
                          variant="text"
                          density="compact"
                          size="small"
                          class="mt-2 landscape-button"
                          v-bind="props"
                          :disabled="isActive"
                        >
                          Set Time Limit
                        </v-btn>
                      </template>
                      <v-list>
                        <v-list-subheader>Set Time Limit (minutes)</v-list-subheader>
                        <v-list-item
                          v-for="limit in [5, 8, 10, 12, 15, 20, 30]"
                          :key="limit"
                          :active="timeLimit === limit"
                          @click="timeLimit = limit"
                        >
                          <v-list-item-title>{{ limit }}</v-list-item-title>
                        </v-list-item>
                        <v-divider />
                        <v-list-item @click="timeLimit = null">
                          <v-list-item-title>No limit</v-list-item-title>
                        </v-list-item>
                      </v-list>
                    </v-menu>
                  </v-card-text>
                </v-card>
              </div>
            </div>

            <!-- Right column: Interview Focus Multi-select and Start/Stop Button -->
            <div class="landscape-right-column">
              <!-- Interview Focus Multi-select -->
              <div
                v-if="!hideMicrophone && minutesStore.balance > 0"
                class="mb-4 interview-focus-container"
              >
                <v-select
                  v-model="selectedInterviewFocus"
                  :items="interviewFocusOptions"
                  label="Interview Focus (optional)"
                  variant="outlined"
                  multiple
                  chips
                  class="interview-focus-select my-4"
                  density="comfortable"
                  :disabled="isActive"
                  hint="Select the topics you want to focus on."
                  persistent-hint
                >
                  <template #prepend-inner>
                    <v-icon>mdi-target</v-icon>
                  </template>
                </v-select>
              </div>

              <!-- Start/Stop Button -->
              <div
                v-if="!hideMicrophone && minutesStore.balance > 0"
                class="d-flex align-center justify-center interview-control-container"
              >
                <v-btn-group
                  class="w-100 interview-btn-group"
                >
                  <v-btn
                    color="primary"
                    size="large"
                    class="pulse-animation flex-grow-1 visualizer-button landscape-main-button"
                    :disabled="isProcessing"
                    @click="!isProcessing && toggleAssistant()"
                  >
                    <div class="button-content">
                      <template v-if="isProcessing">
                        <v-progress-circular
                          indeterminate
                          color="white"
                          size="20"
                          width="2"
                          class="mr-3"
                        />
                        <span class="loading-status">{{ loadingStatus }}</span>
                      </template>
                      <template v-else>
                        <v-icon
                          class="mr-2"
                          size="24"
                        >
                          {{ isActive ? 'mdi-microphone' : 'mdi-microphone-outline' }}
                        </v-icon>
                        {{ isActive ? 'Stop Interview' : 'Start Interview' }}
                      </template>
                    </div>
                    <div
                      v-if="isActive"
                      class="sound-level-container"
                    >
                      <div class="sound-level-bars">
                        <div
                          v-for="(bar, index) in soundLevelBars"
                          :key="index"
                          class="sound-level-bar"
                          :class="{ 'muted': isMuted }"
                          :style="{ height: isMuted ? '10%' : bar + '%' }"
                        />
                      </div>
                    </div>
                  </v-btn>

                  <v-divider vertical />

                  <v-menu>
                    <template #activator="{ props }">
                      <v-btn
                        color="primary"
                        variant="tonal"
                        size="large"
                        v-bind="props"
                      >
                        <v-icon>mdi-microphone</v-icon>
                      </v-btn>
                    </template>
                    <v-card
                      min-width="300"
                      class="pa-2"
                    >
                      <v-card-title class="text-subtitle-1">
                        Microphone Settings
                      </v-card-title>
                      <v-card-text>
                        <div v-if="availableAudioDevices.length > 0">
                          <v-list>
                            <v-list-item
                              v-for="device in availableAudioDevices"
                              :key="device.deviceId"
                              :active="selectedAudioDeviceId === device.deviceId"
                              @click="selectedAudioDeviceId = device.deviceId"
                            >
                              <template #prepend>
                                <v-icon>
                                  {{ selectedAudioDeviceId === device.deviceId ? 'mdi-radiobox-marked' : 'mdi-radiobox-blank' }}
                                </v-icon>
                              </template>
                              <v-list-item-title>{{ device.label }}</v-list-item-title>
                            </v-list-item>
                          </v-list>
                        </div>
                        <div
                          v-else
                          class="text-caption"
                        >
                          No microphones available
                        </div>
                      </v-card-text>
                    </v-card>
                  </v-menu>
                </v-btn-group>
              </div>

              <!-- Selected Microphone Display -->
              <div
                v-if="!hideMicrophone && minutesStore.balance > 0 && (isActive || selectedAudioDeviceId)"
                class="selected-mic-display text-center mt-2"
              >
                <span class="text-caption text-grey">{{ currentMicrophoneName }}</span>
                <v-chip
                  v-if="isActive"
                  size="x-small"
                  class="ml-2 mute-chip"
                  :color="isMuted ? 'error' : 'success'"
                  @click="toggleMute"
                >
                  <v-icon
                    size="x-small"
                    class="mr-1"
                  >
                    {{ isMuted ? 'mdi-microphone-off' : 'mdi-microphone' }}
                  </v-icon>
                  {{ isMuted ? 'Muted' : 'Mic On' }}
                  <v-tooltip
                    activator="parent"
                    location="top"
                  >
                    {{ isMuted ? 'Unmute' : 'Mute' }} (Press 'm')
                  </v-tooltip>
                </v-chip>
              </div>
            </div>
          </div>
        </v-form>
      </v-card-text>
      <v-divider />
      <v-card-actions class="landscape-compact">
        <!-- Start Generation Here -->
        <v-btn
          color="primary"
          variant="text"
          @click="handleCallButtonClick"
        >
          Or Call (*************
        </v-btn>
        <v-spacer />
        <v-btn
          color="grey-darken-1"
          variant="text"
          @click="$emit('close')"
        >
          Close
        </v-btn>
      </v-card-actions>
    </v-card>

    <!-- Purchase Minutes Dialog -->
    <PurchaseMinutesDialog
      v-model="showPurchaseDialog"
      :loading="isPurchasing"
      @purchase="handlePurchase"
      @coupon-redeemed="refreshMinutesBalance"
    />

    <!-- Phone Number Dialog -->
    <v-dialog
      v-model="showPhoneDialog"
      max-width="500"
      persistent
    >
      <v-card>
        <v-card-title class="text-h5 pb-1">
          <div class="d-flex align-center mb-2">
            <v-icon
              icon="mdi-phone"
              color="primary"
              class="mr-2"
            />
            <span>Add Your Mobile Number</span>
          </div>
        </v-card-title>
        <v-card-text class="py-4">
          <p class="mb-4">
            To identify you when calling our service, please provide your mobile phone number:
          </p>
          <v-form
            ref="phoneForm"
            v-model="phoneFormValid"
          >
            <v-text-field
              v-model="mobilePhone"
              label="Mobile Phone"
              variant="outlined"
              :rules="[v => !!v || 'Phone number is required']"
              placeholder="(*************"
              hint="We'll use this number to identify you when you call (*************"
              persistent-hint
              @update:model-value="formatMobilePhoneInput"
            />
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="error"
            variant="text"
            @click="showPhoneDialog = false"
          >
            Cancel
          </v-btn>
          <v-btn
            color="primary"
            variant="elevated"
            :loading="savingPhone"
            :disabled="!phoneFormValid"
            @click="savePhoneNumber"
          >
            Save & Call
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import Vapi from "@vapi-ai/web";
import { useAuthStore } from '../stores/auth';
import { useMinutesStore } from '../stores/minutes';
import PurchaseMinutesDialog from './PurchaseMinutesDialog.vue';
import { supabase } from '@/plugins/supabase';
import { useRouter } from 'vue-router';

// Define emits
const emit = defineEmits(['close']);

// Get the router
const router = useRouter();

// State variables
const isActive = ref(false);
const isProcessing = ref(false);
const statusMessage = ref('');
const statusType = ref('info');
const availableAudioDevices = ref([]);
const selectedAudioDeviceId = ref('');
const callStartTime = ref(null);
const callDuration = ref(0);
const timeLimit = ref(null); // Time limit in minutes for the call
let callTimer = null;
let vapiInstance = null;
const LOCALSTORAGE_MICROPHONE_KEY = 'vapi-voice-assistant-selected-microphone';
const authStore = useAuthStore();
const minutesStore = useMinutesStore();
const showPurchaseDialog = ref(false);
const isPurchasing = ref(false);
const hideMicrophone = ref(false);
const endedDueToNoMinutes = ref(false);
const userProfile = ref(null);
const interviewTitle = ref('New Interview');
const editingTitle = ref(false);
const isMuted = ref(false);
let audioStream = null; // Store active audio stream for muting
const currentInterviewId = ref(null); // Store the current interview ID
const loadingStatus = ref(''); // Status message for button during loading

// Interview focus options
const interviewFocusOptions = ['Current Events', 'Social Impact Initiative', 'Personal Background'];
const selectedInterviewFocus = ref([]);

// Interview profile state
const interviewProfiles = ref([]);
const selectedInterviewProfileId = ref(null);
const loadingProfiles = ref(false);

// Sound level visualization
const soundLevelBars = ref(Array(30).fill(5)); // Increased from 15 to 30 bars
let audioContext = null;
let analyser = null;
let microphone = null;
let animationFrame = null;

// Phone dialog state
const showPhoneDialog = ref(false);
const mobilePhone = ref('');
const phoneForm = ref(null);
const phoneFormValid = ref(false);
const savingPhone = ref(false);

// Computed property for the current microphone name
const currentMicrophoneName = computed(() => {
  if (!selectedAudioDeviceId.value || availableAudioDevices.value.length === 0) {
    return 'Default microphone';
  }

  const selectedDevice = availableAudioDevices.value.find(
    device => device.deviceId === selectedAudioDeviceId.value
  );

  return selectedDevice ? selectedDevice.label : 'Default microphone';
});

// Computed properties for formatted display
const formattedCallDuration = computed(() => {
  const minutes = Math.floor(callDuration.value / 60);
  const seconds = callDuration.value % 60;
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
});

// Initialize component
onMounted(() => {
  initializeVapi();
  getAudioInputDevices();
  if (authStore.user?.id) {
    minutesStore.fetchUserMinutes(authStore.user.id);
    minutesStore.subscribeToUserMinutes(authStore.user.id);
    fetchInterviewProfiles();
    fetchUserProfile();
  }
  // Add keyboard shortcut for mute toggle
  window.addEventListener('keydown', handleKeyDown);
});

// Fetch interview profiles from Supabase
async function fetchInterviewProfiles() {
  loadingProfiles.value = true;
  try {
    const { data, error } = await supabase
      .from('interview_profiles')
      .select('*')
      .eq('status', 'active')
      .order('title');

    if (error) {
      throw error;
    }

    interviewProfiles.value = data;

    // Always select first profile
    if (data && data.length > 0) {
      selectedInterviewProfileId.value = data[0];
      console.log('Selected first interview profile:', data[0].title);
    } else {
      console.error('No active interview profiles found');
      statusMessage.value = 'No interview profiles available. Please try again later.';
      statusType.value = 'error';
    }
  } catch (error) {
    console.error('Error fetching interview profiles:', error);
    statusMessage.value = 'Failed to load interview profiles. Please try again later.';
    statusType.value = 'error';
  } finally {
    loadingProfiles.value = false;
  }
}

// Fetch user profile data from Supabase
async function fetchUserProfile() {
  try {
    if (!authStore.user?.id) return;

    const { data, error } = await supabase
      .from('user_profiles')
      .select('id, user_id, first_name, last_name, resume, city, state, social_impact_initiative, current_events, current_events_text, current_events_updated_at, pageant_title, mobile_phone')
      .eq('user_id', authStore.user.id)
      .single();

    if (error) {
      throw error;
    }

    userProfile.value = data;
    console.log('Fetched user profile:', userProfile.value);
    console.log('User first name:', userProfile.value?.first_name);
    console.log('User mobile phone:', userProfile.value?.mobile_phone);
  } catch (error) {
    console.error('Error fetching user profile:', error);
  }
}

// Check if current events need to be updated and fetch them if needed
async function checkAndFetchCurrentEvents() {
  try {
    // Only proceed if user profile is loaded
    if (!userProfile.value) return false;

    // Only proceed if the profile prompt contains the current events placeholder
    if (!selectedInterviewProfileId.value?.prompt?.includes('{{CURRENT_EVENTS}}')) {
      console.log('Profile prompt does not contain current events placeholder, skipping fetch');
      return false;
    }

    // Check if current events keywords are available
    if (!userProfile.value.current_events || userProfile.value.current_events.length === 0) {
      console.log('No current events keywords defined, skipping fetch');
      return false;
    }

    // Check if current events were updated in the last 24 hours
    const lastUpdated = userProfile.value.current_events_updated_at;
    const now = new Date();
    const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    if (lastUpdated && new Date(lastUpdated) > twentyFourHoursAgo && userProfile.value.current_events_text) {
      console.log('Current events are up to date, using cached version');
      return false;
    }

    // Fetch current events - loading state is now managed by the caller
    const response = await fetch('https://automation.aiedgemedia.com/webhook/current-events', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        keywords: userProfile.value.current_events
      }),
    });

    console.log('Current events response:', response);

    if (!response.ok) {
      throw new Error(`Error fetching current events: ${response.statusText}`);
    }

    // Get response as text instead of JSON
    const textContent = await response.text();

    console.log('Current events text content:', textContent);

    // Update user profile with new current events text
    const { error } = await supabase
      .from('user_profiles')
      .update({
        current_events_text: textContent,
        current_events_updated_at: now.toISOString()
      })
      .eq('user_id', authStore.user.id);

    if (error) {
      throw error;
    }

    // Update local user profile with new data
    userProfile.value.current_events_text = textContent;
    userProfile.value.current_events_updated_at = now.toISOString();

    console.log('Current events updated successfully');
    return true;
  } catch (error) {
    console.error('Error fetching or updating current events:', error);
    // Don't show error to user, just log it and continue
    return false;
  }
}

// Watch for changes to selectedAudioDeviceId and save to localStorage
watch(selectedAudioDeviceId, (newDeviceId) => {
  if (newDeviceId) {
    console.log('Saving selected microphone to localStorage:', newDeviceId);
    localStorage.setItem(LOCALSTORAGE_MICROPHONE_KEY, newDeviceId);
  }
});

// Watch for changes to selectedInterviewProfileId
watch(selectedInterviewProfileId, (newProfile) => {
  if (newProfile && vapiConfig.model?.messages) {
    console.log('Updating VAPI config with new interview profile:', newProfile.title);
    // Update the system message in the config
    vapiConfig.model.messages[0].content = getSystemMessage();
    // Update the server URL with the new profile ID
    updateServerUrl();
  }
});

// Watch for changes to selectedInterviewFocus to update system message
watch(selectedInterviewFocus, () => {
  if (vapiConfig.model?.messages) {
    console.log('Updating VAPI config with new interview focus:', selectedInterviewFocus.value);
    // Update the system message in the config to conditionally include sections
    vapiConfig.model.messages[0].content = getSystemMessage();
  }
});

// Watch for changes to interviewTitle to update server URL and interview record
watch(interviewTitle, async (newTitle) => {
  if (vapiConfig.server) {
    console.log('Updating VAPI config with new interview title:', newTitle);
    updateServerUrl();
  }

  // Update the interview record if it exists and title has changed
  if (currentInterviewId.value) {
    await updateInterviewTitle(currentInterviewId.value, newTitle);
  }
});

// Watch isActive to start/stop audio visualization
watch(isActive, (newValue) => {
  if (newValue) {
    startAudioVisualization();
  } else {
    stopAudioVisualization();
  }
});

// Initialize timer when call starts
function startCallTimer() {
  callStartTime.value = Date.now();

  callTimer = setInterval(() => {
    const elapsedSeconds = Math.floor((Date.now() - callStartTime.value) / 1000);
    const previousMinute = Math.floor(callDuration.value / 60);
    const currentMinute = Math.floor(elapsedSeconds / 60);

    callDuration.value = elapsedSeconds;

    // Record a new minute usage only when we complete a full minute
    if (currentMinute > previousMinute && currentMinute > 0) {
      recordSingleMinuteUsage();
    }

    // Check if we have any minutes left in the balance
    if (minutesStore.balance <= 0 && isActive.value) {
      // Stop the call when minutes run out
      endCallDueToNoBalance();
    }

    // Check if time limit has been reached
    if (timeLimit.value && currentMinute >= timeLimit.value && isActive.value) {
      endCallDueToTimeLimit();
    }
  }, 1000);
}

// Stop timer when call ends
function stopCallTimer() {
  if (callTimer) {
    clearInterval(callTimer);
    callTimer = null;
  }
  callDuration.value = 0;
  callStartTime.value = null;
  // We don't reset the timeLimit here as user might want to
  // keep the same time limit for the next call
}

// Handle call end due to no balance
async function endCallDueToNoBalance() {
  if (vapiInstance) {
    try {
      endedDueToNoMinutes.value = true;  // Set the flag
      // Set status message first so it appears immediately
      statusMessage.value = 'Call ended: You have reached the end of your available minutes. Please add more minutes to continue.';
      statusType.value = 'warning';

      // Send message about running out of minutes
      await vapiInstance.send({
        role: "assistant",
        content: "I apologize, but you've reached the end of your available minutes. The call will now end. Please add more minutes to continue using the service.",
      });

      vapiInstance.stop();
    } catch (error) {
      console.error("Error ending call due to no balance:", error);
      vapiInstance.stop();
    }
  }
}

// Handle call end due to time limit reached
async function endCallDueToTimeLimit() {
  if (vapiInstance) {
    try {
      // Set status message first so it appears immediately
      statusMessage.value = `Call ended: You've reached your selected time limit of ${timeLimit.value} minutes.`;
      statusType.value = 'info';

      // Send message about time limit reached
      await vapiInstance.send({
        role: "assistant",
        content: `I notice we've reached the time limit of ${timeLimit.value} minutes for this interview. The call will now end. Thank you for your time!`,
      });

      vapiInstance.stop();
    } catch (error) {
      console.error("Error ending call due to time limit:", error);
      vapiInstance.stop();
    }
  }
}

// Start audio visualization
async function startAudioVisualization() {
  try {
    // Create audio context if it doesn't exist
    if (!audioContext) {
      audioContext = new (window.AudioContext || window.webkitAudioContext)();
      analyser = audioContext.createAnalyser();
      analyser.fftSize = 256;
    }

    // Get microphone input
    const constraints = {
      audio: selectedAudioDeviceId.value ? { deviceId: { exact: selectedAudioDeviceId.value } } : true
    };

    const stream = await navigator.mediaDevices.getUserMedia(constraints);
    audioStream = stream; // Store the stream for muting/unmuting
    microphone = audioContext.createMediaStreamSource(stream);
    microphone.connect(analyser);

    // Start visualization loop
    updateSoundLevel();
  } catch (error) {
    console.error('Error starting audio visualization:', error);
  }
}

// Update sound level visualization
function updateSoundLevel() {
  if (!analyser) return;

  const bufferLength = analyser.frequencyBinCount;
  const dataArray = new Uint8Array(bufferLength);
  analyser.getByteFrequencyData(dataArray);

  const numBars = soundLevelBars.value.length;
  const centerIndex = Math.floor(numBars / 2);

  // If muted, set all bars to minimum height
  if (isMuted.value) {
    soundLevelBars.value = Array(numBars).fill(10);
  } else {
    // Update bar heights based on frequency data with bell curve distribution
    for (let i = 0; i < numBars; i++) {
      // Calculate distance from center (0 to 1, where 0 is center and 1 is edges)
      const distanceFromCenter = Math.abs(i - centerIndex) / centerIndex;

      // Create bell curve weight (1 at center, decreasing towards edges)
      const bellCurveWeight = Math.exp(-3 * distanceFromCenter * distanceFromCenter);

      // Get frequency data from a position relative to the bar's position
      const freqIndex = Math.floor((i / numBars) * bufferLength);
      const rawValue = dataArray[freqIndex];

      // Apply bell curve weight and ensure minimum height
      // Increased base multiplier and added power function for more dramatic scaling
      const weightedValue = Math.pow((rawValue / 255) * bellCurveWeight, 0.7) * 180;
      soundLevelBars.value[i] = 10 + Math.min(90, weightedValue); // Maintain 10% minimum height
    }
  }

  // Continue the animation loop
  animationFrame = requestAnimationFrame(updateSoundLevel);
}

// Stop audio visualization
function stopAudioVisualization() {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame);
    animationFrame = null;
  }

  if (microphone) {
    microphone.disconnect();
    microphone = null;
  }

  if (audioContext && audioContext.state !== 'closed') {
    // Don't close the audio context, just disconnect
    // audioContext.close();
    // audioContext = null;
  }

  // Reset bars to minimum height (10%)
  soundLevelBars.value = Array(30).fill(10);
}

// Create Vapi configuration for the start method
let vapiConfig = {};

// Initialize Vapi instance
function initializeVapi() {
  // Create Vapi instance with API key
  vapiInstance = new Vapi('04b6f583-4a42-4f1b-9e2d-43bbf1b78f93')

  // Prepare system message with interview profile prompt and user resume
  const systemMessage = getSystemMessage();

  // Log user profile data when initializing Vapi
  console.log('User profile when initializing Vapi:', userProfile.value);
  console.log('First name available:', userProfile.value?.first_name);

  vapiConfig = {
    "name": "Interviewer",
    "voice": {
      "voiceId": "Paige",
      "provider": "vapi"
    },
    "model": {
      "model": "gpt-4o-mini",
      "messages": [
        {
          "role": "system",
          "content": systemMessage
        }
      ],
      "provider": "openai",
      "temperature": 0.5
    },
    // The firstMessage will be set dynamically in updateVapiConfigBeforeStart
    "firstMessage": "Hello there. How are you today?",
    "transcriber": {
      "model": "nova-3",
      "language": "en",
      "numerals": false,
      "provider": "deepgram",
      "endpointing": 150,
      "confidenceThreshold": 0.4
    },
    "clientMessages": [
      "transcript",
      "hang",
      "function-call",
      "speech-update",
      "metadata",
      "transfer-update",
      "conversation-update",
      "workflow.node.started"
    ],
    "serverMessages": [
      "end-of-call-report",
      // "status-update",
      // "hang",
      // "function-call"
    ],
    "endCallPhrases": [
      "goodbye",
      "talk to you soon"
    ],
    "hipaaEnabled": false,
    "backgroundSound": "off",
    "backchannelingEnabled": false,
    "backgroundDenoisingEnabled": true,
    "startSpeakingPlan": {
      "waitSeconds": 0.4,
      "transcriptionEndpointingPlan": {
        "onNumberSeconds": 0.5
      },
      "smartEndpointingEnabled": "livekit"
    },
    "server": {
      "url": ""  // Will be set by updateServerUrl()
    },
  }

  // Set initial server URL
  updateServerUrl();

  // Add event listeners
  vapiInstance.on("call-start", () => {
    console.log("Call has started.");
    isActive.value = true;
    isProcessing.value = false;
    loadingStatus.value = ""; // Clear loading status when call starts
    endedDueToNoMinutes.value = false;  // Reset the flag at start of call
    startCallTimer();
  });

  vapiInstance.on("call-end", async () => {
    console.log("Call has ended.");
    isActive.value = false;
    isProcessing.value = false;
    loadingStatus.value = ""; // Clear loading status when call ends
    isMuted.value = false; // Reset mute state when call ends
    audioStream = null; // Clear the stored stream reference

    // Only deduct final minute if call wasn't ended due to running out of minutes
    if (!endedDueToNoMinutes.value) {
      try {
        await recordSingleMinuteUsage();
        console.log('Final minute recorded for call end');
      } catch (error) {
        console.error("Failed to record final minute:", error);
        statusMessage.value = "Failed to record final minute. Please contact support if this persists.";
        statusType.value = "error";
      }
    }

    // Update interview duration if we have an interview record
    if (currentInterviewId.value) {
      try {
        // Calculate duration in minutes, rounded up
        const durationMinutes = Math.ceil(callDuration.value / 60);

        await supabase
          .from('interviews')
          .update({ duration_minutes: durationMinutes })
          .eq('id', currentInterviewId.value);

        console.log('Updated interview duration for ID:', currentInterviewId.value, 'to', durationMinutes, 'minutes');

        // Store the interview ID before resetting it
        const interviewId = currentInterviewId.value;

        stopCallTimer();
        endedDueToNoMinutes.value = false;  // Reset the flag
        currentInterviewId.value = null; // Reset the interview ID

        // Re-subscribe to get fresh data
        minutesStore.subscribeToUserMinutes(authStore.user.id);

        // Close dialog and navigate to interview detail page
        emit('close');
        router.push(`/interviews/${interviewId}`);
      } catch (error) {
        console.error('Error updating interview duration:', error);
        stopCallTimer();
        endedDueToNoMinutes.value = false;  // Reset the flag
        currentInterviewId.value = null; // Reset the interview ID
        minutesStore.subscribeToUserMinutes(authStore.user.id);
      }
    } else {
      stopCallTimer();
      endedDueToNoMinutes.value = false;  // Reset the flag
      currentInterviewId.value = null; // Reset the interview ID
      minutesStore.subscribeToUserMinutes(authStore.user.id);
    }
  });

  // Add server message handler for end-of-call report
  vapiInstance.on("server-message", async (message) => {
    console.log("Received server message:", message);

    // Handle end-of-call report
    if (message.type === "end-of-call-report" && currentInterviewId.value) {
      try {
        console.log("Saving end-of-call report for interview:", currentInterviewId.value);

        const { error } = await supabase
          .from('interviews')
          .update({ end_of_call_report: message.data })
          .eq('id', currentInterviewId.value);

        if (error) {
          throw error;
        }

        console.log("Successfully saved end-of-call report");
      } catch (error) {
        console.error("Error saving end-of-call report:", error);
      }
    }
  });

  vapiInstance.on("error", (e) => {
    console.error("Vapi error:", e);
    isActive.value = false;
    isProcessing.value = false;
    loadingStatus.value = ""; // Clear loading status on error
    stopCallTimer();
  });
}

// Get list of available audio input devices
async function getAudioInputDevices() {
  try {
    console.log('Enumerating audio input devices...');
    await navigator.mediaDevices.getUserMedia({ audio: true }); // Ensure permissions are granted
    const devices = await navigator.mediaDevices.enumerateDevices();
    availableAudioDevices.value = devices
      .filter(device => device.kind === 'audioinput')
      .map(device => ({
        label: device.label || `Microphone ${availableAudioDevices.value.length + 1}`,
        deviceId: device.deviceId
      }));
    console.log('Available audio devices:', availableAudioDevices.value);

    // Try to get saved device ID from localStorage
    const savedDeviceId = localStorage.getItem(LOCALSTORAGE_MICROPHONE_KEY);

    if (savedDeviceId) {
      console.log('Found saved microphone device ID:', savedDeviceId);
      // Check if saved device is available in current devices
      const deviceExists = availableAudioDevices.value.some(device => device.deviceId === savedDeviceId);

      if (deviceExists) {
        console.log('Using saved microphone device ID');
        selectedAudioDeviceId.value = savedDeviceId;

        // Pre-authorize the selected microphone to make it more likely to be used
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: { deviceId: { exact: savedDeviceId } }
          });
          // Keep this stream active until Vapi needs to be started
          // This will be stored globally so we can access it later
          if (window.preauthorizedStream) {
            window.preauthorizedStream.getTracks().forEach(track => track.stop());
          }
          window.preauthorizedStream = stream;
          console.log('Pre-authorized selected microphone');
        } catch (err) {
          console.error('Failed to pre-authorize selected microphone:', err);
        }

        return; // Early return to avoid setting to first device
      } else {
        console.log('Saved microphone device is no longer available');
        // Continue to select first available device
      }
    }

    // If no device is selected, or the selected one is no longer available, select the first one
    if (availableAudioDevices.value.length > 0 && (!selectedAudioDeviceId.value || !availableAudioDevices.value.some(device => device.deviceId === selectedAudioDeviceId.value))) {
        selectedAudioDeviceId.value = availableAudioDevices.value[0].deviceId;
        console.log('Selected audio device set to:', selectedAudioDeviceId.value);
    }
  } catch (error) {
    console.error('Error enumerating audio devices:', error);
    availableAudioDevices.value = []; // Clear list on error
    if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
      console.error('Microphone access denied. Cannot list devices.');
    }
  }
}

// Toggle the voice assistant on/off
async function toggleAssistant() {
  console.log("toggleAssistant called. isActive:", isActive.value);

  // Prevent multiple executions if already processing
  if (isProcessing.value) {
    console.log("Already processing, ignoring click");
    return;
  }

  // Early return if no minutes available
  if (minutesStore.balance <= 0) {
    statusMessage.value = 'You have no minutes remaining. Please add more minutes to your account to start a call.';
    statusType.value = 'warning';
    showPurchaseDialog.value = false;
    hideMicrophone.value = true;
    return;
  }

  // Log user profile before starting
  console.log('User profile before starting call:', userProfile.value);

  // If user profile isn't loaded yet, try to fetch it
  if (!userProfile.value && authStore.user?.id) {
    loadingStatus.value = "Loading user profile...";
    isProcessing.value = true;
    await fetchUserProfile();
    isProcessing.value = false;
  }

  // Make sure we have an interview profile selected
  if (!selectedInterviewProfileId.value && !isActive.value) {
    if (interviewProfiles.value && interviewProfiles.value.length > 0) {
      // Automatically select the first profile
      selectedInterviewProfileId.value = interviewProfiles.value[0];
      console.log('Auto-selected first interview profile:', interviewProfiles.value[0].title);
    } else {
      statusMessage.value = 'No interview profiles available. Please try again later.';
      statusType.value = 'error';
      return;
    }
  }

  if (isActive.value) {
    console.log("Stopping Vapi...");
    vapiInstance.stop();
    // isActive is now managed by the 'call-end' event
  } else {
    console.log("Starting Vapi...");
    isProcessing.value = true; // Indicate processing before starting
    loadingStatus.value = "Preparing..."; // Default status

    try {
      // Check if current events are needed before showing loading state
      if (selectedInterviewProfileId.value?.prompt?.includes('{{CURRENT_EVENTS}}')) {
        // Show loading status in the button
        loadingStatus.value = "Retrieving current events...";

        try {
          await checkAndFetchCurrentEvents();
        } catch (error) {
          console.error("Error retrieving current events:", error);
          // Continue with the flow even if current events fail
        }
      }

      // Create a new interview record
      loadingStatus.value = "Creating interview...";
      const interviewId = await createInterviewRecord();
      if (interviewId) {
        currentInterviewId.value = interviewId;
      }

      if (!vapiInstance) {
        console.log("Initializing Vapi before starting...");
        loadingStatus.value = "Initializing assistant...";
        initializeVapi(); // Ensure Vapi is initialized if not already
      }

      // Update loading status
      loadingStatus.value = "Starting interview...";

      // Always update config with the latest user data before starting
      await updateVapiConfigBeforeStart();

      // We need to temporarily patch the getUserMedia function to force it to use our selected device
      if (selectedAudioDeviceId.value) {
        console.log("Preparing to enforce selected microphone:", selectedAudioDeviceId.value);

        // Stop any existing pre-authorized stream
        if (window.preauthorizedStream) {
          window.preauthorizedStream.getTracks().forEach(track => track.stop());
          window.preauthorizedStream = null;
        }

        // Create a new temporary stream with the selected device
        navigator.mediaDevices.getUserMedia({
          audio: { deviceId: { exact: selectedAudioDeviceId.value } }
        }).then(stream => {
          console.log("Selected microphone activated");

          // Store the original getUserMedia function
          const originalGetUserMedia = navigator.mediaDevices.getUserMedia;

          // Override getUserMedia to always use our selected device
          navigator.mediaDevices.getUserMedia = async function(constraints) {
            console.log("Intercepted getUserMedia call", constraints);

            // If the constraints include audio, force our device
            if (constraints && constraints.audio) {
              console.log("Enforcing selected microphone in getUserMedia");
              constraints.audio = {
                deviceId: { exact: selectedAudioDeviceId.value }
              };
            }

            // Call the original function with our modified constraints
            return originalGetUserMedia.call(this, constraints);
          };

          // Start Vapi with normal config
          console.log("Starting Vapi with enforced microphone selection");
          vapiInstance.start(vapiConfig)
            .then(() => {
              console.log("Vapi started successfully");

              // Restore the original getUserMedia after Vapi has started
              setTimeout(() => {
                navigator.mediaDevices.getUserMedia = originalGetUserMedia;
                console.log("Restored original getUserMedia");

                // Stop our temporary stream
                stream.getTracks().forEach(track => track.stop());
              }, 2000); // Wait 2 seconds to ensure Vapi is fully initialized
            })
            .catch(error => {
              console.error("Error starting Vapi call:", error);

              // Restore original getUserMedia on error
              navigator.mediaDevices.getUserMedia = originalGetUserMedia;
              console.log("Restored original getUserMedia (after error)");

              // Stop our temporary stream
              stream.getTracks().forEach(track => track.stop());

              // Clear loading state
              isProcessing.value = false;
              loadingStatus.value = "";

              // Show error message
              statusMessage.value = "Failed to start interview. Please try again.";
              statusType.value = "error";
            });
        }).catch(error => {
          console.error("Error accessing the selected microphone:", error);

          // Clear loading state
          isProcessing.value = false;
          loadingStatus.value = "";

          // Show error message
          statusMessage.value = "Failed to access microphone. Please check your device permissions.";
          statusType.value = "error";
        });
      } else {
        // Start with default device if no specific device is selected
        vapiInstance.start(vapiConfig).catch(error => {
          console.error("Error starting Vapi call:", error);

          // Clear loading state
          isProcessing.value = false;
          loadingStatus.value = "";

          // Show error message
          statusMessage.value = "Failed to start interview. Please try again.";
          statusType.value = "error";
        });
      }
      // isActive is now managed by the 'call-start' event
    } catch (error) {
      console.error("Error in interview setup process:", error);

      // Clear loading state
      isProcessing.value = false;
      loadingStatus.value = "";

      // Show error message
      statusMessage.value = "An error occurred while setting up the interview. Please try again.";
      statusType.value = "error";
    }
  }
}

// Watch for balance changes to update UI
watch(() => minutesStore.balance, (newBalance) => {
  if (newBalance <= 0) {
    if (isActive.value) {
      endCallDueToNoBalance();
    } else {
      statusMessage.value = 'You have no minutes remaining. Please add more minutes to your account to start a call.';
      statusType.value = 'warning';
      // showPurchaseDialog.value = true;
    }
  } else if (statusMessage.value && statusMessage.value.includes('end of your available minutes')) {
    // Clear the status message if minutes were added and we had the "end of minutes" message showing
    statusMessage.value = '';
  }
});

// Clean up on component unmount
onUnmounted(() => {
  stopCallTimer();
  if (vapiInstance) {
    vapiInstance.stop();
  }

  // Stop audio visualization and clean up
  stopAudioVisualization();

  // Remove device change listener
  navigator.mediaDevices.removeEventListener('devicechange', getAudioInputDevices);

  // Remove keyboard event listener
  window.removeEventListener('keydown', handleKeyDown);
});

// Handle purchase of minutes
async function handlePurchase(plan) {
  isPurchasing.value = true;
  try {
    console.log('Processing purchase for plan:', plan);
    // Here you would implement the actual purchase logic
    // For now, we'll just close the dialog
    showPurchaseDialog.value = false;
  } catch (error) {
    console.error('Error processing purchase:', error);
    statusMessage.value = 'Failed to process purchase. Please try again.';
    statusType.value = 'error';
  } finally {
    isPurchasing.value = false;
  }
}

// Replace the recordMinutesUsage function with these two functions
async function recordSingleMinuteUsage() {
  try {
    const { error } = await supabase
      .from('user_minutes')
      .insert({
        user_id: authStore.user.id,
        minutes: -1, // Deduct exactly one minute
        status: 'pending',
        created_at: new Date().toISOString()
      });

    if (error) {
      throw error;
    }

    console.log('Single minute usage recorded');
  } catch (error) {
    console.error('Error recording minute usage:', error);
    throw error;
  }
}

// Helper function to generate the system message with interview profile prompt and user resume
function getSystemMessage() {
  let systemMessage = 'You are a helpful interviewer assistant';

  if (selectedInterviewProfileId.value?.prompt) {
    // Replace placeholder with user's resume if available
    let promptText = selectedInterviewProfileId.value.prompt;

    // Only include sections that match the selected focus areas
    const includeUserBio = !selectedInterviewFocus.value.length || selectedInterviewFocus.value.includes('Personal Background');
    const includeCurrentEvents = !selectedInterviewFocus.value.length || selectedInterviewFocus.value.includes('Current Events');
    const includeSocialImpact = !selectedInterviewFocus.value.length || selectedInterviewFocus.value.includes('Social Impact Initiative');

    // Replace user bio placeholder
    if (userProfile.value?.resume && promptText.includes('{{USER_BIO}}') && includeUserBio) {
      const bioPrefix = "The following is the contestant's bio/resume. Ask several personal, relevant questions based on this information. Focus on hobbies, goals, causes they care about, or achievements. Let their personality and values shine through.\n\nAsk thoughtful follow-up questions based on their answers.\n\nBIO/RESUME:\n";
      promptText = promptText.replace('{{USER_BIO}}', bioPrefix + userProfile.value.resume);
    } else if (promptText.includes('{{USER_BIO}}')) {
      // If resume is not available or not in focus, replace with empty string
      promptText = promptText.replace('{{USER_BIO}}', '');
    }

    // Replace user name placeholder
    if (userProfile.value?.first_name && userProfile.value?.last_name && promptText.includes('{{USER_NAME}}')) {
      const fullName = `${userProfile.value.first_name} ${userProfile.value.last_name}`;
      promptText = promptText.replace(/{{USER_NAME}}/g, fullName);
    }

    // Replace user location placeholder
    if (userProfile.value?.city && userProfile.value?.state && promptText.includes('{{USER_LOCATION}}')) {
      const location = `${userProfile.value.city}, ${userProfile.value.state}`;
      promptText = promptText.replace(/{{USER_LOCATION}}/g, location);
    }

    // Replace pageant title placeholder
    if (promptText.includes('{{PAGEANT_TITLE}}')) {
      const pageantTitle = userProfile.value?.pageant_title || 'beauty pageant';
      promptText = promptText.replace(/{{PAGEANT_TITLE}}/g, pageantTitle);
    }

    // Replace user social impact initiative placeholder
    if (userProfile.value?.social_impact_initiative && promptText.includes('{{USER_SOCIAL_IMPACT_INITIATIVE}}') && includeSocialImpact) {
      const impactPrefix = "The contestant has declared the following Social Impact Initiative. Ask at least one question based on this topic. You may ask why they chose this cause, what actions they've taken, what outcomes they've seen, or how they plan to advance the initiative if they win the title.\n\nSOCIAL IMPACT INITIATIVE:\n";
      promptText = promptText.replace(/{{USER_SOCIAL_IMPACT_INITIATIVE}}/g, impactPrefix + userProfile.value.social_impact_initiative);
    } else if (promptText.includes('{{USER_SOCIAL_IMPACT_INITIATIVE}}')) {
      // If social impact initiative is not available or not in focus, replace with empty string
      promptText = promptText.replace(/{{USER_SOCIAL_IMPACT_INITIATIVE}}/g, '');
    }

    // Replace current events placeholder
    if (userProfile.value?.current_events_text && promptText.includes('{{CURRENT_EVENTS}}') && includeCurrentEvents) {
      const eventsPrefix = "The following are recent current events. Ask one or more questions that connect to these headlines or summaries. Choose topics that would prompt thoughtful opinions, values-based responses, or critical thinking.\n\nQuestions may explore:\n- Their view on the topic\n- Whether titleholders should speak out about it\n- How the issue affects young women or their community\n- Ethical or leadership challenges involved\n\nCURRENT EVENTS:\n";
      promptText = promptText.replace(/{{CURRENT_EVENTS}}/g, eventsPrefix + userProfile.value.current_events_text);
    } else if (promptText.includes('{{CURRENT_EVENTS}}')) {
      // If current events text is not available or not in focus, replace with empty string
      promptText = promptText.replace(/{{CURRENT_EVENTS}}/g, '');
    }

    systemMessage = promptText;
  }
  console.log('System message:', systemMessage);
  return systemMessage;
}

// Start editing title
function startEditingTitle() {
  editingTitle.value = true;
}

// Stop editing title
async function stopEditingTitle() {
  editingTitle.value = false;
  // Ensure title is not empty
  if (!interviewTitle.value.trim()) {
    interviewTitle.value = 'New Interview';
  }

  // Update the interview record if it exists
  if (currentInterviewId.value) {
    await updateInterviewTitle(currentInterviewId.value, interviewTitle.value);
  }
}

// Function to update the server URL with all needed parameters
function updateServerUrl() {
  const profileId = selectedInterviewProfileId.value?.id || '';
  let url = "https://automation.aiedgemedia.com/webhook/vapi?userId=" +
    authStore.user.id +
    "&title=" + encodeURIComponent(interviewTitle.value) +
    "&profileId=" + profileId;

  // Add interview ID if available
  if (currentInterviewId.value) {
    url += "&interviewId=" + currentInterviewId.value;
  }

  vapiConfig.server.url = url;
}

// Toggle mute
function toggleMute() {
  if (!audioStream) return;

  isMuted.value = !isMuted.value;

  // Mute/unmute all audio tracks in the stream
  audioStream.getAudioTracks().forEach(track => {
    track.enabled = !isMuted.value;
  });
}

// Handle keyboard shortcuts
function handleKeyDown(event) {
  // Toggle mute with 'm' key if call is active
  if (event.key === 'm' && isActive.value && !event.ctrlKey && !event.metaKey &&
      !['input', 'textarea'].includes(event.target.tagName.toLowerCase())) {
    toggleMute();
    event.preventDefault();
  }
}

// Function to create a new interview record
async function createInterviewRecord() {
  try {
    if (!authStore.user?.id) return null;

    const profileId = selectedInterviewProfileId.value?.id || null;

    const { data, error } = await supabase
      .from('interviews')
      .insert({
        title: interviewTitle.value,
        user_id: authStore.user.id,
        profile_id: profileId,
        created_at: new Date().toISOString(),
      })
      .select('id')
      .single();

    if (error) {
      throw error;
    }

    console.log('Created new interview record with ID:', data.id);
    return data.id;
  } catch (error) {
    console.error('Error creating interview record:', error);
    return null;
  }
}

// Function to update interview title
async function updateInterviewTitle(interviewId, title) {
  try {
    if (!interviewId) return;

    const { error } = await supabase
      .from('interviews')
      .update({ title })
      .eq('id', interviewId);

    if (error) {
      throw error;
    }

    console.log('Updated interview title for ID:', interviewId);
  } catch (error) {
    console.error('Error updating interview title:', error);
  }
}

// Update vapi config just before starting the call
async function updateVapiConfigBeforeStart() {
  // Make sure we have the latest user profile data
  if (!userProfile.value) {
    await fetchUserProfile();
  }

  // Update first message with latest user profile data
  if (userProfile.value?.first_name) {
    vapiConfig.firstMessage = `Hello, ${userProfile.value.first_name}. How are you today?`;
  } else {
    vapiConfig.firstMessage = "Hello there. How are you today?";
  }

  console.log('Updated first message:', vapiConfig.firstMessage);
  console.log('User profile when updating config:', userProfile.value);

  // Update system message
  vapiConfig.model.messages[0].content = getSystemMessage();

  // Update server URL
  updateServerUrl();
}

// Handle the Call button click
function handleCallButtonClick() {
  // Check if user profile is loaded
  if (!userProfile.value) {
    fetchUserProfile().then(() => {
      checkMobilePhoneAndCall();
    });
  } else {
    checkMobilePhoneAndCall();
  }
}

// Check if mobile phone exists and make call or show dialog
function checkMobilePhoneAndCall() {
  if (!userProfile.value?.mobile_phone) {
    // Phone number not set, show dialog
    showPhoneDialog.value = true;
  } else {
    // Phone number exists, make the call
    makePhoneCall();
    return; // Exit early after making the call
  }

  // Pre-fill the phone field with existing number if present
  if (userProfile.value?.mobile_phone) {
    mobilePhone.value = formatPhoneForDisplay(userProfile.value.mobile_phone);
  } else {
    mobilePhone.value = ''; // Clear the field if no phone number
  }
}

// Format mobile phone input for the dialog
function formatMobilePhoneInput() {
  // Strip all non-numeric characters
  const digitsOnly = mobilePhone.value.replace(/\D/g, '');

  // Format the phone number as (XXX) XXX-XXXX
  if (digitsOnly.length <= 3) {
    mobilePhone.value = digitsOnly;
  } else if (digitsOnly.length <= 6) {
    mobilePhone.value = `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3)}`;
  } else {
    mobilePhone.value = `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6, 10)}`;
  }
}

// Helper function to format phone for display
function formatPhoneForDisplay(phoneNumber) {
  if (!phoneNumber) return '';

  // Format stored phone number for display
  const digitsOnly = phoneNumber.replace(/\D/g, '');
  if (digitsOnly.length === 10) {
    return `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6, 10)}`;
  }

  return phoneNumber;
}

// Save phone number to user profile
async function savePhoneNumber() {
  if (!phoneFormValid.value || !authStore.user?.id) return;

  savingPhone.value = true;

  try {
    // Store only digits in the database for consistency
    const digitsOnly = mobilePhone.value.replace(/\D/g, '');

    const { error } = await supabase
      .from('user_profiles')
      .update({
        mobile_phone: digitsOnly
      })
      .eq('user_id', authStore.user.id);

    if (error) throw error;

    // Update local user profile
    if (userProfile.value) {
      userProfile.value.mobile_phone = digitsOnly;
    }

    // Close dialog
    showPhoneDialog.value = false;

    // Make the phone call
    makePhoneCall();
  } catch (err) {
    console.error('Error saving phone number:', err);
  } finally {
    savingPhone.value = false;
  }
}

// Make the actual phone call
function makePhoneCall() {
  window.location.href = 'tel:8882976477';
}

// Handle coupon-redeemed event
function refreshMinutesBalance() {
  // Implement the logic to refresh minutes balance
  console.log('Refreshing minutes balance');
  minutesStore.fetchUserMinutes(authStore.user.id);
}
</script>

<style scoped>
.voice-assistant {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100vw;
  height: 100vh;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.full-height-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  border-radius: 0;
}

.full-height-card .v-card-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.transcript, .response {
  width: 100%;
  border-radius: 8px;
  background-color: rgba(var(--v-theme-surface-variant), 0.7);
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--v-theme-primary), 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--v-theme-primary), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--v-theme-primary), 0);
  }
}

/* Updated Sound level visualization styles */
.visualizer-button {
  position: relative;
  overflow: hidden;
}

.button-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 220px; /* Increased width to accommodate loading text */
  min-height: 24px; /* Ensure consistent height */
  transition: all 0.2s ease;
}

.sound-level-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  opacity: 0.25; /* Slightly increased opacity for better visibility */
  pointer-events: none;
}

.sound-level-bars {
  display: flex;
  align-items: flex-end;
  justify-content: stretch;
  height: 100%;
  width: 100%;
  gap: 0; /* Removed gap to ensure full width coverage */
}

.sound-level-bar {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.8);
  min-height: 10%; /* Ensure bars are always visible */
  transition: height 0.05s ease;
}

.sound-level-bar.muted {
  background-color: rgba(255, 0, 0, 0.5);
  height: 10% !important; /* Fixed height when muted */
}

.balance-card, .duration-card {
  background-color: rgba(var(--v-theme-surface-variant), 0.1);
  border-radius: 8px;
  padding: 16px;
}

.interview-focus-select {
  max-width: 600px;
  margin: 0 auto;
}

.title-input {
  max-width: 300px;
}

.title-text {
  cursor: pointer;
}

.title-text:hover {
  text-decoration: underline;
  text-decoration-style: dotted;
  text-decoration-thickness: 1px;
}

.muted-icon {
  opacity: 0.5;
}

.muted-icon:hover {
  opacity: 0.8;
}

.selected-mic-display {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12px;
  gap: 8px;
}

.mute-chip {
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.mute-chip:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Add specific styles for loading status */
.loading-status {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  white-space: nowrap;
  font-weight: 500;
}

/* Landscape mode responsive styles */
@media (max-height: 600px) and (orientation: landscape) {
  .landscape-compact {
    padding: 8px !important;
    min-height: auto !important;
  }

  .landscape-content {
    padding: 16px !important;
  }

  .landscape-card-content {
    padding: 12px !important;
  }

  .landscape-text {
    font-size: 0.9rem !important;
    margin-bottom: 4px !important;
  }

  .landscape-icon {
    font-size: 24px !important;
    margin-bottom: 4px !important;
  }

  .landscape-button {
    margin-top: 4px !important;
  }

  .landscape-main-button {
    height: auto !important;
    min-height: 42px !important;
  }

  .landscape-alert {
    margin: 0 0 8px 0 !important;
    padding: 4px 8px !important;
    width: 100%;
  }

  .landscape-container {
    display: flex;
    width: 100%;
    gap: 16px;
    align-items: stretch;
  }

  .landscape-left-column {
    flex: 0 0 25%; /* 1/4 of the page width */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 16px;
  }

  .landscape-right-column {
    flex: 0 0 75%; /* 3/4 of the page width */
    display: flex;
    flex-direction: column;
    justify-content: center; /* Center vertically */
    align-items: center; /* Center horizontally */
    padding: 0 24px;
  }

  .interview-focus-container {
    margin-bottom: 8px !important;
    width: 100%;
    max-width: 500px;
  }

  .interview-focus-select {
    margin-top: 0 !important;
    width: 100%;
  }

  .interview-btn-group {
    width: 100% !important;
    max-width: 500px !important;
  }

  .interview-control-container {
    padding-top: 8px !important;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .selected-mic-display {
    margin-top: 4px !important;
  }

  .balance-card, .duration-card {
    width: 100% !important;
    margin: 0 !important;
  }

  /* Make sure balance-timer-row stays in column format in landscape */
  .balance-timer-row {
    flex-direction: column;
    gap: 16px;
  }
}

/* Additional refinements for extremely small height screens */
@media (max-height: 450px) and (orientation: landscape) {
  .landscape-content {
    padding: 8px !important;
  }

  .landscape-container {
    gap: 8px;
  }

  .landscape-left-column {
    gap: 8px;
  }

  .landscape-right-column {
    padding: 0 16px;
  }

  .interview-focus-select {
    margin-bottom: 8px !important;
  }

  .landscape-card-content {
    padding: 8px !important;
  }
}

/* Balance and timer cards layout */
.balance-timer-row {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

@media (min-width: 768px) {
  .balance-timer-row {
    flex-direction: row;
    justify-content: space-between;
  }

  .balance-card, .duration-card {
    flex: 1;
    margin-bottom: 0 !important;
  }
}
</style>
