<template>
  <div class="filler-words-analysis">
    <h3 class="text-h6 mb-3 d-flex align-center">
      Filler Words
      <v-tooltip
        location="top"
        text="Filler words are sounds or words that don't add meaning to your speech. Reducing them can make you sound more confident and articulate."
        max-width="300"
      >
        <template #activator="{ props }">
          <v-icon
            v-bind="props"
            icon="mdi-information-outline"
            size="small"
            class="ml-2"
          />
        </template>
      </v-tooltip>
    </h3>

    <!-- Loading state -->
    <v-card
      v-if="loading"
      variant="tonal"
      class="pa-3 text-center"
      bg-color="surface"
    >
      <v-progress-circular
        indeterminate
        color="primary"
        class="mb-3"
      />
      <p>Analysis is being generated...</p>
    </v-card>

    <!-- No data state -->
    <v-card
      v-else-if="!fillerWordsData"
      variant="tonal"
      class="pa-3 text-center"
      bg-color="surface"
    >
      <p>No filler words analysis available for this interview.</p>
    </v-card>

    <!-- Data available state -->
    <v-card
      v-else
      variant="tonal"
      class="pa-4"
      bg-color="surface"
    >
      <!-- Summary card with grade -->
      <v-card
        class="mb-4 pa-4"
        :color="gradeBgColor"
      >
        <div class="d-flex align-center">
          <div class="grade-container text-center me-4">
            <div
              class="grade-circle d-flex align-center justify-center mb-1 mx-auto"
              :class="gradeColorClass"
            >
              <span class="text-h4 font-weight-bold">{{ grade }}</span>
            </div>
            <span class="text-caption font-weight-medium">{{ gradeLabel }}</span>
          </div>

          <div class="performance-text flex-grow-1">
            <h4 class="text-subtitle-1 font-weight-bold mb-2">
              Overall Performance
            </h4>
            <div class="d-flex align-center mb-1">
              <v-icon
                :icon="fillerWordIcon"
                :color="gradeIconColor"
                class="me-2"
                size="small"
              />
              <span class="text-body-2">
                {{ fillerWordsData.total_filler_words }} filler words
              </span>
            </div>
            <div class="d-flex align-center mb-1">
              <v-icon
                icon="mdi-text"
                class="me-2"
                size="small"
              />
              <span class="text-body-2">
                {{ fillerWordsData.total_words }} total words
              </span>
            </div>
            <div class="d-flex align-center">
              <v-icon
                icon="mdi-percent"
                :color="gradeIconColor"
                class="me-2"
                size="small"
              />
              <span :class="['text-body-2', 'font-weight-medium', percentageColorClass]">
                {{ fillerWordsData.filler_word_percentage.toFixed(1) }}% filler word rate
              </span>
            </div>
          </div>
        </div>
      </v-card>

      <!-- Filler words breakdown -->
      <h4 class="text-subtitle-1 font-weight-bold mb-3">
        Filler Word Breakdown
      </h4>

      <div
        v-if="sortedFillerWords.length === 0"
        class="text-center py-3"
      >
        <p class="text-body-1">
          No filler words detected!
        </p>
        <v-icon
          icon="mdi-trophy"
          color="success"
          size="large"
          class="mt-2"
        />
      </div>

      <div v-else>
        <div
          v-for="(count, word) in sortedFillerWords"
          :key="word"
          class="mb-3"
        >
          <div class="d-flex justify-space-between align-center mb-1">
            <span class="text-body-2 font-weight-medium">
              "{{ word }}"
            </span>
            <span class="text-body-2">
              {{ count }} {{ count === 1 ? 'time' : 'times' }}
            </span>
          </div>
          <v-progress-linear
            :model-value="(count / maxFillerWordCount) * 100"
            height="12"
            rounded
            :color="getBarColor(count)"
          >
            <template #default>
              <span class="text-caption text-white">{{ Math.round((count / fillerWordsData.total_words) * 100) }}%</span>
            </template>
          </v-progress-linear>
        </div>
      </div>

      <!-- Tips section -->
      <div class="mt-6">
        <h4 class="text-subtitle-1 font-weight-bold mb-2">
          Tips for Improvement
        </h4>
        <ul class="pl-4">
          <li class="mb-1">
            Pause instead of using filler words - silence is powerful
          </li>
          <li class="mb-1">
            Record yourself speaking and practice identifying your common filler words
          </li>
          <li class="mb-1">
            Slow down your speaking pace to give yourself time to think
          </li>
          <li
            v-if="mostUsedFillerWord"
            class="mb-1"
          >
            Focus on reducing your most frequent filler word: "{{ mostUsedFillerWord }}"
          </li>
        </ul>
      </div>
    </v-card>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'

const props = defineProps({
  analysis: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const { analysis, loading } = toRefs(props)

// Extract filler words data from analysis
const fillerWordsData = computed(() => {
  if (!analysis.value || !analysis.value.filler_words) {
    return null
  }
  return analysis.value.filler_words
})

// Sort filler words by count (descending)
const sortedFillerWords = computed(() => {
  if (!fillerWordsData.value || !fillerWordsData.value.filler_word_frequency) {
    return []
  }

  const entries = Object.entries(fillerWordsData.value.filler_word_frequency)
  const sorted = entries.sort((a, b) => b[1] - a[1])

  // Convert back to object
  return Object.fromEntries(sorted)
})

// Get the most used filler word
const mostUsedFillerWord = computed(() => {
  if (!sortedFillerWords.value || Object.keys(sortedFillerWords.value).length === 0) {
    return null
  }
  return Object.keys(sortedFillerWords.value)[0]
})

// Get maximum count for scaling the bars
const maxFillerWordCount = computed(() => {
  if (!sortedFillerWords.value || Object.keys(sortedFillerWords.value).length === 0) {
    return 1
  }
  return Math.max(...Object.values(sortedFillerWords.value))
})

// Calculate grade based on percentage
const grade = computed(() => {
  if (!fillerWordsData.value) return 'N/A'

  const percentage = fillerWordsData.value.filler_word_percentage

  if (percentage <= 2) return 'A+'
  if (percentage <= 4) return 'A'
  if (percentage <= 7) return 'B+'
  if (percentage <= 10) return 'B'
  if (percentage <= 13) return 'C+'
  if (percentage <= 15) return 'C'
  if (percentage <= 20) return 'D+'
  return 'D'
})

// Grade label - providing positive feedback regardless of score
const gradeLabel = computed(() => {
  if (!fillerWordsData.value) return ''

  const percentage = fillerWordsData.value.filler_word_percentage

  if (percentage <= 2) return 'Exceptional!'
  if (percentage <= 4) return 'Excellent!'
  if (percentage <= 7) return 'Very Good!'
  if (percentage <= 10) return 'Good!'
  if (percentage <= 13) return 'Solid Effort!'
  if (percentage <= 15) return 'Room to Grow!'
  if (percentage <= 35) return 'Keep Practicing!'
  return 'You Can Improve!'
})

// Grade background color
const gradeBgColor = computed(() => {
  if (!fillerWordsData.value) return 'surface'

  const percentage = fillerWordsData.value.filler_word_percentage

  if (percentage <= 4) return 'success-lighten-5'
  if (percentage <= 10) return 'info-lighten-5'
  if (percentage <= 20) return 'warning-lighten-5'
  return 'error-lighten-5'
})

// Grade color class for the circle
const gradeColorClass = computed(() => {
  if (!fillerWordsData.value) return 'bg-grey'

  const percentage = fillerWordsData.value.filler_word_percentage

  if (percentage <= 4) return 'bg-success'
  if (percentage <= 10) return 'bg-info'
  if (percentage <= 20) return 'bg-warning'
  return 'bg-error'
})

// Grade icon color (for the percentage and filler word icons)
const gradeIconColor = computed(() => {
  if (!fillerWordsData.value) return 'grey'

  const percentage = fillerWordsData.value.filler_word_percentage

  if (percentage <= 10) return 'success'
  if (percentage <= 20) return 'info'
  if (percentage <= 30) return 'warning'
  return 'error'
})

// Percentage text color class
const percentageColorClass = computed(() => {
  if (!fillerWordsData.value) return ''

  const percentage = fillerWordsData.value.filler_word_percentage

  if (percentage <= 10) return 'text-success'
  if (percentage <= 20) return 'text-info'
  if (percentage <= 30) return 'text-warning'
  return 'text-error'
})

// Icon for filler words
const fillerWordIcon = computed(() => {
  if (!fillerWordsData.value) return 'mdi-message-text'

  const percentage = fillerWordsData.value.filler_word_percentage

  if (percentage <= 10) return 'mdi-check-circle'
  if (percentage <= 20) return 'mdi-information'
  if (percentage <= 30) return 'mdi-alert'
  return 'mdi-alert-circle'
})

// Function to get bar color based on frequency
function getBarColor(count) {
  if (!fillerWordsData.value) return 'grey'

  // Calculate how significant this word is in the total
  const ratio = count / fillerWordsData.value.total_words

  if (ratio < 0.02) return 'success'
  if (ratio < 0.04) return 'info'
  if (ratio < 0.06) return 'warning'
  return 'error'
}
</script>

<style scoped>
.filler-words-analysis {
  margin-bottom: 24px;
  background: var(--v-theme-surface, #fff);
  min-height: 100%;
  width: 100%;
  box-sizing: border-box;
}

@media print {
  .filler-words-analysis {
    background: var(--v-theme-surface, #fff) !important;
    min-height: 100vh !important;
    width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

.grade-circle {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  color: white;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
}

.bg-success {
  background-color: var(--v-theme-success);
}

.bg-info {
  background-color: var(--v-theme-info);
}

.bg-warning {
  background-color: var(--v-theme-warning);
}

.bg-error {
  background-color: var(--v-theme-error);
}

.bg-grey {
  background-color: var(--v-theme-secondary);
}

.grade-container {
  min-width: 80px;
}

.performance-text {
  border-left: 1px solid rgba(var(--v-theme-on-surface), 0.12);
  padding-left: 24px;
}

@media (max-width: 600px) {
  .performance-text {
    border-left: none;
    padding-left: 0;
    margin-top: 16px;
  }

  .d-flex.align-center {
    flex-direction: column;
  }

  .grade-container {
    margin-bottom: 16px;
  }
}
</style>
