<template>
  <div class="app-navigation-container d-flex flex-column" ref="wrapper">
    <!-- Header Toggle -->
    <v-list-item class="pa-3 border-b nav-header" :class="{ 'justify-center': props.rail }">
      <template #prepend>
        <v-btn icon variant="text" size="small" :color="props.rail ? 'primary' : 'default'" @click="emit('update:rail', !props.rail)">
          <v-icon>{{ props.rail ? 'mdi-menu' : 'mdi-menu-open' }}</v-icon>
        </v-btn>
      </template>
      <v-list-item-title v-if="!props.rail" class="text-h6 ms-2">Menu</v-list-item-title>
    </v-list-item>

    <!-- Navigation Items -->
    <div class="nav-content-scrollable">
      <v-list density="compact" nav rounded class="py-1">
        <!-- Dashboard -->
        <v-tooltip
          :disabled="!props.rail"
          location="end"
        >
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-bind="props.rail ? tooltipProps : {}"
              :active="currentRoute === 'dashboard'"
              to="/dashboard"
            >
              <template #prepend>
                <v-icon>mdi-view-dashboard</v-icon>
              </template>
              <v-list-item-title>Dashboard</v-list-item-title>
            </v-list-item>
          </template>
          <span>Dashboard</span>
        </v-tooltip>

        <!-- Interviews -->
        <v-tooltip
          :disabled="!props.rail"
          location="end"
        >
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-bind="props.rail ? tooltipProps : {}"
              :active="currentRoute === 'interviews'"
              to="/interviews"
            >
              <template #prepend>
                <v-icon>mdi-clipboard-text-outline</v-icon>
              </template>
              <v-list-item-title>Interviews</v-list-item-title>
            </v-list-item>
          </template>
          <span>Interviews</span>
        </v-tooltip>

        <!-- Profile -->
        <v-tooltip
          :disabled="!props.rail"
          location="end"
        >
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-bind="props.rail ? tooltipProps : {}"
              :active="currentRoute === 'profile'"
              to="/profile"
            >
              <template #prepend>
                <v-icon>mdi-account-details</v-icon>
              </template>
              <v-list-item-title>Profile</v-list-item-title>
            </v-list-item>
          </template>
          <span>Profile</span>
        </v-tooltip>

        <!-- Activity -->
        <v-tooltip
          :disabled="!props.rail"
          location="end"
        >
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-bind="props.rail ? tooltipProps : {}"
              :active="currentRoute === 'activity'"
              to="/activity"
            >
              <template #prepend>
                <v-icon>mdi-history</v-icon>
              </template>
              <v-list-item-title>Activity</v-list-item-title>
            </v-list-item>
          </template>
          <span>Activity</span>
        </v-tooltip>

        <!-- Rewards -->
        <v-tooltip
          :disabled="!props.rail"
          location="end"
        >
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-bind="props.rail ? tooltipProps : {}"
              :active="currentRoute === 'rewards'"
              to="/rewards"
            >
              <template #prepend>
                <v-icon>mdi-star</v-icon>
              </template>
              <v-list-item-title>Rewards</v-list-item-title>
            </v-list-item>
          </template>
          <span>Rewards</span>
        </v-tooltip>

        <!-- Account -->
        <v-tooltip
          :disabled="!props.rail"
          location="end"
        >
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-bind="props.rail ? tooltipProps : {}"
              :active="currentRoute === 'account'"
              to="/account"
            >
              <template #prepend>
                <v-icon>mdi-account-cog</v-icon>
              </template>
              <v-list-item-title>Account</v-list-item-title>
            </v-list-item>
          </template>
          <span>Account</span>
        </v-tooltip>

        <v-divider class="my-2" />

        <!-- Documentation -->
        <v-tooltip
          :disabled="!props.rail"
          location="end"
        >
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-bind="props.rail ? tooltipProps : {}"
              :active="currentRoute === 'documentation'"
              to="/documentation"
            >
              <template #prepend>
                <v-icon>mdi-book-open-variant</v-icon>
              </template>
              <v-list-item-title>Documentation</v-list-item-title>
            </v-list-item>
          </template>
          <span>Documentation</span>
        </v-tooltip>

        <!-- Support Tickets -->
        <v-tooltip
          :disabled="!props.rail"
          location="end"
        >
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-bind="props.rail ? tooltipProps : {}"
              :active="currentRoute === 'support-tickets'"
              to="/support-tickets"
            >
              <template #prepend>
                <v-icon>mdi-help-circle</v-icon>
              </template>
              <v-list-item-title>
                {{ messagesStore.isSupportStaff ? 'Support Tickets' : 'My Tickets' }}
              </v-list-item-title>
              <template
                v-if="!props.rail"
                #append
              >
                <v-chip
                  v-if="messagesStore.unreadCounts.support > 0"
                  size="small"
                  color="primary"
                >
                  {{ messagesStore.unreadCounts.support }}
                </v-chip>
              </template>
            </v-list-item>
          </template>
          <span>{{ messagesStore.isSupportStaff ? 'Support Tickets' : 'My Tickets' }}</span>
        </v-tooltip>

        <!-- Admin (only visible to admin users) -->
        <template v-if="isUserAdmin">
          <v-divider class="my-2" />

          <v-tooltip
            :disabled="!props.rail"
            location="end"
          >
            <template #activator="{ props: tooltipProps }">
              <v-list-item
                v-bind="props.rail ? tooltipProps : {}"
                :active="currentRoute === 'admin'"
                to="/admin"
              >
                <template #prepend>
                  <v-icon>mdi-shield-account</v-icon>
                </template>
                <v-list-item-title>Admin</v-list-item-title>
              </v-list-item>
            </template>
            <span>Admin</span>
          </v-tooltip>
        </template>
      </v-list>
    </div>

    <!-- Logout Footer -->
    <div class="nav-footer">
      <v-divider />
      <v-list density="compact" nav>
        <v-tooltip :disabled="!props.rail" location="end">
          <template #activator="{ props: tooltipProps }">
            <v-list-item v-bind="props.rail ? tooltipProps : {}" @click="handleLogout">
              <template #prepend>
                <v-icon>mdi-logout</v-icon>
              </template>
              <v-list-item-title>Log Out</v-list-item-title>
            </v-list-item>
          </template>
          <span>Log Out</span>
        </v-tooltip>
      </v-list>
    </div>
  </div>

  <!-- Mobile Menu Bottom Sheet -->
  <v-bottom-sheet
    v-if="mobile"
    v-model="showMobileMenu"
  >
    <v-card>
      <v-card-title class="d-flex align-center justify-space-between">
        <span>Menu</span>
        <v-btn
          icon="mdi-close"
          variant="text"
          @click="showMobileMenu = false"
        />
      </v-card-title>
      <v-card-text>
        <v-list>
          <!-- Dashboard -->
          <v-list-item
            :active="currentRoute === 'dashboard'"
            to="/dashboard"
            @click="showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-view-dashboard</v-icon>
            </template>
            <v-list-item-title>Dashboard</v-list-item-title>
          </v-list-item>

          <!-- Interviews -->
          <v-list-item
            :active="currentRoute === 'interviews'"
            to="/interviews"
            @click="showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-clipboard-text-outline</v-icon>
            </template>
            <v-list-item-title>Interviews</v-list-item-title>
          </v-list-item>

          <!-- Profile -->
          <v-list-item
            :active="currentRoute === 'profile'"
            to="/profile"
            @click="showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-account-details</v-icon>
            </template>
            <v-list-item-title>Profile</v-list-item-title>
          </v-list-item>

          <!-- Activity -->
          <v-list-item
            :active="currentRoute === 'activity'"
            to="/activity"
            @click="showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-history</v-icon>
            </template>
            <v-list-item-title>Activity</v-list-item-title>
          </v-list-item>

          <!-- Rewards -->
          <v-list-item
            :active="currentRoute === 'rewards'"
            to="/rewards"
            @click="showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-star</v-icon>
            </template>
            <v-list-item-title>Rewards</v-list-item-title>
          </v-list-item>

          <!-- Account -->
          <v-list-item
            :active="currentRoute === 'account'"
            to="/account"
            @click="showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-account-cog</v-icon>
            </template>
            <v-list-item-title>Account</v-list-item-title>
          </v-list-item>

          <!-- Documentation -->
          <v-list-item
            :active="currentRoute === 'documentation'"
            to="/documentation"
            @click="showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-book-open-variant</v-icon>
            </template>
            <v-list-item-title>Documentation</v-list-item-title>
          </v-list-item>

          <!-- Support Tickets -->
          <v-list-item
            :active="currentRoute === 'support-tickets'"
            to="/support-tickets"
            @click="showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-help-circle</v-icon>
            </template>
            <v-list-item-title>
              {{ messagesStore.isSupportStaff ? 'Support Tickets' : 'My Tickets' }}
            </v-list-item-title>
            <template #append>
              <v-chip
                v-if="messagesStore.unreadCounts.support > 0"
                size="small"
                color="primary"
              >
                {{ messagesStore.unreadCounts.support }}
              </v-chip>
            </template>
          </v-list-item>

          <!-- Admin (only visible to admin users) -->
          <template v-if="isUserAdmin">
            <v-divider class="my-2" />

            <v-list-item
              :active="currentRoute === 'admin'"
              to="/admin"
              @click="showMobileMenu = false"
            >
              <template #prepend>
                <v-icon>mdi-shield-account</v-icon>
              </template>
              <v-list-item-title>Admin</v-list-item-title>
            </v-list-item>
          </template>

          <v-divider class="my-2" />

          <!-- Logout -->
          <v-list-item @click="handleLogout; showMobileMenu = false">
            <template #prepend>
              <v-icon>mdi-logout</v-icon>
            </template>
            <v-list-item-title>Log Out</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-card-text>
    </v-card>
  </v-bottom-sheet>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useDisplay } from 'vuetify'
import { useRoute, useRouter } from 'vue-router'
import { useMessagesStore } from '@/stores/messages'
import { useNotificationsStore } from '@/stores/notifications'
import { useAuthStore } from '@/stores/auth'

// Props
const props = defineProps({
  rail: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:rail'])

// Stores
const messagesStore = useMessagesStore()
const notificationsStore = useNotificationsStore()
const authStore = useAuthStore()

// Display composable for responsive design
const { mobile } = useDisplay({ mobileBreakpoint: 'md' })

// Route and Router
const route = useRoute()
const router = useRouter()

// State
const showMobileMenu = ref(false)

// Computed
const currentRoute = computed(() => {
  const path = route.path
  if (path === '/dashboard') return 'dashboard'
  if (path === '/interviews' || path.startsWith('/interview/')) return 'interviews'
  if (path === '/profile') return 'profile'
  if (path === '/activity') return 'activity'
  if (path === '/rewards') return 'rewards'
  if (path === '/account') return 'account'
  if (path === '/admin' || path.startsWith('/admin/')) return 'admin'
  if (path === '/documentation') return 'documentation'
  if (path === '/support-tickets' || path.startsWith('/support-tickets/')) return 'support-tickets'
  return ''
})

// Check if user is admin
const isUserAdmin = computed(() => {
  return authStore.user?.user_metadata?.role === 'admin'
})

// Lifecycle
onMounted(() => {
  // Initialize stores
  if (authStore.user) {
    messagesStore.fetchCommunities()
    messagesStore.fetchConversationsEnhanced()
    messagesStore.subscribeToConversations()
  }
  notificationsStore.initialize()

  // Add keyboard shortcut for toggling rail mode
  const handleKeydown = (event) => {
    if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
      event.preventDefault()
      emit('update:rail', !props.rail)
    }
  }

  document.addEventListener('keydown', handleKeydown)

  // Cleanup function will be called in onUnmounted
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
})

onUnmounted(() => {
  messagesStore.unsubscribeAll()
})

// Function to handle logout
const handleLogout = async () => {
  const { success } = await authStore.logout()
  if (success) {
    router.push('/login')
  }
}
</script>

<style scoped>
.border-b {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

/* AppNavigation container styles */
.app-navigation-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Fixed header */
.nav-header {
  flex: 0 0 auto;
}

/* Scrollable content area */
.nav-content-scrollable {
  flex: 1 1 auto;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
}

/* Fixed footer */
.nav-footer {
  flex: 0 0 auto;
  background-color: var(--v-theme-surface);
  border-top: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

/* Style the scrollbar */
.nav-content-scrollable::-webkit-scrollbar {
  width: 6px;
}

.nav-content-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.nav-content-scrollable::-webkit-scrollbar-thumb {
  background-color: rgba(var(--v-theme-on-surface), 0.2);
  border-radius: 3px;
}

.nav-content-scrollable::-webkit-scrollbar-thumb:hover {
  background-color: rgba(var(--v-theme-on-surface), 0.3);
}
</style>
