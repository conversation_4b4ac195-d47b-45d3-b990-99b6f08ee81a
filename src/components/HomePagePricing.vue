<template>
  <div>
    <!-- Error <PERSON> -->
    <v-alert
      v-if="error"
      type="error"
      class="ma-4"
      variant="outlined"
      closable
      @click:close="error = null"
    >
      {{ error }}
    </v-alert>

    <PricingTables
      :loading="loading"
      :selected="selected"
      @purchase="handlePurchase"
    >
      <template #extra-starter-features>
        <v-list-item
          prepend-icon="mdi-account-multiple"
          title="Elite AI Coaching"
          subtitle="Professional-level feedback"
        />
        <v-list-item
          prepend-icon="mdi-file-chart"
          title="Advanced Analytics"
          subtitle="In-depth performance metrics"
        />
        <v-list-item
          prepend-icon="mdi-share-variant"
          title="Unlimited Sharing"
          subtitle="Collaborate with coaches"
        />
        <v-list-item
          prepend-icon="mdi-crown"
          title="Full Interview Simulations"
          subtitle="Complete pageant preparation"
        />
      </template>

      <template #extra-standard-features>
        <v-list-item
          prepend-icon="mdi-account-multiple"
          title="Elite AI Coaching"
          subtitle="Professional-level feedback"
        />
        <v-list-item
          prepend-icon="mdi-file-chart"
          title="Advanced Analytics"
          subtitle="In-depth performance metrics"
        />
        <v-list-item
          prepend-icon="mdi-share-variant"
          title="Unlimited Sharing"
          subtitle="Collaborate with coaches"
        />
        <v-list-item
          prepend-icon="mdi-newspaper-variant"
          title="Current Events"
          subtitle="Real-world topics in your interviews"
          value="current-events"
          color="primary"
          variant="elevated"
        />
        <v-list-item
          prepend-icon="mdi-crown"
          title="Full Interview Simulations"
          subtitle="Complete pageant preparation"
        />
      </template>

      <template #extra-premium-features>
        <v-list-item
          prepend-icon="mdi-account-multiple"
          title="Elite AI Coaching"
          subtitle="Professional-level feedback"
        />
        <v-list-item
          prepend-icon="mdi-file-chart"
          title="Advanced Analytics"
          subtitle="In-depth performance metrics"
        />
        <v-list-item
          prepend-icon="mdi-share-variant"
          title="Unlimited Sharing"
          subtitle="Collaborate with coaches"
        />
        <v-list-item
          prepend-icon="mdi-newspaper-variant"
          title="Current Events"
          subtitle="Real-world topics in your interviews"
          value="current-events"
          color="primary"
          variant="elevated"
        />
        <v-list-item
          prepend-icon="mdi-crown"
          title="Full Interview Simulations"
          subtitle="Complete pageant preparation"
        />
      </template>

      <template #button-text-starter>
        Get Started
      </template>
      <template #button-text-standard>
        Get Started
      </template>
      <template #button-text-premium>
        Get Started
      </template>
    </PricingTables>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { supabase } from '@/plugins/supabase'
import PricingTables from '@/components/PricingTables.vue'

const authStore = useAuthStore()
const error = ref(null)
const loading = ref(false)
const selected = ref(['current-events'])

async function handlePurchase({ plan, minutes }) {
  if (!authStore.isAuthenticated) {
    // Redirect to login
    window.location.href = '/login?mode=signup'
    return
  }

  loading.value = true

  try {
    // Get the current session token
    const { data: { session } } = await supabase.auth.getSession()
    const accessToken = session?.access_token || ''

    // Call the Supabase Edge Function to create a checkout session
    const response = await fetch(
      'https://bvgyhorawikkarjcucxv.supabase.co/functions/v1/create-checkout',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          plan: plan,
          amount: minutes,
          customerEmail: authStore.user.email,
        }),
      }
    )

    const data = await response.json()

    if (data.error) {
      throw new Error(data.error)
    }

    // Redirect to Stripe Checkout
    window.location.href = data.url
  } catch (err) {
    console.error('Error creating checkout session:', err)
    error.value = `Failed to create checkout session: ${err.message}`
  } finally {
    loading.value = false
  }
}
</script>
