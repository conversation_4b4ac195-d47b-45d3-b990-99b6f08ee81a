<template>
  <v-dialog
    v-model="dialogModel"
    max-width="500px"
  >
    <v-card>
      <v-card-title>Redeem Coupon Code</v-card-title>
      <v-card-text>
        <v-alert
          v-if="couponError"
          density="comfortable"
          type="error"
          variant="tonal"
          class="mb-4"
        >
          {{ couponError }}
        </v-alert>
        <v-alert
          v-if="couponSuccess"
          density="comfortable"
          type="success"
          variant="tonal"
          class="mb-4"
        >
          {{ couponSuccess }}
        </v-alert>

        <v-form
          ref="couponForm"
          @submit.prevent="redeemCouponCode"
        >
          <v-text-field
            v-model="couponCode"
            label="Coupon Code"
            variant="outlined"
            placeholder="Enter your coupon code"
            :rules="[v => !!v || 'Coupon code is required']"
            autofocus
            @keyup.enter="redeemCouponCode"
          />
        </v-form>
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-btn
          color="error"
          variant="text"
          @click="cancel"
        >
          CANCEL
        </v-btn>
        <v-btn
          color="success"
          variant="text"
          :loading="redeemingCoupon"
          @click="redeemCouponCode"
        >
          REDEEM
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { supabase } from '@/plugins/supabase'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

const couponCode = ref('')
const couponError = ref('')
const couponSuccess = ref('')
const redeemingCoupon = ref(false)
const couponForm = ref(null)

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'redeemed'])

const dialogModel = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

function cancel() {
  couponCode.value = ''
  couponError.value = ''
  couponSuccess.value = ''
  emit('update:modelValue', false)
}

// Redeem coupon code
async function redeemCouponCode() {
  if (!authStore.user) {
    console.error('No authenticated user found')
    return
  }

  if (!couponCode.value.trim()) {
    couponError.value = 'Please enter a coupon code'
    return
  }

  redeemingCoupon.value = true
  couponError.value = ''
  couponSuccess.value = ''

  try {
    // Get the current session and access token
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      couponError.value = 'Authentication error. Please try logging in again.'
      return
    }

    // Call the edge function directly to handle non-2xx responses
    const response = await fetch(`${supabase.functions.url}/redeem-coupon`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({ couponCode: couponCode.value.trim() })
    })

    // Parse the response body
    const data = await response.json()

    // Check if response was successful
    if (!response.ok) {
      // Extract error message from response for non-2xx codes
      const errorMessage = data?.error || 'Failed to redeem coupon code'
      couponError.value = errorMessage
      return
    }

    // Handle application-level errors in successful responses
    if (data.error) {
      couponError.value = data.error
      return
    }

    // Success! Show the success message and update the display
    couponSuccess.value = data.message || 'Coupon code redeemed successfully!'

    // Emit event to inform parent of successful redemption
    emit('redeemed')

    // Reset the coupon code
    couponCode.value = ''

    // Close the dialog after a short delay
    setTimeout(() => {
      dialogModel.value = false
      couponSuccess.value = ''
    }, 2000)
  } catch (err) {
    console.error('Error redeeming coupon code:', err)
    couponError.value = 'Network error. Please try again.'
  } finally {
    redeemingCoupon.value = false
  }
}
</script>
