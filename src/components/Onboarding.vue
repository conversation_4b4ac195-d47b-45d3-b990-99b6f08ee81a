<template>
  <v-dialog
    v-model="dialog"
    persistent
    max-width="1000px"
    scrollable
  >
    <v-card>
      <v-card-title class="text-h5 pb-1">
        <div class="d-flex align-center justify-space-between w-100 mb-2">
          <div class="d-flex align-center">
            <v-icon
              icon="mdi-account-circle"
              color="primary"
              class="mr-2"
            />
            <span>Welcome to MissInterview.com!</span>
          </div>
          <v-btn
            icon="mdi-close"
            variant="text"
            size="small"
            aria-label="Close"
            @click="dialog = false"
          />
        </div>
        <div class="text-body-2 ml-8">
          Let's set up your profile to get the most out of your experience
        </div>
      </v-card-title>

      <v-divider />

      <v-stepper
        v-model="currentStep"
        class="onboarding-stepper"
        elevation="0"
      >
        <v-stepper-header class="mb-4">
          <template
            v-for="(step, index) in steps"
            :key="step.title"
          >
            <v-stepper-item
              :value="index + 1"
              :complete="currentStep > index + 1"
              editable
              :title="step.title"
              color="primary"
            />
            <v-divider v-if="index < steps.length - 1" />
          </template>
        </v-stepper-header>

        <v-stepper-window v-model="currentStep">
          <!-- Step 1: Name -->
          <v-stepper-window-item :value="1">
            <v-card-text class="pb-0">
              <div class="content-container">
                <v-alert
                  v-if="errors.name"
                  type="error"
                  density="compact"
                  class="mb-4"
                  closable
                  @click:close="errors.name = ''"
                >
                  {{ errors.name }}
                </v-alert>

                <p class="mb-4">
                  Please enter your name. This will help us personalize your interview experience.
                </p>

                <v-row>
                  <v-col
                    cols="12"
                    sm="6"
                  >
                    <div class="d-flex align-center mb-2">
                      <label class="text-subtitle-2 font-weight-medium">First Name</label>
                      <span class="text-error ml-1">*</span>
                    </div>
                    <v-text-field
                      v-model="profileData.first_name"
                      variant="outlined"
                      density="comfortable"
                      :rules="[v => !!v || 'First name is required']"
                      hide-details
                      required
                      autofocus
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    sm="6"
                  >
                    <div class="d-flex align-center mb-2">
                      <label class="text-subtitle-2 font-weight-medium">Last Name</label>
                      <span class="text-error ml-1">*</span>
                    </div>
                    <v-text-field
                      v-model="profileData.last_name"
                      variant="outlined"
                      density="comfortable"
                      :rules="[v => !!v || 'Last name is required']"
                      hide-details
                      required
                    />
                  </v-col>
                </v-row>
              </div>
            </v-card-text>
            <v-divider class="mt-10 mb-4" />
            <v-card-actions>
              <v-spacer />
              <v-btn
                color="primary"
                variant="elevated"
                :disabled="!isNameStepValid"
                @click="nextStep"
              >
                Next
              </v-btn>
            </v-card-actions>
          </v-stepper-window-item>

          <!-- Step 2: Mobile Phone -->
          <v-stepper-window-item :value="2">
            <v-card-text class="pb-0">
              <div class="content-container">
                <div class="d-flex align-center mb-4">
                  <v-icon
                    icon="mdi-phone"
                    color="primary"
                    class="mr-2"
                  />
                  <span class="text-h6">Mobile Phone (Optional)</span>
                </div>

                <div class="d-flex align-center mb-2">
                  <label class="text-subtitle-2 font-weight-medium">Mobile Phone</label>
                  <v-tooltip
                    location="top"
                    max-width="300"
                  >
                    <template #activator="{ props }">
                      <v-icon
                        color="info"
                        size="small"
                        class="ml-2"
                        v-bind="props"
                      >
                        mdi-information-outline
                      </v-icon>
                    </template>
                    <span>Your mobile phone is used to verify your account when calling ************ to practice an interview.</span>
                  </v-tooltip>
                </div>
                <v-text-field
                  v-model="profileData.mobile_phone"
                  variant="outlined"
                  density="comfortable"
                  placeholder="(*************"
                  hide-details
                  class="mb-4"
                  @update:model-value="formatPhoneInput"
                />
              </div>
            </v-card-text>
            <v-divider class="mt-10 mb-4" />
            <v-card-actions class="pt-0">
              <v-btn
                color="grey-darken-1"
                variant="text"
                @click="prevStep"
              >
                Back
              </v-btn>
              <v-spacer />
              <v-btn
                color="secondary"
                variant="text"
                @click="skipStep"
              >
                Skip
              </v-btn>
              <v-btn
                color="primary"
                variant="elevated"
                @click="saveAndNextStep"
              >
                Next
              </v-btn>
            </v-card-actions>
          </v-stepper-window-item>

          <!-- Step 3: Pageant Location -->
          <v-stepper-window-item :value="3">
            <v-card-text class="pb-0">
              <div class="content-container">
                <div class="d-flex align-center mb-4">
                  <v-icon
                    icon="mdi-map-marker"
                    color="primary"
                    class="mr-2"
                  />
                  <span class="text-h6">Pageant Information (Optional)</span>
                </div>

                <div class="mb-4">
                  <div class="d-flex align-center mb-2">
                    <label class="text-subtitle-2 font-weight-medium">Pageant Title</label>
                    <v-tooltip
                      location="top"
                      max-width="300"
                    >
                      <template #activator="{ props }">
                        <v-icon
                          color="info"
                          size="small"
                          class="ml-2"
                          v-bind="props"
                        >
                          mdi-information-outline
                        </v-icon>
                      </template>
                      <span>Enter the title of the pageant you are competing in (e.g., Miss Indiana University)</span>
                    </v-tooltip>
                  </div>
                  <v-text-field
                    v-model="profileData.pageant_title"
                    variant="outlined"
                    density="comfortable"
                    placeholder="e.g., Miss Indiana University"
                    hide-details
                  />
                </div>

                <div class="d-flex align-center mt-4 mb-3">
                  <v-icon
                    color="info"
                    class="mr-2"
                  >
                    mdi-map-marker
                  </v-icon>
                  <span class="text-subtitle-1 font-weight-medium">Pageant Location</span>
                  <v-tooltip
                    location="top"
                    max-width="300"
                  >
                    <template #activator="{ props }">
                      <v-icon
                        color="info"
                        size="small"
                        class="ml-2"
                        v-bind="props"
                      >
                        mdi-information-outline
                      </v-icon>
                    </template>
                    <span>Please enter the city and state/province where your pageant will take place. This helps us customize your interview questions with relevant current events and local issues specific to that region.</span>
                  </v-tooltip>
                </div>

                <v-row>
                  <v-col
                    cols="12"
                    sm="6"
                  >
                    <div class="d-flex align-center mb-2">
                      <label class="text-subtitle-2 font-weight-medium">City</label>
                    </div>
                    <v-text-field
                      v-model="profileData.city"
                      variant="outlined"
                      density="comfortable"
                      hide-details
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    sm="6"
                  >
                    <div class="d-flex align-center mb-2">
                      <label class="text-subtitle-2 font-weight-medium">State/Province</label>
                    </div>
                    <v-text-field
                      v-model="profileData.state"
                      variant="outlined"
                      density="comfortable"
                      hide-details
                    />
                  </v-col>
                </v-row>
              </div>
            </v-card-text>
            <v-divider class="mt-10 mb-4" />
            <v-card-actions class="pt-0">
              <v-btn
                color="grey-darken-1"
                variant="text"
                @click="prevStep"
              >
                Back
              </v-btn>
              <v-spacer />
              <v-btn
                color="secondary"
                variant="text"
                @click="skipStep"
              >
                Skip
              </v-btn>
              <v-btn
                color="primary"
                variant="elevated"
                @click="saveAndNextStep"
              >
                Next
              </v-btn>
            </v-card-actions>
          </v-stepper-window-item>

          <!-- Step 4: Current Events Keywords -->
          <v-stepper-window-item :value="4">
            <v-card-text class="pb-0">
              <div class="content-container">
                <div class="d-flex align-center mb-4">
                  <v-icon
                    icon="mdi-newspaper-variant"
                    color="primary"
                    class="mr-2"
                  />
                  <span class="text-h6">Current Events Keywords (Optional)</span>
                </div>

                <v-combobox
                  v-model="profileData.current_events"
                  label="Current Events Keywords"
                  variant="outlined"
                  chips
                  multiple
                  closable-chips
                  placeholder="Enter keywords and press Enter"
                  hint="Add keywords related to current events relevant to your pageant. Press ENTER after each new keyword."
                  persistent-hint
                  class="mb-4"
                >
                  <template #prepend-inner>
                    <v-icon icon="mdi-tag-multiple" />
                  </template>
                </v-combobox>

                <v-alert
                  type="info"
                  variant="tonal"
                  density="compact"
                  class="mb-4"
                >
                  <v-icon
                    start
                    icon="mdi-information-outline"
                  />
                  These keywords help us generate better current events questions related to your pageant.
                </v-alert>
              </div>
            </v-card-text>
            <v-divider class="mt-10 mb-4" />
            <v-card-actions class="pt-0">
              <v-btn
                color="grey-darken-1"
                variant="text"
                @click="prevStep"
              >
                Back
              </v-btn>
              <v-spacer />
              <v-btn
                color="secondary"
                variant="text"
                @click="skipStep"
              >
                Skip
              </v-btn>
              <v-btn
                color="primary"
                variant="elevated"
                @click="saveAndNextStep"
              >
                Next
              </v-btn>
            </v-card-actions>
          </v-stepper-window-item>

          <!-- Step 5: Resume -->
          <v-stepper-window-item :value="5">
            <v-card-text class="pb-0">
              <div class="content-container">
                <div class="d-flex align-center mb-4">
                  <v-icon
                    icon="mdi-file-document"
                    color="primary"
                    class="mr-2"
                  />
                  <span class="text-h6">Resume (Optional)</span>
                  <v-tooltip
                    location="top"
                    max-width="300"
                  >
                    <template #activator="{ props }">
                      <v-icon
                        color="info"
                        size="small"
                        class="ml-2"
                        v-bind="props"
                      >
                        mdi-information-outline
                      </v-icon>
                    </template>
                    <span>Your resume is used by our AI to generate personalized interview questions, similar to how a real pageant judge would prepare questions based on your background.</span>
                  </v-tooltip>
                </div>

                <DocumentUploader
                  label="Import from document"
                  hint="Upload a PDF, Word Doc, or text file to extract your resume"
                  :loading="processingResumeFile"
                  @text-extracted="handleResumeTextExtracted"
                  @error="handleUploadError"
                  @processing="processingResumeFile = $event"
                />

                <v-textarea
                  v-model="profileData.resume"
                  variant="outlined"
                  rows="6"
                  height="200px"
                  placeholder="Add your resume here..."
                  class="mt-4"
                />
              </div>
            </v-card-text>
            <v-divider class="mt-10 mb-4" />
            <v-card-actions class="pt-0">
              <v-btn
                color="grey-darken-1"
                variant="text"
                @click="prevStep"
              >
                Back
              </v-btn>
              <v-spacer />
              <v-btn
                color="secondary"
                variant="text"
                @click="skipStep"
              >
                Skip
              </v-btn>
              <v-btn
                color="primary"
                variant="elevated"
                @click="saveAndNextStep"
              >
                Next
              </v-btn>
            </v-card-actions>
          </v-stepper-window-item>

          <!-- Step 6: Social Impact Initiative -->
          <v-stepper-window-item :value="6">
            <v-card-text class="pb-0">
              <div class="content-container">
                <div class="d-flex align-center mb-4">
                  <v-icon
                    icon="mdi-hand-heart"
                    color="primary"
                    class="mr-2"
                  />
                  <span class="text-h6">Social Impact Initiative (Optional)</span>
                  <v-tooltip
                    location="top"
                    max-width="300"
                  >
                    <template #activator="{ props }">
                      <v-icon
                        color="info"
                        size="small"
                        class="ml-2"
                        v-bind="props"
                      >
                        mdi-information-outline
                      </v-icon>
                    </template>
                    <span>Your social impact initiative describes the cause you're passionate about and how you plan to make a difference. This helps our AI generate relevant questions about your platform.</span>
                  </v-tooltip>
                </div>

                <DocumentUploader
                  label="Import from document"
                  hint="Upload a PDF, Word Doc, or text file to extract your social impact initiative"
                  :loading="processingInitiativeFile"
                  @text-extracted="handleInitiativeTextExtracted"
                  @error="handleUploadError"
                  @processing="processingInitiativeFile = $event"
                />

                <v-textarea
                  v-model="profileData.social_impact_initiative"
                  variant="outlined"
                  rows="6"
                  height="200px"
                  placeholder="Add your social impact initiative here..."
                  class="mt-4"
                />
              </div>
            </v-card-text>
            <v-divider class="mt-10 mb-4" />
            <v-card-actions class="pt-0">
              <v-btn
                color="grey-darken-1"
                variant="text"
                @click="prevStep"
              >
                Back
              </v-btn>
              <v-spacer />
              <v-btn
                color="secondary"
                variant="text"
                @click="skipStep"
              >
                Skip
              </v-btn>
              <v-btn
                color="primary"
                variant="elevated"
                @click="saveAndNextStep"
              >
                Next
              </v-btn>
            </v-card-actions>
          </v-stepper-window-item>

          <!-- Step 7: Start Interview -->
          <v-stepper-window-item :value="7">
            <v-card-text class="pb-0">
              <div class="content-container">
                <div class="d-flex justify-center align-center mb-8 flex-column">
                  <v-icon
                    icon="mdi-check-circle"
                    color="success"
                    size="64"
                    class="mb-4"
                  />
                  <h2 class="text-h5 text-center">
                    Profile Complete!
                  </h2>
                  <p class="text-center mt-2">
                    Thank you for setting up your profile. You're now ready to start your interview practice.
                  </p>
                </div>
              </div>
            </v-card-text>
            <v-card-actions class="pt-0">
              <v-btn
                color="grey-darken-1"
                variant="text"
                @click="prevStep"
              >
                Back
              </v-btn>
              <v-spacer />
              <v-btn
                color="success"
                variant="elevated"
                @click="finishAndStartInterview"
              >
                Start Interview
              </v-btn>
            </v-card-actions>
          </v-stepper-window-item>
        </v-stepper-window>
      </v-stepper>

      <!-- Loading overlay -->
      <v-overlay
        v-model="saving"
        class="align-center justify-center"
        persistent
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </v-overlay>
    </v-card>

    <!-- Notification snackbar -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.text }}
      <template #actions>
        <v-btn
          variant="text"
          @click="snackbar.show = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </v-dialog>

  <!-- Voice Assistant Dialog -->
  <VoiceAssistantDialog v-model="voiceAssistantDialog" />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { supabase } from '@/plugins/supabase'
import { useAuthStore } from '@/stores/auth'
import DocumentUploader from '@/components/DocumentUploader.vue'
import VoiceAssistantDialog from '@/components/VoiceAssistantDialog.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'completed'])

// Component state
const dialog = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const authStore = useAuthStore()
const voiceAssistantDialog = ref(false)

// Stepper state
const currentStep = ref(1)
const steps = [
  { title: 'Name', required: true },
  { title: 'Mobile', required: false },
  { title: 'Pageant', required: false },
  { title: 'Events', required: false },
  { title: 'Resume', required: false },
  { title: 'Initiative', required: false },
  { title: 'Start', required: false }
]

// Form state
const profileData = ref({
  first_name: '',
  last_name: '',
  mobile_phone: '',
  city: '',
  state: '',
  pageant_title: '',
  current_events: [],
  resume: '',
  social_impact_initiative: ''
})

// Loading states
const saving = ref(false)
const processingResumeFile = ref(false)
const processingInitiativeFile = ref(false)

// Error states
const errors = ref({
  name: '',
  general: ''
})

// Snackbar state
const snackbar = ref({
  show: false,
  text: '',
  color: 'success'
})

// Computed values for validation
const isNameStepValid = computed(() => {
  return profileData.value &&
    !!profileData.value.first_name &&
    !!profileData.value.last_name
})

// Initialize component
onMounted(async () => {
  if (authStore.user?.id) {
    await fetchUserProfile()
  }
})

// Fetch user profile data
async function fetchUserProfile() {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', authStore.user.id)
      .single()

    if (error) throw error

    if (data) {
      // Format phone for display if it exists
      const formattedPhone = data.mobile_phone ? formatPhoneForDisplay(data.mobile_phone) : ''

      profileData.value = {
        first_name: data.first_name || '',
        last_name: data.last_name || '',
        mobile_phone: formattedPhone,
        city: data.city || '',
        state: data.state || '',
        pageant_title: data.pageant_title || '',
        current_events: data.current_events || [],
        resume: data.resume || '',
        social_impact_initiative: data.social_impact_initiative || ''
      }
    }
  } catch (error) {
    console.error('Error fetching user profile:', error)
    showSnackbar('Failed to load your profile data. Please try again.', 'error')
  }
}

// Navigation functions
function nextStep() {
  if (currentStep.value === 1 && !isNameStepValid.value) {
    errors.value.name = 'Please provide both first and last name'
    return
  }

  saveCurrentStep()
}

function prevStep() {
  currentStep.value--
}

function skipStep() {
  currentStep.value++
}

async function saveAndNextStep() {
  await saveCurrentStep()
  currentStep.value++
}

async function saveCurrentStep() {
  if (!authStore.user?.id) {
    showSnackbar('You must be logged in to save your profile', 'error')
    return
  }

  saving.value = true

  try {
    const dataToSave = { ...profileData.value }

    // Process the mobile phone to store only digits
    if (dataToSave.mobile_phone) {
      dataToSave.mobile_phone = dataToSave.mobile_phone.replace(/\D/g, '')
    }

    // Set onboarding_complete to true if required fields are filled
    if (dataToSave.first_name && dataToSave.last_name) {
      dataToSave.onboarding_complete = true
    }

    // First check if a profile already exists
    const { data: existingProfile, error: fetchError } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('user_id', authStore.user.id)
      .single()

    if (fetchError && fetchError.code !== 'PGRST116') {
      // PGRST116 means no rows returned, which is fine for new users
      throw fetchError
    }

    let saveError
    if (existingProfile) {
      // Update existing profile
      const { error } = await supabase
        .from('user_profiles')
        .update(dataToSave)
        .eq('user_id', authStore.user.id)

      saveError = error
    } else {
      // Create new profile
      const { error } = await supabase
        .from('user_profiles')
        .insert({
          user_id: authStore.user.id,
          ...dataToSave
        })

      saveError = error
    }

    if (saveError) throw saveError

    // Special case for first step - increment step after save
    if (currentStep.value === 1) {
      currentStep.value++
    }

    showSnackbar('Profile updated successfully', 'success')
  } catch (error) {
    console.error('Error saving profile data:', error)
    showSnackbar('Failed to save your profile. Please try again.', 'error')
  } finally {
    saving.value = false
  }
}

async function finishAndStartInterview() {
  // Update with onboarding_complete flag
  try {
    saving.value = true

    const dataToSave = { ...profileData.value, onboarding_complete: true }

    // Process the mobile phone to store only digits
    if (dataToSave.mobile_phone) {
      dataToSave.mobile_phone = dataToSave.mobile_phone.replace(/\D/g, '')
    }

    // Update the profile with onboarding_complete set to true
    const { error } = await supabase
      .from('user_profiles')
      .update(dataToSave)
      .eq('user_id', authStore.user.id)

    if (error) throw error

    showSnackbar('Profile completed successfully', 'success')
  } catch (error) {
    console.error('Error completing profile:', error)
    showSnackbar('Failed to complete your profile. Please try again.', 'error')
  } finally {
    saving.value = false
  }

  dialog.value = false
  emit('completed')

  // Open the voice assistant dialog using v-model
  voiceAssistantDialog.value = true
}

// Utility functions
function formatPhoneInput() {
  // Strip all non-numeric characters
  const digitsOnly = profileData.value.mobile_phone.replace(/\D/g, '')

  // Format the phone number as (XXX) XXX-XXXX
  if (digitsOnly.length <= 3) {
    profileData.value.mobile_phone = digitsOnly
  } else if (digitsOnly.length <= 6) {
    profileData.value.mobile_phone = `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3)}`
  } else {
    profileData.value.mobile_phone = `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6, 10)}`
  }
}

function formatPhoneForDisplay(phoneNumber) {
  if (!phoneNumber) return null

  // Format stored phone number for display
  const digitsOnly = phoneNumber.replace(/\D/g, '')
  if (digitsOnly.length === 10) {
    return `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6, 10)}`
  }

  return phoneNumber
}

function showSnackbar(text, color = 'success') {
  snackbar.value = {
    show: true,
    text,
    color
  }
}

// Document uploader handlers
function handleResumeTextExtracted(text) {
  profileData.value.resume = text
  showSnackbar('Resume text extracted successfully')
}

function handleInitiativeTextExtracted(text) {
  profileData.value.social_impact_initiative = text
  showSnackbar('Social impact initiative text extracted successfully')
}

function handleUploadError(error) {
  showSnackbar(error, 'error')
}
</script>

<style scoped>
.onboarding-stepper :deep(.v-stepper-header) {
  flex-wrap: nowrap;
  overflow-x: auto;
  justify-content: space-between;
}

.onboarding-stepper :deep(.v-stepper-item) {
  flex: 0 0 auto;
}

/* Content container styles */
.content-container {
  max-width: 700px;
  margin: 0 auto;
}

@media (max-width: 600px) {
  .content-container {
    max-width: 100%;
  }

  .onboarding-stepper :deep(.v-stepper-item--complete),
  .onboarding-stepper :deep(.v-stepper-item--inactive) {
    display: none;
  }

  .onboarding-stepper :deep(.v-stepper-item) {
    flex: 1;
    justify-content: center;
  }

  .onboarding-stepper :deep(.v-divider) {
    flex: 0 1 20px;
  }
}
</style>
