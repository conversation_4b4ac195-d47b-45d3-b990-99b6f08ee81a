<template>
  <div>
    <v-file-input
      v-model="documentFile"
      :label="props.label"
      :accept="props.accept"
      :prepend-icon="props.prependIcon"
      :loading="processing"
      :hint="props.hint"
      persistent-hint
      class="mb-4"
      variant="solo-filled"
      @update:model-value="handleFileUpload"
    />
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { supabase } from '@/plugins/supabase'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

const props = defineProps({
  label: {
    type: String,
    default: 'Import from document'
  },
  accept: {
    type: String,
    default: '.pdf,.doc,.docx,.txt'
  },
  prependIcon: {
    type: String,
    default: 'mdi-file-document'
  },
  hint: {
    type: String,
    default: 'Upload a PDF, Word Doc, or text file to extract text'
  }
})

const emit = defineEmits(['text-extracted', 'error', 'processing'])

const documentFile = ref(null)
const processing = ref(false)

// Handle document file upload and extract text
async function handleFileUpload() {
  if (!documentFile.value) return

  processing.value = true
  emit('processing', true)

  console.log('Processing file:', documentFile.value.name, 'Type:', documentFile.value.type, 'Size:', documentFile.value.size)

  try {
    const file = documentFile.value

    // For .txt files, we can read directly in the browser
    if (file.name.toLowerCase().endsWith('.txt')) {
      console.log('Processing TXT file directly in browser')
      const text = await file.text()
      emit('text-extracted', text)
      return
    }

    // For PDF files, try to use PDF.js from CDN
    if (file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')) {
      console.log('Processing PDF file with PDF.js in browser')
      try {
        // Dynamically load PDF.js from CDN
        if (!window.pdfjsLib) {
          console.log('Loading PDF.js from CDN')
          // Load PDF.js library
          await new Promise((resolve, reject) => {
            const script = document.createElement('script')
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js'
            script.onload = resolve
            script.onerror = reject
            document.head.appendChild(script)
          })

          // Set up worker
          window.pdfjsLib.GlobalWorkerOptions.workerSrc =
            'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
        }

        const arrayBuffer = await file.arrayBuffer()
        const pdf = await window.pdfjsLib.getDocument({ data: arrayBuffer }).promise

        console.log(`PDF loaded. Number of pages: ${pdf.numPages}`)
        let textParts = []

        for (let i = 1; i <= pdf.numPages; i++) {
          console.log(`Processing page ${i}/${pdf.numPages}`)
          const page = await pdf.getPage(i)
          const textContent = await page.getTextContent()
          const pageText = textContent.items
            .map(item => item.str || '')
            .join(' ')
          textParts.push(pageText)
        }

        const extractedText = textParts.join('\n\n')
        console.log('PDF text extraction complete', extractedText.substring(0, 100) + '...')
        emit('text-extracted', extractedText)
        return
      } catch (pdfError) {
        console.error('PDF.js processing error:', pdfError)
        // Provide a helpful error message to the user
        emit('error', 'Could not process the PDF in the browser. Please try a simpler PDF or manually copy the text.')
        return
      }
    }

    // For Word documents (.docx or .doc), use the Edge Function
    if (file.name.toLowerCase().endsWith('.docx') || file.name.toLowerCase().endsWith('.doc')) {
      console.log('Word document detected, sending to Supabase Edge Function')

      try {
        // Get the current session for auth
        const { data: { session } } = await supabase.auth.getSession()

        if (!session) {
          throw new Error('Authentication required')
        }

        console.log('Got auth session:', {
          hasSession: !!session,
          hasAccessToken: !!session?.access_token,
          tokenStart: session?.access_token ? session.access_token.substring(0, 10) + '...' : 'none'
        })

        // Check if the documents bucket exists and create it if needed
        try {
          const { data: buckets } = await supabase.storage.listBuckets()
          const documentsBucketExists = buckets?.some(bucket => bucket.name === 'documents')

          if (!documentsBucketExists) {
            console.log('Creating documents bucket')
            await supabase.storage.createBucket('documents', {
              public: false,
              fileSizeLimit: 5242880, // 5MB limit
            })
          }
        } catch (bucketError) {
          console.warn('Could not check/create bucket:', bucketError)
          // Continue anyway as the bucket might already exist
        }

        // Upload the file to a temporary storage location
        const timestamp = new Date().getTime()
        const filePath = `temp/${authStore.user.id}/${timestamp}-${file.name}`

        console.log('Uploading document to storage path:', filePath)
        const { error: uploadError } = await supabase.storage
          .from('documents')
          .upload(filePath, file)

        if (uploadError) {
          console.error('Upload error:', uploadError)
          throw new Error(`Upload failed: ${uploadError.message}`)
        }

        console.log('Document uploaded successfully, calling Edge Function')

        // Get a fresh token to ensure it's not expired
        const { data: freshAuthData } = await supabase.auth.getSession()
        const freshToken = freshAuthData?.session?.access_token

        if (!freshToken) {
          throw new Error('Failed to get valid auth token')
        }

        console.log('Got fresh token for Edge Function call')

        // Try to authenticate with direct fetch first to debug
        try {
          console.log(`Testing auth with direct fetch to ${supabase.functions.url}/extract-document-text`)
          const testResponse = await fetch(`${supabase.functions.url}/extract-document-text`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${freshToken}`
            },
            body: JSON.stringify({ test: true })
          })

          console.log('Auth test response status:', testResponse.status)
          const testData = await testResponse.json()
          console.log('Auth test response:', testData)
        } catch (testError) {
          console.warn('Auth test failed (but continuing):', testError)
        }

        // Call the Supabase Edge Function with explicit auth header
        console.log('Invoking Edge Function with supabase.functions.invoke')
        const { data: functionData, error: functionError } = await supabase.functions
          .invoke('extract-document-text', {
            body: { bucketName: 'documents', filePath: filePath },
            headers: {
              Authorization: `Bearer ${freshToken}`
            }
          })

        // Clean up the temporary file regardless of success
        try {
          await supabase.storage
            .from('documents')
            .remove([filePath])
          console.log('Temporary file removed')
        } catch (cleanupError) {
          console.warn('Failed to remove temporary file:', cleanupError)
        }

        if (functionError) {
          console.error('Edge function error:', functionError)
          throw new Error(functionError.message || 'Edge function error')
        }

        if (!functionData) {
          console.error('No function data returned')
          throw new Error('No data returned from edge function')
        }

        if (!functionData.text) {
          console.error('No text in function data:', functionData)
          throw new Error('No text extracted from document')
        }

        // Emit the extracted text
        emit('text-extracted', functionData.text)
        return
      } catch (err) {
        console.error('Error processing document:', err)

        // Provide helpful error messages
        if (err.message?.includes('Authentication required')) {
          emit('error', 'Please sign in to use document processing features')
        } else if (err.message?.includes('Unauthorized') || err.message?.includes('Auth session')) {
          emit('error', 'Authentication error. Please try signing out and back in, then refresh the page.')
        } else if (err.message?.includes('storage/permission_denied')) {
          emit('error', 'Permission denied accessing storage. Check your Supabase RLS policies.')
        } else if (err.message?.toLowerCase().includes('not supported')) {
          emit('error', 'This document format is not supported. Please convert to .docx format.')
        } else {
          emit('error', `Error processing document: ${err.message || 'Unknown error'}`)
        }

        return
      }
    }

    // Fallback for unsupported file types
    emit('error', `Unsupported file type: ${file.name}. Please use PDF, DOCX, or TXT files.`)

  } catch (err) {
    console.error('Error processing document:', err)
    emit('error', `Error processing document: ${err.message || 'Unknown error'}`)
  } finally {
    processing.value = false
    documentFile.value = null
    emit('processing', false)
  }
}
</script>
