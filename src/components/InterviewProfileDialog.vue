<template>
  <v-dialog
    v-model="dialogOpen"
    max-width="900"
  >
    <v-card>
      <v-card-title class="d-flex align-center">
        <v-icon class="mr-2">
          mdi-account-voice
        </v-icon>
        {{ editingProfile ? 'Edit Interview Profile' : 'Add Interview Profile' }}
        <v-spacer />
        <v-btn
          icon
          variant="text"
          @click="close"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider />
      <v-card-text>
        <v-form
          ref="form"
          @submit.prevent="saveProfile"
        >
          <v-text-field
            v-model="formData.title"
            label="Title"
            :rules="[rules.required]"
            required
            variant="outlined"
            class="mb-4"
          />
          <v-textarea
            v-model="formData.prompt"
            label="Prompt"
            :rules="[rules.required]"
            required
            variant="outlined"
            auto-grow
            rows="4"
            class="mb-4"
          />
          <v-select
            v-model="formData.status"
            :items="statusOptions"
            label="Status"
            :rules="[rules.required]"
            required
            variant="outlined"
            class="mb-4"
          />
          <v-alert
            v-if="error"
            type="error"
            class="mb-4"
          >
            {{ error }}
          </v-alert>
        </v-form>
      </v-card-text>
      <v-divider />
      <v-card-actions>
        <v-spacer />
        <v-btn
          variant="text"
          @click="close"
        >
          Cancel
        </v-btn>
        <v-btn
          color="primary"
          :loading="saving"
          @click="saveProfile"
        >
          {{ editingProfile ? 'Save Changes' : 'Add Profile' }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { supabase } from '@/plugins/supabase'

const props = defineProps({
  modelValue: Boolean,
  editingProfile: {
    type: Object,
    default: null
  }
})
const emit = defineEmits(['update:modelValue', 'saved', 'closed'])

const dialogOpen = ref(props.modelValue)
watch(() => props.modelValue, v => { dialogOpen.value = v })
watch(dialogOpen, v => { emit('update:modelValue', v) })

const form = ref(null)
const saving = ref(false)
const error = ref(null)

const statusOptions = [
  { title: 'Active', value: 'active' },
  { title: 'Inactive', value: 'inactive' }
]

const formData = ref({
  title: '',
  prompt: '',
  status: 'active'
})

const rules = {
  required: v => !!v || 'This field is required'
}

watch(
  () => props.editingProfile,
  (profile) => {
    if (profile) {
      formData.value = {
        title: profile.title || '',
        prompt: profile.prompt || '',
        status: profile.status || 'active'
      }
    } else {
      formData.value = {
        title: '',
        prompt: '',
        status: 'active'
      }
    }
    error.value = null
  },
  { immediate: true }
)

function close() {
  dialogOpen.value = false
  emit('closed')
}

async function saveProfile() {
  error.value = null
  if (!form.value?.validate()) return
  saving.value = true
  try {
    if (props.editingProfile) {
      // Update
      const { error: updateError } = await supabase
        .from('interview_profiles')
        .update({
          title: formData.value.title,
          prompt: formData.value.prompt,
          status: formData.value.status
        })
        .eq('id', props.editingProfile.id)
      if (updateError) throw updateError
    } else {
      // Insert
      const { error: insertError } = await supabase
        .from('interview_profiles')
        .insert({
          title: formData.value.title,
          prompt: formData.value.prompt,
          status: formData.value.status
        })
      if (insertError) throw insertError
    }
    emit('saved')
  } catch (err) {
    error.value = err.message || 'Failed to save profile.'
  } finally {
    saving.value = false
  }
}
</script>
