<template>
  <v-dialog
    v-model="dialogModel"
    max-width="900px"
  >
    <v-card>
      <v-card-title class="text-h5 pa-4 d-flex align-center">
        <span>Purchase<span class="d-none d-sm-inline"> Minutes</span></span>
        <v-spacer />
        <v-btn
          variant="text"
          color="primary"
          density="comfortable"
          prepend-icon="mdi-ticket-percent"
          class="mr-2"
          @click="showRedeemCouponDialog = true"
        >
          Redeem Coupon
        </v-btn>
        <v-btn
          icon="mdi-close"
          variant="text"
          @click="$emit('update:modelValue', false)"
        />
      </v-card-title>
      <v-divider />

      <!-- Error Alert -->
      <v-alert
        v-if="error"
        type="error"
        class="ma-4"
        variant="outlined"
        closable
        @click:close="error = null"
      >
        {{ error }}
      </v-alert>

      <v-card-text class="pa-4">
        <PricingTables
          :loading="loading"
          @purchase="handlePurchase"
        />
      </v-card-text>
    </v-card>
  </v-dialog>

  <!-- Redeem coupon dialog -->
  <RedeemCouponDialog
    v-model="showRedeemCouponDialog"
    @redeemed="onCouponRedeemed"
  />
</template>

<script setup>
import { computed, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { supabase } from '@/plugins/supabase'
import PricingTables from '@/components/PricingTables.vue'
import RedeemCouponDialog from '@/components/RedeemCouponDialog.vue'

const authStore = useAuthStore()
const error = ref(null)
const showRedeemCouponDialog = ref(false)

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'purchase', 'coupon-redeemed'])

const dialogModel = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

function onCouponRedeemed() {
  emit('coupon-redeemed')
}

async function handlePurchase({ plan, minutes }) {
  if (!authStore.user?.email) {
    error.value = 'You must be logged in to purchase minutes.'
    return
  }

  emit('purchase', { plan, minutes })

  try {
    // Get the current session token
    const { data: { session } } = await supabase.auth.getSession()
    const accessToken = session?.access_token || ''

    // Call the Supabase Edge Function to create a checkout session
    const response = await fetch(
      'https://bvgyhorawikkarjcucxv.supabase.co/functions/v1/create-checkout',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          plan: plan,
          amount: minutes,
          customerEmail: authStore.user.email,
        }),
      }
    )

    const data = await response.json()

    if (data.error) {
      throw new Error(data.error)
    }

    // Close the dialog
    dialogModel.value = false

    // Redirect to Stripe Checkout
    window.location.href = data.url
  } catch (err) {
    console.error('Error creating checkout session:', err)
    error.value = `Failed to create checkout session: ${err.message}`
  }
}
</script>
