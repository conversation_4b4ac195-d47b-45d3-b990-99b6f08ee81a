<template>
  <v-container
    v-if="!isPortraitMobile"
    class="d-flex align-center justify-center fill-height"
    :class="{ 'mobile-container': isMobile }"
  >
    <v-card
      flat
      class="text-center pa-8"
      :class="{ 'mobile-card': isMobile }"
    >
      <v-icon
        icon="mdi-text-box-outline"
        :size="isMobile ? 48 : 64"
        color="grey-lighten-1"
        class="mb-4"
      />
      <h3 class="text-h6 text-grey-darken-1 mb-2">
        No Interview Selected
      </h3>
      <p class="text-body-1 text-grey-darken-1">
        <span v-if="isLandscape">Tap an interview from the list to view its details</span>
        <span v-else>Select an interview from the list on the left to view its details</span>
      </p>
    </v-card>
  </v-container>
</template>

<script setup>
import { useDisplay } from 'vuetify'
import { computed } from 'vue'

const { mobile, width, height } = useDisplay()

const isMobile = computed(() => mobile.value)

const isLandscape = computed(() => {
  // Use display width/height
  const isLandscapeRatio = width.value > height.value
  // Also check media query for additional accuracy
  const mediaQueryIsLandscape = window.matchMedia('(orientation: landscape)').matches
  return isLandscapeRatio && mediaQueryIsLandscape
})

const isPortraitMobile = computed(() => {
  return mobile.value && !isLandscape.value
})
</script>

<style scoped>
.v-card {
  max-width: 400px;
}

.fill-height {
  height: calc(100vh - 64px - 56px);
}

.mobile-container {
  padding: 16px;
}

.mobile-card {
  padding: 16px !important;
  max-width: 100%;
}

@media (max-height: 450px) and (orientation: landscape) {
  .fill-height {
    height: calc(100vh - 48px - 36px);
  }

  .mobile-card {
    padding: 12px !important;
  }
}

/* Hide the component on mobile portrait orientation */
@media (max-width: 600px) and (orientation: portrait) {
  .v-container {
    display: none !important;
  }
}
</style>
