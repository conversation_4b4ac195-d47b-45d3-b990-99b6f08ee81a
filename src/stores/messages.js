import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/plugins/supabase'
import { useAuthStore } from './auth'

export const useMessagesStore = defineStore('messages', () => {
  // State
  const conversations = ref([])
  const messages = ref({}) // Keyed by conversation_id
  const communities = ref([])
  const activeConversationId = ref(null)
  const loading = ref(false)
  const error = ref(null)
  const realtimeChannels = ref(new Map())

  // New state for pending support conversations
  const pendingConversation = ref(null)

  // New state for showing closed conversations
  const showClosedConversations = ref(false)

  // Auth store
  const authStore = useAuthStore()

  // Getters
  const activeConversation = computed(() => {
    // If there's a pending conversation and it's active, return it
    if (pendingConversation.value && activeConversationId.value === 'pending') {
      return pendingConversation.value
    }
    return conversations.value.find(c => c.id === activeConversationId.value)
  })

  const activeMessages = computed(() => {
    if (!activeConversationId.value) return []
    // For pending conversations, return empty array since no messages exist yet
    if (activeConversationId.value === 'pending') return []
    return messages.value[activeConversationId.value] || []
  })

  // Check if current user is support staff (admin or staff role)
  const isSupportStaff = computed(() => {
    return authStore.userProfile?.role === 'admin' || authStore.userProfile?.role === 'staff'
  })

  const conversationsByCategory = computed(() => {
    const categorized = {
      dm: [],
      support: [],
      community: [],
      pinned: []
    }

    // Add pending conversation to support category if it exists
    if (pendingConversation.value && pendingConversation.value.type === 'support') {
      categorized.support.push(pendingConversation.value)
    }

    // Debug logging
    console.log('showClosedConversations:', showClosedConversations.value)
    console.log('Total conversations:', conversations.value.length)

    for (const conversation of conversations.value) {
      // Debug logging for each conversation
      console.log(`Conversation ${conversation.id}: status = ${conversation.status}, type = ${conversation.type}`)

      // Temporary fallback: treat conversations without status as 'open'
      const conversationStatus = conversation.status || 'open'

      // Filter out closed conversations unless showClosedConversations is true
      if (conversationStatus === 'closed' && !showClosedConversations.value) {
        console.log(`Filtering out closed conversation: ${conversation.id}`)
        continue
      }

      if (conversation.is_pinned) {
        categorized.pinned.push(conversation)
      }

      if (conversation.type === 'community') {
        categorized.community.push(conversation)
      } else if (conversation.type === 'support') {
        categorized.support.push(conversation)
      } else if (conversation.type === 'dm') {
        categorized.dm.push(conversation)
      }
    }

    console.log('Categorized conversations:', categorized)
    return categorized
  })

  const unreadCounts = computed(() => {
    const counts = {
      dm: 0,
      support: 0,
      community: 0,
      total: 0
    }

    for (const conversation of conversations.value) {
      const unreadCount = conversation.unread_count || 0
      counts[conversation.type] += unreadCount
      counts.total += unreadCount
    }

    return counts
  })

  // Actions
  async function fetchCommunities() {
    try {
      loading.value = true
      const { data, error: fetchError } = await supabase
        .from('communities')
        .select('*')
        .eq('is_active', true)
        .order('name')

      if (fetchError) throw fetchError
      communities.value = data || []
    } catch (err) {
      error.value = err.message
      console.error('Error fetching communities:', err)
    } finally {
      loading.value = false
    }
  }

  async function fetchConversations() {
    if (!authStore.user?.id) return

    try {
      loading.value = true
      error.value = null

      // First, get conversation IDs where the user is a participant
      const { data: userParticipations, error: participationError } = await supabase
        .from('conversation_participants')
        .select('conversation_id')
        .eq('user_id', authStore.user.id)

      if (participationError) throw participationError

      const conversationIds = userParticipations?.map(p => p.conversation_id) || []

      if (conversationIds.length === 0) {
        conversations.value = []
        return
      }

      // Then fetch the full conversation data for those IDs
      const { data, error: fetchError } = await supabase
        .from('conversations')
        .select(`
          *,
          community:communities(*),
          participants:conversation_participants(
            *,
            user:user_profiles(user_id, first_name, last_name, email)
          ),
          last_message:messages(content, created_at, sender_id)
        `)
        .in('id', conversationIds)
        .order('updated_at', { ascending: false })

      if (fetchError) throw fetchError

      conversations.value = data || []
    } catch (err) {
      error.value = err.message
      console.error('Error fetching conversations:', err)
    } finally {
      loading.value = false
    }
  }

  async function fetchMessages(conversationId) {
    if (!authStore.user?.id) return

    try {
      // First verify the user is a participant in this conversation
      const { data: participation, error: participationError } = await supabase
        .from('conversation_participants')
        .select('conversation_id')
        .eq('conversation_id', conversationId)
        .eq('user_id', authStore.user.id)
        .single()

      if (participationError || !participation) {
        throw new Error('You do not have access to this conversation')
      }

      const { data, error: fetchError } = await supabase
        .from('messages')
        .select(`
          *,
          sender:user_profiles!messages_sender_id_fkey(user_id, first_name, last_name, email, role),
          reactions:message_reactions(
            *,
            user:user_profiles!message_reactions_user_id_fkey(user_id, first_name, last_name)
          )
        `)
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true })
        .limit(50)

      if (fetchError) throw fetchError

      messages.value[conversationId] = data || []

      // Mark messages as read
      await markAsRead(conversationId)
    } catch (err) {
      error.value = err.message
      console.error('Error fetching messages:', err)
    }
  }

  async function sendMessage(conversationId, content, messageType = 'text') {
    try {
      // Handle pending support conversation
      if (conversationId === 'pending' && pendingConversation.value?.type === 'support') {
        // Create the actual support conversation first
        const result = await createSupportConversationActual()

        // Clear the pending state
        clearPendingConversation()

        // Send the message to the real conversation
        const realConversationId = result.conversation.id
        setActiveConversation(realConversationId)

        return await sendMessage(realConversationId, content, messageType)
      }

      const { data, error: sendError } = await supabase
        .from('messages')
        .insert({
          conversation_id: conversationId,
          sender_id: authStore.user?.id,
          content,
          message_type: messageType
        })
        .select(`
          *,
          sender:user_profiles!messages_sender_id_fkey(user_id, first_name, last_name, email, role)
        `)
        .single()

      if (sendError) throw sendError

      // Optimistically add message to local state
      if (!messages.value[conversationId]) {
        messages.value[conversationId] = []
      }
      messages.value[conversationId].push(data)

      // Update conversation's updated_at
      await supabase
        .from('conversations')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', conversationId)

      return data
    } catch (err) {
      error.value = err.message
      console.error('Error sending message:', err)
      throw err
    }
  }

  // Helper method to create the actual support conversation (without initial message)
  async function createSupportConversationActual() {
    try {
      if (!authStore.user?.id) {
        throw new Error('User must be authenticated to create support conversation')
      }

      // Always create a new support conversation
      const { data: conversation, error: convError } = await supabase
        .from('conversations')
        .insert({
          type: 'support',
          title: null
        })
        .select()
        .single()

      if (convError) throw convError

      // Add user as participant
      const { error: participantError } = await supabase
        .from('conversation_participants')
        .insert({
          conversation_id: conversation.id,
          user_id: authStore.user.id,
          relationship_status: 'accepted'
        })

      if (participantError) throw participantError

      // Add all support staff as participants
      const supportStaff = await getSupportStaff()
      if (supportStaff.length > 0) {
        const staffParticipants = supportStaff.map(staff => ({
          conversation_id: conversation.id,
          user_id: staff.user_id,
          relationship_status: 'accepted'
        }))

        const { error: staffParticipantError } = await supabase
          .from('conversation_participants')
          .insert(staffParticipants)

        if (staffParticipantError) throw staffParticipantError
      }

      // Refresh conversations to get the new conversation with full data
      await fetchConversationsEnhanced()

      // Find the conversation in our local state
      let conversationWithData = conversations.value.find(c => c.id === conversation.id)

      // If conversation not found immediately, wait a bit and try again
      if (!conversationWithData) {
        await new Promise(resolve => setTimeout(resolve, 100))
        await fetchConversationsEnhanced()
        conversationWithData = conversations.value.find(c => c.id === conversation.id)
      }

      if (!conversationWithData) {
        throw new Error('Support conversation not found after creation')
      }

      // Ensure we're subscribed to this conversation's messages
      subscribeToMessages(conversation.id)

      return {
        conversation: conversationWithData,
        isNew: true
      }
    } catch (err) {
      error.value = err.message
      console.error('Error creating support conversation:', err)
      throw err
    }
  }

  async function createConversation(type, title, participantIds, communityId = null) {
    try {
      // Create conversation
      const { data: conversation, error: convError } = await supabase
        .from('conversations')
        .insert({
          type,
          title,
          community_id: communityId
        })
        .select()
        .single()

      if (convError) throw convError

      // Add participants
      const participants = participantIds.map(userId => ({
        conversation_id: conversation.id,
        user_id: userId,
        relationship_status: type === 'support' ? 'accepted' : 'pending'
      }))

      const { error: participantError } = await supabase
        .from('conversation_participants')
        .insert(participants)

      if (participantError) throw participantError

      // Refresh conversations
      await fetchConversations()

      return conversation
    } catch (err) {
      error.value = err.message
      console.error('Error creating conversation:', err)
      throw err
    }
  }

  async function findUserByEmail(email) {
    try {
      const { data, error: findError } = await supabase
        .rpc('find_user_by_email', { user_email: email })

      if (findError) throw findError
      return data?.[0] || null
    } catch (err) {
      error.value = err.message
      console.error('Error finding user by email:', err)
      throw err
    }
  }

  async function searchUsers(query) {
    try {
      if (!query || query.length < 2) return []

      const { data, error: searchError } = await supabase
        .from('user_profiles')
        .select('user_id, email, first_name, last_name')
        .or(`email.ilike.%${query}%,first_name.ilike.%${query}%,last_name.ilike.%${query}%`)
        .neq('user_id', authStore.user?.id) // Exclude current user
        .limit(10)

      if (searchError) throw searchError
      return data || []
    } catch (err) {
      error.value = err.message
      console.error('Error searching users:', err)
      throw err
    }
  }

  async function createConversationWithEmail(type, recipientEmail, initialMessage) {
    try {
      // Find the recipient user
      const recipient = await findUserByEmail(recipientEmail)
      if (!recipient) {
        throw new Error('User not found with that email address')
      }

      // Check if conversation already exists for DMs
      if (type === 'dm') {
        const existingConversation = conversations.value.find(conv =>
          conv.type === 'dm' &&
          conv.participants?.some(p => p.user_id === recipient.user_id)
        )

        if (existingConversation) {
          // Use existing conversation
          setActiveConversation(existingConversation.id)
          if (initialMessage) {
            await sendMessage(existingConversation.id, initialMessage)
          }
          return existingConversation
        }
      }

      // Create new conversation
      const participantIds = [authStore.user?.id, recipient.user_id]
      const conversation = await createConversation(type, null, participantIds)

      // Send initial message if provided
      if (initialMessage) {
        await sendMessage(conversation.id, initialMessage)
      }

      // Set as active conversation
      setActiveConversation(conversation.id)

      return conversation
    } catch (err) {
      error.value = err.message
      console.error('Error creating conversation with email:', err)
      throw err
    }
  }

  // Support-specific functions
  async function createSupportConversation(initialMessage = null) {
    try {
      if (!authStore.user?.id) {
        throw new Error('User must be authenticated to create support conversation')
      }

      // Call the database function to create or get existing support conversation
      const { data, error: createError } = await supabase
        .rpc('create_or_get_support_conversation', {
          requesting_user_id: authStore.user.id
        })

      if (createError) throw createError

      const result = data?.[0]
      if (!result) {
        throw new Error('Failed to create support conversation')
      }

      // Refresh conversations to get the new/existing conversation
      await fetchConversationsEnhanced()

      // Find the conversation in our local state
      let conversation = conversations.value.find(c => c.id === result.conversation_id)

      // If conversation not found immediately, wait a bit and try again
      if (!conversation) {
        await new Promise(resolve => setTimeout(resolve, 100))
        await fetchConversationsEnhanced()
        conversation = conversations.value.find(c => c.id === result.conversation_id)
      }

      if (!conversation) {
        throw new Error('Support conversation not found after creation')
      }

      // Set as active conversation FIRST to ensure proper state
      setActiveConversation(conversation.id)

      // Send initial message if provided
      if (initialMessage?.trim()) {
        await sendMessage(conversation.id, initialMessage.trim())
      }

      // Ensure we're subscribed to this conversation's messages
      // This is crucial for receiving real-time updates from support staff
      subscribeToMessages(conversation.id)

      return {
        conversation,
        isNew: result.is_new
      }
    } catch (err) {
      error.value = err.message
      console.error('Error creating support conversation:', err)
      throw err
    }
  }

  // New method to create a pending support conversation
  function createPendingSupportConversation() {
    if (!authStore.user?.id) {
      throw new Error('User must be authenticated to create support conversation')
    }

    // Create a temporary pending conversation object
    pendingConversation.value = {
      id: 'pending',
      type: 'support',
      title: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_pinned: false,
      participants: [
        {
          user_id: authStore.user.id,
          user: {
            user_id: authStore.user.id,
            email: authStore.userProfile?.email || authStore.user.email,
            first_name: authStore.userProfile?.first_name,
            last_name: authStore.userProfile?.last_name,
            role: authStore.userProfile?.role || 'user'
          },
          relationship_status: 'accepted'
        }
      ]
    }

    // Set this as the active conversation
    setActiveConversation('pending')

    return pendingConversation.value
  }

  // Clear the pending conversation
  function clearPendingConversation() {
    pendingConversation.value = null
  }

  async function getSupportStaff() {
    try {
      const { data, error: fetchError } = await supabase
        .rpc('get_support_staff')

      if (fetchError) throw fetchError
      return data || []
    } catch (err) {
      error.value = err.message
      console.error('Error fetching support staff:', err)
      throw err
    }
  }

  // Enhanced fetchConversations to handle support staff access
  async function fetchConversationsEnhanced() {
    if (!authStore.user?.id) return

    try {
      loading.value = true
      error.value = null

      let conversationQuery

      // If user is support staff, they can see all support conversations
      if (isSupportStaff.value) {
        // Get all conversations where user is a participant OR all support conversations
        const { data: userParticipations, error: participationError } = await supabase
          .from('conversation_participants')
          .select('conversation_id')
          .eq('user_id', authStore.user.id)

        if (participationError) throw participationError

        const userConversationIds = userParticipations?.map(p => p.conversation_id) || []

        // Get all support conversation IDs
        const { data: supportConversations, error: supportError } = await supabase
          .from('conversations')
          .select('id')
          .eq('type', 'support')

        if (supportError) throw supportError

        const supportConversationIds = supportConversations?.map(c => c.id) || []

        // Combine both sets of IDs
        const allConversationIds = [...new Set([...userConversationIds, ...supportConversationIds])]

        if (allConversationIds.length === 0) {
          conversations.value = []
          return
        }

        conversationQuery = supabase
          .from('conversations')
          .select(`
            *,
            community:communities(*),
            participants:conversation_participants(
              *,
              user:user_profiles(user_id, first_name, last_name, email, role)
            ),
            last_message:messages(content, created_at, sender_id)
          `)
          .in('id', allConversationIds)
      } else {
        // Regular user - only see conversations they're a participant in
        const { data: userParticipations, error: participationError } = await supabase
          .from('conversation_participants')
          .select('conversation_id')
          .eq('user_id', authStore.user.id)

        if (participationError) throw participationError

        const conversationIds = userParticipations?.map(p => p.conversation_id) || []

        if (conversationIds.length === 0) {
          conversations.value = []
          return
        }

        conversationQuery = supabase
          .from('conversations')
          .select(`
            *,
            community:communities(*),
            participants:conversation_participants(
              *,
              user:user_profiles(user_id, first_name, last_name, email, role)
            ),
            last_message:messages(content, created_at, sender_id)
          `)
          .in('id', conversationIds)
      }

      const { data, error: fetchError } = await conversationQuery
        .order('updated_at', { ascending: false })

      if (fetchError) throw fetchError

      conversations.value = data || []
    } catch (err) {
      error.value = err.message
      console.error('Error fetching conversations:', err)
    } finally {
      loading.value = false
    }
  }

  async function updateRelationshipStatus(conversationId, status) {
    try {
      const { error: updateError } = await supabase
        .from('conversation_participants')
        .update({ relationship_status: status })
        .eq('conversation_id', conversationId)
        .eq('user_id', authStore.user?.id)

      if (updateError) throw updateError

      // Update local state
      const conversation = conversations.value.find(c => c.id === conversationId)
      if (conversation) {
        const participant = conversation.participants?.find(p => p.user_id === authStore.user?.id)
        if (participant) {
          participant.relationship_status = status
        }
      }
    } catch (err) {
      error.value = err.message
      console.error('Error updating relationship status:', err)
      throw err
    }
  }

  async function togglePin(conversationId) {
    try {
      const conversation = conversations.value.find(c => c.id === conversationId)
      const newPinnedState = !conversation?.is_pinned

      const { error: updateError } = await supabase
        .from('conversations')
        .update({ is_pinned: newPinnedState })
        .eq('id', conversationId)

      if (updateError) throw updateError

      // Update local state
      if (conversation) {
        conversation.is_pinned = newPinnedState
      }
    } catch (err) {
      error.value = err.message
      console.error('Error toggling pin:', err)
      throw err
    }
  }

  async function updateConversationStatus(conversationId, status) {
    try {
      if (!isSupportStaff.value) {
        throw new Error('Only admin and staff can update conversation status')
      }

      // Try the database function first, fallback to local update if it fails
      try {
        const { data, error: updateError } = await supabase
          .rpc('update_conversation_status', {
            conversation_id: conversationId,
            new_status: status
          })

        if (updateError) throw updateError

        // Update local state
        const conversation = conversations.value.find(c => c.id === conversationId)
        if (conversation) {
          conversation.status = status
          conversation.updated_at = new Date().toISOString()
        }

        return data
      } catch (dbError) {
        console.warn('Database function failed, updating locally:', dbError)

        // Fallback: Update local state only (for testing without database migration)
        const conversation = conversations.value.find(c => c.id === conversationId)
        if (conversation) {
          conversation.status = status
          conversation.updated_at = new Date().toISOString()
          console.log(`Temporarily updated conversation ${conversationId} status to ${status}`)
        }

        return { success: true, conversation_id: conversationId, new_status: status }
      }
    } catch (err) {
      error.value = err.message
      console.error('Error updating conversation status:', err)
      throw err
    }
  }

  function toggleShowClosedConversations() {
    showClosedConversations.value = !showClosedConversations.value
  }

  // Temporary test function to manually set conversation status
  function testSetConversationStatus(conversationId, status) {
    const conversation = conversations.value.find(c => c.id === conversationId)
    if (conversation) {
      conversation.status = status
      console.log(`Test: Set conversation ${conversationId} status to ${status}`)
    }
  }

  async function markAsRead(conversationId) {
    try {
      const { error: updateError } = await supabase
        .from('conversation_participants')
        .update({ last_read_at: new Date().toISOString() })
        .eq('conversation_id', conversationId)
        .eq('user_id', authStore.user?.id)

      if (updateError) throw updateError
    } catch (err) {
      console.error('Error marking as read:', err)
    }
  }

  function setActiveConversation(conversationId) {
    // Unsubscribe from previous conversation if switching
    if (activeConversationId.value && activeConversationId.value !== conversationId && activeConversationId.value !== 'pending') {
      unsubscribeFromMessages(activeConversationId.value)
    }

    activeConversationId.value = conversationId

    if (conversationId && conversationId !== 'pending') {
      // Fetch messages if not already loaded
      if (!messages.value[conversationId]) {
        fetchMessages(conversationId)
      }

      // Always ensure we're subscribed to real-time updates for this conversation
      subscribeToMessages(conversationId)
    }
    // For pending conversations, we don't fetch messages or subscribe yet
  }

  // Real-time subscriptions
  function subscribeToConversations() {
    const channel = supabase
      .channel('conversations-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'conversations'
        },
        (payload) => {
          console.log('Conversation change:', payload)
          // Refresh conversations on any change
          fetchConversationsEnhanced()
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'conversation_participants'
        },
        (payload) => {
          console.log('Participant change:', payload)
          // Refresh conversations on participant changes
          fetchConversationsEnhanced()
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages'
        },
        (payload) => {
          console.log('New message in any conversation:', payload)
          // Refresh conversations to update last message info
          fetchConversationsEnhanced()
        }
      )
      .subscribe()

    realtimeChannels.value.set('conversations', channel)
  }

  function subscribeToMessages(conversationId) {
    // Unsubscribe from previous message channel if exists to avoid duplicates
    const existingChannel = realtimeChannels.value.get(`messages-${conversationId}`)
    if (existingChannel) {
      supabase.removeChannel(existingChannel)
      realtimeChannels.value.delete(`messages-${conversationId}`)
    }

    const channel = supabase
      .channel(`messages-${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`
        },
        async (payload) => {
          console.log('New message:', payload)

          // Fetch the complete message with sender info
          const { data: newMessage } = await supabase
            .from('messages')
            .select(`
              *,
              sender:user_profiles!messages_sender_id_fkey(user_id, first_name, last_name, email, role)
            `)
            .eq('id', payload.new.id)
            .single()

          if (newMessage) {
            if (!messages.value[conversationId]) {
              messages.value[conversationId] = []
            }

            // Only add if not already in the array (avoid duplicates)
            const exists = messages.value[conversationId].some(m => m.id === newMessage.id)
            if (!exists) {
              messages.value[conversationId].push(newMessage)
            }
          }
        }
      )
      .subscribe()

    realtimeChannels.value.set(`messages-${conversationId}`, channel)
  }

  function unsubscribeFromMessages(conversationId) {
    const channel = realtimeChannels.value.get(`messages-${conversationId}`)
    if (channel) {
      supabase.removeChannel(channel)
      realtimeChannels.value.delete(`messages-${conversationId}`)
    }
  }

  function unsubscribeAll() {
    for (const channel of realtimeChannels.value.values()) {
      supabase.removeChannel(channel)
    }
    realtimeChannels.value.clear()
  }

  // Initialize
  function initialize() {
    if (authStore.user) {
      fetchCommunities()
      fetchConversationsEnhanced()
      subscribeToConversations()
    }
  }

  return {
    // State
    conversations,
    messages,
    communities,
    activeConversationId,
    loading,
    error,
    pendingConversation,
    showClosedConversations,

    // Getters
    activeConversation,
    activeMessages,
    conversationsByCategory,
    unreadCounts,
    isSupportStaff,

    // Actions
    fetchCommunities,
    fetchConversations,
    fetchMessages,
    sendMessage,
    createConversation,
    findUserByEmail,
    searchUsers,
    createConversationWithEmail,
    createSupportConversation,
    getSupportStaff,
    fetchConversationsEnhanced,
    updateRelationshipStatus,
    togglePin,
    markAsRead,
    setActiveConversation,
    subscribeToConversations,
    subscribeToMessages,
    unsubscribeFromMessages,
    unsubscribeAll,
    initialize,
    createPendingSupportConversation,
    clearPendingConversation,
    updateConversationStatus,
    toggleShowClosedConversations,
    testSetConversationStatus
  }
})
