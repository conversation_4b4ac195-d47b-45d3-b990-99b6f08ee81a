import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/plugins/supabase'

export const useMinutesStore = defineStore('minutes', () => {
  // State
  const userMinutesRecords = ref([])
  let minutesSubscription = null

  // Computed
  const balance = computed(() => {
    if (!userMinutesRecords.value.length) return 0

    const total = userMinutesRecords.value.reduce((total, record) => {
      let minutes = typeof record.minutes === 'string'
        ? parseFloat(record.minutes)
        : Number(record.minutes)

      if (isNaN(minutes)) {
        minutes = 0
      }

      return total + minutes
    }, 0)

    // Ensure we don't show negative balance
    return Math.max(0, total)
  })

  const formattedBalance = computed(() => {
    const minutes = Math.floor(balance.value)
    return `${minutes}`
    // const seconds = Math.round((balance.value - minutes) * 60)
    // return `${minutes}:${seconds.toString().padStart(2, '0')}`
  })

  // Actions
  async function fetchUserMinutes(userId) {
    if (!userId) return

    try {
      const { data, error } = await supabase
        .from('user_minutes')
        .select('*')
        .eq('user_id', userId)

      if (error) {
        console.error('Error fetching user minutes:', error)
        return
      }

      userMinutesRecords.value = data
    } catch (error) {
      console.error('Error in fetchUserMinutes:', error)
    }
  }

  function subscribeToUserMinutes(userId) {
    if (!userId || minutesSubscription) return

    // Create new subscription
    minutesSubscription = supabase
      .channel('user_minutes_changes')
      .on(
        'postgres_changes',
        {
          event: '*', // Listen for all events
          schema: 'public',
          table: 'user_minutes',
          filter: `user_id=eq.${userId}`
        },
        async () => {
          console.log('===> User minutes updated')
          // Refetch all user minutes data when any change occurs
          await fetchUserMinutes(userId)
        }
      )
      .subscribe((status) => {
        console.log('Minutes subscription status:', status)
      })
  }

  function unsubscribeFromMinutes() {
    if (minutesSubscription) {
      minutesSubscription.unsubscribe()
      minutesSubscription = null
    }
    userMinutesRecords.value = []
  }

  return {
    // State
    userMinutesRecords,
    // Computed
    balance,
    formattedBalance,
    // Actions
    fetchUserMinutes,
    subscribeToUserMinutes,
    unsubscribeFromMinutes
  }
})
