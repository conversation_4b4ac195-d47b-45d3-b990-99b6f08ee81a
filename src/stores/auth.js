// Utilities
import { defineStore } from 'pinia'
import { supabase } from '@/plugins/supabase'
import { ref, computed } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const userProfile = ref(null)
  const loading = ref(false)
  const error = ref(null)
  const isProd = window.location.hostname === 'missinterview.com'

  const isAuthenticated = computed(() => !!user.value)

  // Check if user is admin based on profile role
  const isAdmin = computed(() => userProfile.value?.role === 'admin')

  // Get user avatar URL from various sources
  const getUserAvatar = computed(() => (userId = null) => {
    const targetUser = userId === user.value?.id || !userId ? user.value : null

    if (!targetUser) return null

    // Try to get avatar from user_metadata (OAuth providers like Google, Facebook)
    if (targetUser.user_metadata?.avatar_url) {
      return targetUser.user_metadata.avatar_url
    }

    // Try to get avatar from identities (OAuth provider data)
    if (targetUser.identities && targetUser.identities.length > 0) {
      for (const identity of targetUser.identities) {
        if (identity.identity_data?.avatar_url) {
          return identity.identity_data.avatar_url
        }
        if (identity.identity_data?.picture) {
          return identity.identity_data.picture
        }
      }
    }

    return null
  })

  // Generate user initials for avatar fallback
  const getUserInitials = computed(() => (userData) => {
    if (!userData) return '?'

    // Try to get name from user_metadata first
    const fullName = userData.user_metadata?.full_name
    const firstName = userData.user_metadata?.first_name || (fullName ? fullName.split(' ')[0] : null)
    const lastName = userData.user_metadata?.last_name || (fullName ? fullName.split(' ')[1] : null)

    if (firstName) {
      return lastName ? `${firstName[0]}${lastName[0]}`.toUpperCase() : firstName[0].toUpperCase()
    }

    // Try to get name from identities
    if (userData.identities && userData.identities.length > 0) {
      for (const identity of userData.identities) {
        const identityFullName = identity.identity_data?.full_name || identity.identity_data?.name
        if (identityFullName) {
          const nameParts = identityFullName.split(' ')
          return nameParts.length > 1
            ? `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}`.toUpperCase()
            : nameParts[0][0].toUpperCase()
        }
      }
    }

    // Fallback to email first letter
    if (userData.email) {
      return userData.email[0].toUpperCase()
    }

    return '?'
  })

  // Get avatar for any user by their user data
  function getUserAvatarFromData(userData) {
    if (!userData) return null

    // Try user_metadata first
    if (userData.user_metadata?.avatar_url) {
      return userData.user_metadata.avatar_url
    }

    // Try identities
    if (userData.identities && userData.identities.length > 0) {
      for (const identity of userData.identities) {
        if (identity.identity_data?.avatar_url) {
          return identity.identity_data.avatar_url
        }
        if (identity.identity_data?.picture) {
          return identity.identity_data.picture
        }
      }
    }

    return null
  }

  // Fetch user profile from user_profiles table
  async function fetchUserProfile() {
    if (!user.value?.id) return null;

    try {
      const { data, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.value.id)
        .single();

      if (profileError) throw profileError;

      userProfile.value = data;
      return data;
    } catch (err) {
      console.error('Error fetching user profile:', err);
      return null;
    }
  }

  // Initialize the store with the current session
  async function initialize() {
    loading.value = true
    error.value = null

    try {
      // Get the current session
      const { data, error: sessionError } = await supabase.auth.getSession()

      if (sessionError) {
        throw sessionError
      }

      if (data?.session) {
        user.value = data.session.user
        // Fetch the user profile after setting the user
        await fetchUserProfile();
        return { success: true, user: user.value }
      } else {
        // No active session found
        user.value = null
        userProfile.value = null
        return { success: false, reason: 'no-session' }
      }
    } catch (err) {
      console.error('Error initializing auth store:', err)
      error.value = 'Failed to initialize authentication'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  // Login with email and password
  async function login(email, password) {
    loading.value = true
    error.value = null

    try {
      const response = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (response.error) throw response.error

      // Update user from session
      if (response.data.session) {
        user.value = response.data.session.user
        // Fetch user profile after login
        await fetchUserProfile();
      }

      return { success: true }
    } catch (err) {
      console.error('Login error:', err)
      error.value = err.message || 'Failed to login'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  // Login/Signup with Google OAuth
  // This function works for both login and registration - if a user doesn't exist yet,
  // Supabase will automatically create a new account for them using their Google profile
  async function loginWithGoogle() {
    loading.value = true
    error.value = null

    try {
      const { error: authError } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
          redirectTo: isProd
            ? 'https://missinterview.com/login'
            : `${window.location.origin}/login`
        }
      })

      if (authError) throw authError

      return { success: true }
    } catch (err) {
      console.error('Google authentication error:', err)
      error.value = err.message || 'Failed to authenticate with Google'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  // Login/Signup with Facebook OAuth
  // This function works for both login and registration - if a user doesn't exist yet,
  // Supabase will automatically create a new account for them using their Facebook profile
  async function loginWithFacebook() {
    loading.value = true
    error.value = null

    try {
      const { error: authError } = await supabase.auth.signInWithOAuth({
        provider: 'facebook',
        options: {
          redirectTo: isProd
            ? 'https://missinterview.com/login'
            : `${window.location.origin}/login`
        }
      })

      if (authError) throw authError

      return { success: true }
    } catch (err) {
      console.error('Facebook authentication error:', err)
      error.value = err.message || 'Failed to authenticate with Facebook'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  // Sign up with email and password
  // Updated to accept user metadata such as first_name and last_name
  async function signUp(email, password, userData = {}) {
    loading.value = true
    error.value = null

    try {
      const { error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData,
          emailRedirectTo: isProd
            ? 'https://missinterview.com/login'
            : `${window.location.origin}/login`
        }
      })

      if (authError) throw authError

      return { success: true, message: 'Please check your email for confirmation' }
    } catch (err) {
      console.error('Signup error:', err)
      error.value = err.message || 'Failed to sign up'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  // Logout
  async function logout() {
    loading.value = true
    error.value = null

    try {
      // Check if there's a current session/user before attempting to sign out
      const { data: { session } } = await supabase.auth.getSession()

      if (session) {
        // Only attempt to sign out if there's a valid session
        const { error: authError } = await supabase.auth.signOut()
        if (authError) throw authError
      }

      // Always clear local state regardless of whether signOut succeeded
      user.value = null
      userProfile.value = null
      return { success: true }
    } catch (err) {
      console.error('Logout error:', err)

      // If it's an AuthSessionMissingError, treat it as successful logout
      // since the user is effectively already logged out
      if (err.message && err.message.includes('Auth session missing')) {
        user.value = null
        userProfile.value = null
        return { success: true }
      }

      error.value = err.message || 'Failed to logout'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  // Set up auth state change listener
  supabase.auth.onAuthStateChange((event, session) => {
    if (event === 'SIGNED_IN' && session) {
      user.value = session.user
      // Fetch user profile when signed in
      fetchUserProfile()
    } else if (event === 'SIGNED_OUT') {
      user.value = null
      userProfile.value = null
    } else if (event === 'TOKEN_REFRESHED' && session) {
      // Update the user when the token is refreshed
      user.value = session.user
    }
  })

  // Reset password
  async function resetPassword(email) {
    loading.value = true
    error.value = null

    try {
      const { error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: isProd
          ? 'https://missinterview.com/reset-password'
          : `${window.location.origin}/reset-password`
      })

      if (resetError) throw resetError

      return { success: true, message: 'Password reset instructions sent to your email' }
    } catch (err) {
      console.error('Password reset error:', err)
      error.value = err.message || 'Failed to send password reset email'
      return { success: false, error: error.value }
    } finally {
      loading.value = false
    }
  }

  // Call initialize once when the store is created
  // This ensures we check for an existing session immediately
  initialize()

  return {
    user,
    userProfile,
    loading,
    error,
    isAuthenticated,
    isAdmin,
    getUserAvatar,
    getUserInitials,
    getUserAvatarFromData,
    fetchUserProfile,
    initialize,
    login,
    loginWithGoogle,
    loginWithFacebook,
    signUp,
    logout,
    resetPassword
  }
})
