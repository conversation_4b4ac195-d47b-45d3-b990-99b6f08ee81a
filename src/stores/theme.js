// Utilities
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// List of available themes
export const availableThemes = [
  { name: 'default', display: 'Default (Rose Pink)' },
  { name: 'roseQuartz', display: 'Rose Quartz' },
  { name: 'lavenderDream', display: 'Lavender Dream' },
  { name: 'peachBlossom', display: 'Peach Blossom' },
  { name: 'mintCream', display: 'Mint Cream' },
  { name: 'sunsetGlow', display: 'Sunset Glow' },
  { name: 'corporateBlue', display: 'Corporate Blue' },
  { name: 'midnightNavy', display: 'Midnight Navy' },
  { name: 'emeraldMist', display: 'Emerald Mist' },
  { name: 'graphiteGray', display: 'Graphite Gray' },
  { name: 'onyxNight', display: 'Onyx Night' },
]

export const useThemeStore = defineStore('theme', () => {
  // Initialize theme name and mode from localStorage or default
  const themeName = ref(localStorage.getItem('themeName') || 'default')
  const mode = ref(localStorage.getItem('themeMode') || 'light')

  // Computed theme string for Vuetify (e.g., 'roseQuartz-light')
  const theme = computed(() => `${themeName.value}-${mode.value}`)

  function setThemeName(newName) {
    themeName.value = newName
    localStorage.setItem('themeName', newName)
  }

  function setMode(newMode) {
    mode.value = newMode
    localStorage.setItem('themeMode', newMode)
  }

  function toggleMode() {
    mode.value = mode.value === 'light' ? 'dark' : 'light'
    localStorage.setItem('themeMode', mode.value)
  }

  // Get theme parameters as URLSearchParams string
  function getThemeParams(additionalParams = {}) {
    const params = new URLSearchParams({
      theme: themeName.value,
      mode: mode.value,
      ...additionalParams
    })
    return params.toString()
  }

  return {
    themeName,
    mode,
    theme,
    setThemeName,
    setMode,
    toggleMode,
    getThemeParams,
    availableThemes
  }
})
