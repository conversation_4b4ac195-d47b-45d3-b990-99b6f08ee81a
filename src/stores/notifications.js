import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/plugins/supabase'
import { useAuthStore } from './auth'

export const useNotificationsStore = defineStore('notifications', () => {
  // State
  const preferences = ref([])
  const liveNotifications = ref([])
  const loading = ref(false)
  const error = ref(null)

  // Auth store
  const authStore = useAuthStore()

  // Getters
  const notificationsByType = computed(() => {
    const grouped = {
      dm: [],
      support: [],
      community: [],
      mentions: []
    }

    for (const notification of liveNotifications.value) {
      if (grouped[notification.type]) {
        grouped[notification.type].push(notification)
      }
    }

    return grouped
  })

  const unreadCount = computed(() => {
    return liveNotifications.value.filter(n => !n.read).length
  })

  const isNotificationEnabled = computed(() => {
    return (type, communityId = null) => {
      const pref = preferences.value.find(p =>
        p.notification_type === type &&
        (communityId ? p.community_id === communityId : !p.community_id)
      )
      return pref ? pref.is_enabled : true // Default to enabled
    }
  })

  // Actions
  async function fetchPreferences() {
    try {
      loading.value = true
      const { data, error: fetchError } = await supabase
        .from('user_notification_preferences')
        .select('*')
        .eq('user_id', authStore.user?.id)

      if (fetchError) throw fetchError
      preferences.value = data || []
    } catch (err) {
      error.value = err.message
      console.error('Error fetching notification preferences:', err)
    } finally {
      loading.value = false
    }
  }

  async function updatePreference(type, enabled, communityId = null) {
    try {
      const existingPref = preferences.value.find(p =>
        p.notification_type === type &&
        (communityId ? p.community_id === communityId : !p.community_id)
      )

      if (existingPref) {
        // Update existing preference
        const { error: updateError } = await supabase
          .from('user_notification_preferences')
          .update({ is_enabled: enabled })
          .eq('id', existingPref.id)

        if (updateError) throw updateError

        existingPref.is_enabled = enabled
      } else {
        // Create new preference
        const { data, error: insertError } = await supabase
          .from('user_notification_preferences')
          .insert({
            user_id: authStore.user?.id,
            notification_type: type,
            community_id: communityId,
            is_enabled: enabled
          })
          .select()
          .single()

        if (insertError) throw insertError
        preferences.value.push(data)
      }
    } catch (err) {
      error.value = err.message
      console.error('Error updating notification preference:', err)
      throw err
    }
  }

  function addLiveNotification(notification) {
    // Check if notifications are enabled for this type/community
    const enabled = isNotificationEnabled.value(
      notification.type,
      notification.community_id
    )

    if (enabled) {
      liveNotifications.value.unshift({
        id: Date.now() + Math.random(),
        ...notification,
        read: false,
        timestamp: new Date()
      })

      // Keep only last 50 notifications
      if (liveNotifications.value.length > 50) {
        liveNotifications.value = liveNotifications.value.slice(0, 50)
      }
    }
  }

  function markAsRead(notificationId) {
    const notification = liveNotifications.value.find(n => n.id === notificationId)
    if (notification) {
      notification.read = true
    }
  }

  function markAllAsRead() {
    for (const notification of liveNotifications.value) {
      notification.read = true
    }
  }

  function clearNotifications() {
    liveNotifications.value = []
  }

  function removeNotification(notificationId) {
    const index = liveNotifications.value.findIndex(n => n.id === notificationId)
    if (index > -1) {
      liveNotifications.value.splice(index, 1)
    }
  }

  // Initialize
  function initialize() {
    if (authStore.user) {
      fetchPreferences()
    }
  }

  return {
    // State
    preferences,
    liveNotifications,
    loading,
    error,

    // Getters
    notificationsByType,
    unreadCount,
    isNotificationEnabled,

    // Actions
    fetchPreferences,
    updatePreference,
    addLiveNotification,
    markAsRead,
    markAllAsRead,
    clearNotifications,
    removeNotification,
    initialize
  }
})
