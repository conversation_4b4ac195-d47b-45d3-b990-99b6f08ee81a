<template>
  <div class="support-layout">
    <!-- Left Sidebar - Navigation Drawer with Rail Mode -->
    <v-navigation-drawer
      v-model="drawerOpen"
      :rail="rail"
      :permanent="!mobile"
      :temporary="mobile"
      app
      location="left"
      :width="240"
      :rail-width="56"
      :mobile-breakpoint="900"
      elevation="0"
      class="border-e d-none d-md-flex"
    >
      <!-- Header -->
      <v-list-item class="pa-3 border-b" :class="{ 'justify-center': rail }">
        <template #prepend>
          <v-btn
            icon
            variant="text"
            size="small"
            @click="rail = !rail"
            :color="rail ? 'primary' : 'default'"
          >
            <v-icon>{{ rail ? 'mdi-menu' : 'mdi-menu-open' }}</v-icon>
          </v-btn>
        </template>
        <v-list-item-title v-if="!rail" class="text-h6 ms-2">Support</v-list-item-title>
        <template #append v-if="!rail && selectedCategory === 'tickets'">
          <v-btn
            icon="mdi-plus"
            size="small"
            variant="text"
            @click="showNewMessageInterface = true"
          />
        </template>
      </v-list-item>

      <!-- Categories -->
      <v-list density="compact" nav rounded>
        <!-- Documentation -->
        <v-tooltip :disabled="!rail" location="end">
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-bind="rail ? tooltipProps : {}"
              :active="selectedCategory === 'documentation'"
              @click="selectCategory('documentation')"
            >
              <template #prepend>
                <v-icon>mdi-book-open-variant</v-icon>
              </template>
              <v-list-item-title>Documentation</v-list-item-title>
            </v-list-item>
          </template>
          <span>Documentation</span>
        </v-tooltip>

        <!-- Support Tickets -->
        <v-tooltip :disabled="!rail" location="end">
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-bind="rail ? tooltipProps : {}"
              :active="selectedCategory === 'tickets'"
              @click="selectCategory('tickets')"
            >
              <template #prepend>
                <v-icon>mdi-help-circle</v-icon>
              </template>
              <v-list-item-title>
                {{ messagesStore.isSupportStaff ? 'Support Tickets' : 'My Tickets' }}
              </v-list-item-title>
              <template #append v-if="!rail">
                <v-chip
                  v-if="messagesStore.unreadCounts.support > 0"
                  size="small"
                  color="primary"
                >
                  {{ messagesStore.unreadCounts.support }}
                </v-chip>
              </template>
            </v-list-item>
          </template>
          <span>{{ messagesStore.isSupportStaff ? 'Support Tickets' : 'My Tickets' }}</span>
        </v-tooltip>
      </v-list>
    </v-navigation-drawer>

    <!-- Main Content Area -->
    <div class="content-area">
      <div class="d-flex fill-height">
        <!-- Middle Column - Conversation List (only for tickets) -->
        <div
          v-if="selectedCategory === 'tickets' && (!mobile || !messagesStore.activeConversationId)"
          class="conversation-list-column border-e"
          :class="{
            'flex-shrink-0': !mobile,
            'w-100': mobile
          }"
          :style="!mobile ? 'width: 320px;' : ''"
        >
          <v-card flat class="fill-height d-flex flex-column">
            <!-- Mobile Header with Menu and New Message -->
            <div v-if="mobile" class="pa-3 border-b flex-shrink-0 d-flex align-center">
              <v-btn
                icon="mdi-menu"
                variant="text"
                @click="showMobileMenu = true"
                class="me-2"
              />
              <h2 class="text-h6 flex-grow-1">Support Tickets</h2>
              <v-btn
                icon="mdi-plus"
                variant="text"
                @click="showNewMessageInterface = true"
              />
            </div>

            <!-- Search -->
            <div class="pa-3 border-b flex-shrink-0">
              <v-text-field
                v-model="searchQuery"
                placeholder="Search tickets..."
                prepend-inner-icon="mdi-magnify"
                variant="outlined"
                density="compact"
                hide-details
              />
            </div>

            <!-- Show Closed Tickets Toggle -->
            <div v-if="messagesStore.isSupportStaff" class="pa-3 border-b flex-shrink-0">
              <v-switch
                v-model="messagesStore.showClosedConversations"
                color="primary"
                density="compact"
                hide-details
              >
                <template #label>
                  <span class="text-body-2">Show closed tickets</span>
                </template>
              </v-switch>
            </div>

            <!-- Conversation List -->
            <div class="flex-grow-1 overflow-y-auto align-self-start w-100">
              <v-list density="compact" nav class="pa-0">
                <v-list-item
                  v-for="conversation in filteredSupportConversations"
                  :key="conversation.id"
                  :active="messagesStore.activeConversationId === conversation.id"
                  @click="selectConversation(conversation.id)"
                  class="mb-1"
                >
                  <template #prepend>
                    <v-avatar size="32">
                      <v-icon>mdi-help-circle</v-icon>
                    </v-avatar>
                  </template>

                  <v-list-item-title class="text-body-2">
                    {{ getConversationTitle(conversation) }}
                  </v-list-item-title>

                  <v-list-item-subtitle class="text-caption">
                    {{ getLastMessagePreview(conversation) }}
                  </v-list-item-subtitle>

                  <template #append>
                    <div class="d-flex flex-column align-end">
                      <v-btn
                        icon="mdi-pin"
                        size="x-small"
                        variant="text"
                        :color="conversation.is_pinned ? 'primary' : 'grey'"
                        @click.stop="messagesStore.togglePin(conversation.id)"
                      />
                      <span class="text-caption text-grey">
                        {{ formatTime(conversation.updated_at) }}
                      </span>
                    </div>
                  </template>
                </v-list-item>
              </v-list>

              <!-- New Support Request Button for regular users -->
              <div
                v-if="!messagesStore.isSupportStaff"
                class="pa-3 border-t"
              >
                <v-btn
                  color="primary"
                  variant="outlined"
                  block
                  @click="createNewSupportRequest"
                  prepend-icon="mdi-plus"
                >
                  New Support Request
                </v-btn>
              </div>
            </div>
          </v-card>
        </div>

        <!-- Right Column - Content Area -->
        <div
          class="content-column flex-grow-1"
          :class="{
            'w-100': selectedCategory === 'documentation' || (mobile && !messagesStore.activeConversationId)
          }"
        >
          <v-card flat class="fill-height d-flex flex-column">
            <!-- Documentation Content -->
            <div v-if="selectedCategory === 'documentation'" class="pa-6 flex-grow-1 overflow-y-auto">
              <div class="text-center">
                <v-icon size="64" color="primary" class="mb-4">mdi-book-open-variant</v-icon>
                <h1 class="text-h4 mb-4">Documentation</h1>
                <p class="text-body-1 text-grey">
                  Documentation content will be available here soon.
                </p>
              </div>
            </div>

            <!-- Support Tickets Content -->
            <div v-else-if="selectedCategory === 'tickets'">
              <!-- Chat Header -->
              <v-card-title
                v-if="messagesStore.activeConversation"
                class="d-flex align-center justify-space-between pa-4 border-b flex-shrink-0"
              >
                <div class="d-flex align-center">
                  <!-- Mobile Back Button -->
                  <v-btn
                    v-if="mobile"
                    icon="mdi-arrow-left"
                    variant="text"
                    @click="goBackToConversationList"
                    class="me-2"
                  />

                  <v-avatar class="me-3">
                    <v-icon>mdi-help-circle</v-icon>
                  </v-avatar>
                  <div>
                    <div class="text-h6">
                      {{ getConversationTitle(messagesStore.activeConversation) }}
                    </div>
                    <div class="text-caption text-grey">
                      {{ getConversationSubtitle(messagesStore.activeConversation) }}
                    </div>
                  </div>
                </div>

                <div class="d-flex align-center">
                  <!-- Three-dot menu -->
                  <v-menu>
                    <template #activator="{ props }">
                      <v-btn
                        icon="mdi-dots-vertical"
                        variant="text"
                        v-bind="props"
                      />
                    </template>
                    <v-list>
                      <!-- Admin Actions for Support Tickets -->
                      <template v-if="messagesStore.activeConversation?.type === 'support' && messagesStore.isSupportStaff">
                        <v-list-item v-if="messagesStore.activeConversation.status !== 'closed'" @click="closeTicket">
                          <template #prepend>
                            <v-icon>mdi-close-circle</v-icon>
                          </template>
                          <v-list-item-title>Close Ticket</v-list-item-title>
                        </v-list-item>
                        <v-list-item v-else @click="reopenTicket">
                          <template #prepend>
                            <v-icon>mdi-refresh</v-icon>
                          </template>
                          <v-list-item-title>Reopen Ticket</v-list-item-title>
                        </v-list-item>
                        <v-divider />
                      </template>

                      <v-list-item>
                        <template #prepend>
                          <v-icon>mdi-information</v-icon>
                        </template>
                        <v-list-item-title>Ticket Info</v-list-item-title>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
              </v-card-title>

              <!-- Messages Area -->
              <div
                v-if="messagesStore.activeConversation"
                class="flex-grow-1 overflow-y-auto pa-4"
                ref="messagesContainer"
              >
                <div
                  v-for="message in messagesStore.activeMessages"
                  :key="message.id"
                  class="mb-4"
                >
                  <div
                    :class="[
                      'd-flex',
                      message.sender_id === authStore.user?.id ? 'justify-end' : 'justify-start'
                    ]"
                  >
                    <div
                      :class="[
                        'message-bubble pa-3 rounded-lg',
                        message.sender_id === authStore.user?.id
                          ? 'bg-primary text-white'
                          : 'bg-grey-lighten-4'
                      ]"
                      style="max-width: 70%;"
                    >
                      <div
                        v-if="message.sender_id !== authStore.user?.id"
                        class="text-caption font-weight-bold mb-1"
                      >
                        {{ getSenderName(message) }}
                      </div>
                      <div class="text-body-2">{{ message.content }}</div>
                      <div
                        :class="[
                          'text-caption mt-1',
                          message.sender_id === authStore.user?.id
                            ? 'text-white'
                            : 'text-grey'
                        ]"
                      >
                        {{ formatTime(message.created_at) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Empty State for Support Tickets -->
              <div
                v-else-if="!showNewMessageInterface"
                class="flex-grow-1 d-flex align-center justify-center"
              >
                <div class="text-center">
                  <v-icon
                    size="64"
                    color="grey-lighten-2"
                    icon="mdi-help-circle"
                  />
                  <div class="text-h6 mt-4 text-grey">
                    No support tickets yet
                  </div>
                  <div class="text-body-2 text-grey mt-2">
                    Create a new support ticket to get help
                  </div>
                  <!-- Support-specific action button for regular users -->
                  <v-btn
                    v-if="!messagesStore.isSupportStaff"
                    color="primary"
                    class="mt-4"
                    @click="createNewSupportRequest"
                  >
                    Contact Support
                  </v-btn>
                </div>
              </div>

              <!-- New Message Interface -->
              <div
                v-if="showNewMessageInterface"
                class="flex-grow-1"
              >
                <NewMessageInterface
                  message-type="support"
                  @close="showNewMessageInterface = false"
                  @conversation-created="handleConversationCreated"
                />
              </div>

              <!-- Message Input - At bottom of chat area only -->
              <div
                v-if="messagesStore.activeConversation"
                class="border-t flex-shrink-0"
              >
                <!-- Message Input -->
                <div class="pa-4">
                  <div class="d-flex align-center">
                    <v-text-field
                      v-model="newMessage"
                      placeholder="Type a message..."
                      variant="outlined"
                      density="compact"
                      hide-details
                      @keyup.enter="sendMessage"
                      class="flex-grow-1 me-2"
                    />
                    <v-btn
                      icon="mdi-send"
                      color="primary"
                      :disabled="!newMessage.trim()"
                      @click="sendMessage"
                    />
                  </div>
                </div>
              </div>
            </div>
          </v-card>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Menu Bottom Sheet -->
  <v-bottom-sheet v-model="showMobileMenu" v-if="mobile">
    <v-card>
      <v-card-title class="d-flex align-center justify-space-between">
        <span>Menu</span>
        <v-btn icon="mdi-close" variant="text" @click="showMobileMenu = false" />
      </v-card-title>
      <v-card-text>
        <v-list>
          <!-- Documentation -->
          <v-list-item
            :active="selectedCategory === 'documentation'"
            @click="selectCategory('documentation'); showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-book-open-variant</v-icon>
            </template>
            <v-list-item-title>Documentation</v-list-item-title>
          </v-list-item>

          <!-- Support Tickets -->
          <v-list-item
            :active="selectedCategory === 'tickets'"
            @click="selectCategory('tickets'); showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-help-circle</v-icon>
            </template>
            <v-list-item-title>
              {{ messagesStore.isSupportStaff ? 'Support Tickets' : 'My Tickets' }}
            </v-list-item-title>
            <template #append>
              <v-chip
                v-if="messagesStore.unreadCounts.support > 0"
                size="small"
                color="primary"
              >
                {{ messagesStore.unreadCounts.support }}
              </v-chip>
            </template>
          </v-list-item>
        </v-list>
      </v-card-text>
    </v-card>
  </v-bottom-sheet>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useDisplay } from 'vuetify'
import { useMessagesStore } from '@/stores/messages'
import { useNotificationsStore } from '@/stores/notifications'
import { useAuthStore } from '@/stores/auth'
import NewMessageInterface from '@/components/NewMessageInterface.vue'

// Stores
const messagesStore = useMessagesStore()
const notificationsStore = useNotificationsStore()
const authStore = useAuthStore()

// Display composable for responsive design
const { mobile } = useDisplay({ mobileBreakpoint: 'md' })

// State
const selectedCategory = ref('documentation')
const searchQuery = ref('')
const newMessage = ref('')
const showNewMessageInterface = ref(false)
const showMobileMenu = ref(false)
const messagesContainer = ref(null)
const drawerOpen = ref(true)
const rail = ref(false)

// Computed
const filteredSupportConversations = computed(() => {
  let conversations = messagesStore.conversationsByCategory.support

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    conversations = conversations.filter(c =>
      getConversationTitle(c).toLowerCase().includes(query) ||
      getLastMessagePreview(c).toLowerCase().includes(query)
    )
  }

  return conversations
})

// Methods
function selectCategory(category) {
  // Clear active conversation when switching categories
  if (selectedCategory.value !== category) {
    messagesStore.setActiveConversation(null)
    showNewMessageInterface.value = false
  }
  selectedCategory.value = category
}

function getConversationTitle(conversation) {
  // Handle pending conversation
  if (conversation.id === 'pending') {
    return 'New Support Request'
  }

  if (conversation.title) return conversation.title

  if (conversation.type === 'support') {
    // For support staff, show the user's name who created the support request
    if (messagesStore.isSupportStaff) {
      const userParticipant = conversation.participants?.find(
        p => p.user?.role === 'user'
      )
      if (userParticipant?.user) {
        const userName = userParticipant.user.first_name
          ? `${userParticipant.user.first_name} ${userParticipant.user.last_name || ''}`.trim()
          : userParticipant.user.email
        return `Support: ${userName}`
      }
      return 'Support Request'
    }
    // For regular users, just show "Support Request"
    return 'Support Request'
  }

  return 'Support Ticket'
}

function getConversationSubtitle(conversation) {
  // Handle pending conversation
  if (conversation.id === 'pending') {
    return 'Start typing to create your support request'
  }

  if (conversation.type === 'support') {
    if (messagesStore.isSupportStaff) {
      // For support staff, show the user's email
      const userParticipant = conversation.participants?.find(
        p => p.user?.role === 'user'
      )
      return userParticipant?.user?.email || 'Support conversation'
    }
    // For regular users, show that it's a support conversation
    return 'Support conversation'
  }

  return 'Support ticket'
}

function getLastMessagePreview(conversation) {
  // Handle pending conversation
  if (conversation.id === 'pending') {
    return 'Start typing your message...'
  }

  const lastMessage = conversation.last_message?.[0]
  if (!lastMessage) return 'No messages yet'

  return lastMessage.content.length > 50
    ? `${lastMessage.content.substring(0, 50)}...`
    : lastMessage.content
}

function getSenderName(message) {
  if (!message.sender) return 'Unknown'

  return message.sender.first_name
    ? `${message.sender.first_name} ${message.sender.last_name || ''}`.trim()
    : message.sender.email
}

function formatTime(timestamp) {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  const now = new Date()
  const diffInHours = (now - date) / (1000 * 60 * 60)

  if (diffInHours < 24) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }
  if (diffInHours < 168) { // 7 days
    return date.toLocaleDateString([], { weekday: 'short' })
  }
  return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
}

async function selectConversation(conversationId) {
  // Set the active conversation (this will handle subscription internally)
  messagesStore.setActiveConversation(conversationId)

  // Scroll to bottom after messages load
  await nextTick()
  scrollToBottom()
}

function goBackToConversationList() {
  messagesStore.setActiveConversation(null)
}

async function sendMessage() {
  if (!newMessage.value.trim() || !messagesStore.activeConversationId) return

  try {
    await messagesStore.sendMessage(
      messagesStore.activeConversationId,
      newMessage.value.trim()
    )
    newMessage.value = ''

    // Scroll to bottom after sending
    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('Failed to send message:', error)
  }
}

async function createNewSupportRequest() {
  try {
    // Create a pending support conversation and set it as active
    messagesStore.createPendingSupportConversation()

    // Switch to tickets category and select the pending conversation
    selectedCategory.value = 'tickets'
    await selectConversation('pending')
  } catch (error) {
    console.error('Failed to create new support request:', error)
  }
}

async function closeTicket() {
  if (!messagesStore.activeConversationId || !messagesStore.isSupportStaff) return

  try {
    await messagesStore.updateConversationStatus(messagesStore.activeConversationId, 'closed')
    console.log('Ticket closed successfully')
  } catch (error) {
    console.error('Failed to close ticket:', error)
  }
}

async function reopenTicket() {
  if (!messagesStore.activeConversationId || !messagesStore.isSupportStaff) return

  try {
    await messagesStore.updateConversationStatus(messagesStore.activeConversationId, 'open')
    console.log('Ticket reopened successfully')
  } catch (error) {
    console.error('Failed to reopen ticket:', error)
  }
}

function handleConversationCreated(conversation) {
  // Close the new message interface
  showNewMessageInterface.value = false

  // Switch to tickets category
  selectedCategory.value = 'tickets'

  // Select the newly created conversation and ensure proper subscription
  if (conversation.id) {
    // Use nextTick to ensure the conversation is properly set before subscribing
    nextTick(() => {
      selectConversation(conversation.id)
    })
  }
}

function scrollToBottom() {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// Watch for new messages to auto-scroll
watch(
  () => messagesStore.activeMessages.length,
  () => {
    nextTick(() => scrollToBottom())
  }
)

// Lifecycle
onMounted(() => {
  // Initialize with enhanced conversation fetching for support staff
  if (authStore.user) {
    messagesStore.fetchCommunities()
    messagesStore.fetchConversationsEnhanced()
    messagesStore.subscribeToConversations()
  }
  notificationsStore.initialize()

  // Add keyboard shortcut for toggling rail mode
  const handleKeydown = (event) => {
    if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
      event.preventDefault()
      rail.value = !rail.value
    }
  }

  document.addEventListener('keydown', handleKeydown)

  // Cleanup function will be called in onUnmounted
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
})

onUnmounted(() => {
  messagesStore.unsubscribeAll()
})
</script>

<style scoped>
.message-bubble {
  word-wrap: break-word;
}

.border-e {
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.border-b {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.border-t {
  border-top: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.support-layout {
  height: calc(100vh - 110px);
  position: relative;
  overflow: hidden;
}

@media (max-width: 599px) {
  .support-layout {
    height: calc(100vh - 64px); /* Only app bar height on mobile */
  }
}

.content-area {
  height: 100%;
  position: relative;
}
</style>
