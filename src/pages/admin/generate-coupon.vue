<template>
  <v-container
    fluid
    class="mx-0 px-4"
  >
    <v-row class="mt-4">
      <v-col cols="12">
        <h1 class="text-h4 mb-4">
          Generate Coupon Codes
        </h1>

        <!-- Show alert based on status -->
        <v-alert
          v-if="statusMessage"
          :type="statusType"
          class="mx-4 mb-4"
          dismissible
          variant="outlined"
        >
          {{ statusMessage }}
        </v-alert>

        <v-card class="mb-6">
          <v-card-title>
            Create New Coupon Code
          </v-card-title>
          <v-card-text>
            <v-form ref="form">
              <v-row>
                <v-col
                  cols="12"
                  md="6"
                >
                  <v-text-field
                    v-model="couponCode"
                    label="Coupon Code"
                    hint="Leave blank to generate a random code"
                    persistent-hint
                  />
                </v-col>
                <v-col
                  cols="12"
                  md="6"
                >
                  <v-text-field
                    v-model="minutes"
                    label="Minutes"
                    type="number"
                    min="1"
                    :rules="[v => !!v || 'Minutes are required', v => v > 0 || 'Minutes must be greater than 0']"
                  />
                </v-col>
                <v-col
                  cols="12"
                  md="6"
                >
                  <v-text-field
                    v-model="maxUses"
                    label="Max Uses"
                    type="number"
                    min="1"
                    hint="Number of times this code can be used"
                    persistent-hint
                  />
                </v-col>
                <v-col
                  cols="12"
                  md="6"
                >
                  <v-text-field
                    v-model="expiresInDays"
                    label="Expires In (days)"
                    type="number"
                    min="1"
                    hint="Leave blank for no expiration"
                    persistent-hint
                  />
                </v-col>
                <v-col cols="12">
                  <v-textarea
                    v-model="description"
                    label="Description"
                    rows="2"
                    auto-grow
                  />
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="primary"
              :loading="loading"
              @click="generateCoupon"
            >
              Generate Coupon
            </v-btn>
          </v-card-actions>
        </v-card>

        <v-card v-if="generatedCoupon">
          <v-card-title>
            Generated Coupon
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12">
                <v-list>
                  <v-list-item>
                    <v-list-item-title>Code</v-list-item-title>
                    <v-list-item-subtitle>
                      <div class="d-flex align-center">
                        <code class="font-weight-bold">{{ generatedCoupon.code }}</code>
                        <v-btn
                          icon="mdi-content-copy"
                          size="small"
                          class="ml-2"
                          @click="copyToClipboard(generatedCoupon.code)"
                        />
                      </div>
                    </v-list-item-subtitle>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-title>Minutes</v-list-item-title>
                    <v-list-item-subtitle>{{ generatedCoupon.minutes }}</v-list-item-subtitle>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-title>Max Uses</v-list-item-title>
                    <v-list-item-subtitle>{{ generatedCoupon.max_uses || 'Unlimited' }}</v-list-item-subtitle>
                  </v-list-item>
                  <v-list-item v-if="generatedCoupon.expires_at">
                    <v-list-item-title>Expires</v-list-item-title>
                    <v-list-item-subtitle>{{ formatDate(generatedCoupon.expires_at) }}</v-list-item-subtitle>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-title>Description</v-list-item-title>
                    <v-list-item-subtitle>{{ generatedCoupon.description }}</v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Snackbar for copy actions -->
    <v-snackbar
      v-model="showSnackbar"
      :color="snackbarColor"
      :timeout="3000"
      location="top"
    >
      {{ snackbarMessage }}

      <template #actions>
        <v-btn
          variant="text"
          icon="mdi-close"
          @click="showSnackbar = false"
        />
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup>
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { supabase } from '@/plugins/supabase'

const authStore = useAuthStore()

// Form fields
const couponCode = ref('')
const minutes = ref(30)
const maxUses = ref(1)
const expiresInDays = ref('')
const description = ref('Test coupon code')
const form = ref(null)

// UI state
const loading = ref(false)
const statusMessage = ref('')
const statusType = ref('success')
const generatedCoupon = ref(null)

// Snackbar for copy actions
const showSnackbar = ref(false)
const snackbarMessage = ref('')
const snackbarColor = ref('success')

// Define route meta to require authentication
defineOptions({
  meta: {
    requiresAuth: true
  }
})

// Generate a coupon code
async function generateCoupon() {
  if (!authStore.user) return

  // Validate form
  const isValid = await form.value?.validate()
  if (!isValid) return

  loading.value = true
  statusMessage.value = ''
  statusType.value = 'success'

  try {
    // Call the edge function to create a coupon code
    const { data, error } = await supabase.functions.invoke('create-test-coupon', {
      body: {
        code: couponCode.value || undefined,
        minutes: Number(minutes.value),
        maxUses: maxUses.value ? Number(maxUses.value) : undefined,
        expiresInDays: expiresInDays.value ? Number(expiresInDays.value) : undefined,
        description: description.value
      }
    })

    if (error) {
      throw new Error(error.message || 'Failed to create coupon code')
    }

    if (data.error) {
      statusMessage.value = data.error
      statusType.value = 'error'
      return
    }

    // Success
    statusMessage.value = data.message
    statusType.value = 'success'
    generatedCoupon.value = data.coupon

    // Reset form
    couponCode.value = ''
  } catch (err) {
    console.error('Error generating coupon code:', err)
    statusMessage.value = err.message || 'Failed to generate coupon code. Please try again.'
    statusType.value = 'error'
  } finally {
    loading.value = false
  }
}

// Copy to clipboard
async function copyToClipboard(text) {
  try {
    await navigator.clipboard.writeText(text)
    snackbarMessage.value = 'Copied to clipboard'
    snackbarColor.value = 'success'
    showSnackbar.value = true
  } catch (err) {
    console.error('Failed to copy text: ', err)
    snackbarMessage.value = 'Failed to copy to clipboard'
    snackbarColor.value = 'error'
    showSnackbar.value = true
  }
}

// Format date
function formatDate(dateString) {
  if (!dateString) return ''

  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric'
  }).format(date)
}
</script>

<style scoped>
code {
  padding: 0.2rem 0.4rem;
  background-color: rgba(var(--v-theme-surface-variant), 0.5);
  border-radius: 4px;
}
</style>
