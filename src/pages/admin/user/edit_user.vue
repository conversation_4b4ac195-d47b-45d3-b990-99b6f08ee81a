<template>
  <v-container class="mt-4">
    <v-row>
      <!-- User Profile Section - Left Column -->
      <v-col
        cols="12"
        md="6"
      >
        <v-card class="mb-4">
          <v-card-title class="d-flex align-center">
            <v-icon
              size="large"
              color="primary"
              class="mr-2"
            >
              mdi-account-edit
            </v-icon>
            Edit User Profile
            <v-spacer />
            <v-btn
              color="pink-lighten-4"
              variant="text"
              prepend-icon="mdi-arrow-left"
              to="/admin/users"
              class="text-none"
            >
              BACK TO ADMIN
            </v-btn>
          </v-card-title>

          <v-card-text v-if="loading">
            <div class="d-flex justify-center py-4">
              <v-progress-circular
                indeterminate
                color="primary"
              />
            </div>
          </v-card-text>

          <v-card-text v-else-if="error">
            <v-alert
              type="error"
              title="Error"
              :text="error"
            />
          </v-card-text>

          <v-form
            v-else
            ref="form"
            @submit.prevent="saveUser"
          >
            <v-card-text>
              <v-row>
                <!-- Basic Information -->
                <v-col cols="12">
                  <div class="text-h6 mb-3">Basic Information</div>
                </v-col>

                <v-col cols="12" md="6">
                  <div class="mb-3">
                    <div class="text-subtitle-2 mb-1">User ID</div>
                    <div class="text-body-2">{{ userProfile.user_id || '-' }}</div>
                  </div>
                </v-col>

                <v-col cols="12" md="6">
                  <div class="mb-3">
                    <div class="text-subtitle-2 mb-1">Internal ID</div>
                    <div class="text-body-2">{{ userProfile.id || '-' }}</div>
                  </div>
                </v-col>

                <v-col cols="12" md="6">
                  <div class="mb-3">
                    <div class="text-subtitle-2 mb-1">Created At</div>
                    <div class="text-body-2">{{ formatDate(userProfile.created_at) }}</div>
                  </div>
                </v-col>

                <v-col cols="12" md="6">
                  <div class="mb-3">
                    <div class="text-subtitle-2 mb-1">Updated At</div>
                    <div class="text-body-2">{{ formatDate(userProfile.updated_at) }}</div>
                  </div>
                </v-col>

                <v-col cols="12">
                  <v-text-field
                    v-model="userProfile.email"
                    label="Email"
                    readonly
                    variant="outlined"
                    density="comfortable"
                  >
                    <template #append>
                      <v-btn
                        v-if="authStore.user?.user_metadata?.role === 'admin'"
                        icon
                        color="primary"
                        size="small"
                        variant="text"
                        :disabled="loading || error"
                        @click="openEmailDialog"
                      >
                        <v-icon>mdi-email-outline</v-icon>
                        <v-tooltip
                          activator="parent"
                          location="top"
                        >
                          Send Email
                        </v-tooltip>
                      </v-btn>
                    </template>
                  </v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="userProfile.first_name"
                    label="First Name"
                    variant="outlined"
                    density="comfortable"
                    :rules="[rules.required]"
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="userProfile.last_name"
                    label="Last Name"
                    variant="outlined"
                    density="comfortable"
                    :rules="[rules.required]"
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="userProfile.city"
                    label="City"
                    variant="outlined"
                    density="comfortable"
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="userProfile.state"
                    label="State"
                    variant="outlined"
                    density="comfortable"
                  />
                </v-col>

                <v-col cols="12" md="6">
                  <div class="mb-3">
                    <div class="text-subtitle-2 mb-1">Mobile Phone</div>
                    <div class="text-body-2">{{ userProfile.mobile_phone || '-' }}</div>
                  </div>
                </v-col>

                <v-col cols="12" md="6">
                  <div class="mb-3">
                    <div class="text-subtitle-2 mb-1">Source</div>
                    <div class="text-body-2">{{ userProfile.source || '-' }}</div>
                  </div>
                </v-col>

                <!-- Profile Information -->
                <v-col cols="12">
                  <v-divider class="my-4" />
                  <div class="text-h6 mb-3">Profile Information</div>
                </v-col>

                <v-col cols="12">
                  <div class="mb-3">
                    <div class="text-subtitle-2 mb-1">Pageant Title</div>
                    <div class="text-body-2">{{ userProfile.pageant_title || '-' }}</div>
                  </div>
                </v-col>

                <v-col cols="12">
                  <v-textarea
                    v-model="userProfile.resume"
                    label="Resume"
                    variant="outlined"
                    rows="6"
                    readonly
                  />
                </v-col>

                <v-col cols="12">
                  <v-textarea
                    v-model="userProfile.social_impact_initiative"
                    label="Social Impact Initiative"
                    variant="outlined"
                    rows="4"
                    readonly
                  />
                </v-col>

                <!-- Current Events -->
                <v-col cols="12">
                  <v-divider class="my-4" />
                  <div class="text-h6 mb-3">Current Events</div>
                </v-col>

                <v-col cols="12">
                  <div class="mb-3">
                    <div class="text-subtitle-2 mb-1">Current Events (Array)</div>
                    <div class="text-body-2">
                      <v-chip
                        v-for="(event, index) in userProfile.current_events"
                        :key="index"
                        size="small"
                        class="mr-1 mb-1"
                      >
                        {{ event }}
                      </v-chip>
                      <span v-if="!userProfile.current_events || userProfile.current_events.length === 0">-</span>
                    </div>
                  </div>
                </v-col>

                <v-col cols="12">
                  <div class="mb-3">
                    <div class="text-subtitle-2 mb-1">Current Events Text</div>
                    <div class="text-body-2">{{ userProfile.current_events_text || '-' }}</div>
                  </div>
                </v-col>

                <v-col cols="12">
                  <div class="mb-3">
                    <div class="text-subtitle-2 mb-1">Current Events Updated At</div>
                    <div class="text-body-2">{{ formatDate(userProfile.current_events_updated_at) }}</div>
                  </div>
                </v-col>

                <!-- System Information -->
                <v-col cols="12">
                  <v-divider class="my-4" />
                  <div class="text-h6 mb-3">System Information</div>
                </v-col>

                <v-col cols="12" md="6">
                  <div class="mb-3">
                    <div class="text-subtitle-2 mb-1">Role</div>
                    <div class="text-body-2">
                      <v-chip
                        :color="userProfile.role === 'admin' ? 'error' : 'primary'"
                        size="small"
                      >
                        {{ userProfile.role || 'user' }}
                      </v-chip>
                    </div>
                  </div>
                </v-col>

                <v-col cols="12" md="6">
                  <div class="mb-3">
                    <div class="text-subtitle-2 mb-1">Onboarding Complete</div>
                    <div class="text-body-2">
                      <v-chip
                        :color="userProfile.onboarding_complete ? 'success' : 'warning'"
                        size="small"
                      >
                        {{ userProfile.onboarding_complete ? 'Yes' : 'No' }}
                      </v-chip>
                    </div>
                  </div>
                </v-col>

                <v-col cols="12">
                  <div class="mb-3">
                    <div class="text-subtitle-2 mb-1">From Email</div>
                    <div class="text-body-2">{{ userProfile.from_email || '-' }}</div>
                  </div>
                </v-col>
              </v-row>
            </v-card-text>

            <v-divider />

            <v-card-actions class="pa-4">
              <v-spacer />
              <v-btn
                color="error"
                variant="text"
                :disabled="saving"
                @click="confirmDelete = true"
              >
                Delete User
              </v-btn>
              <v-btn
                color="primary"
                type="submit"
                :loading="saving"
              >
                Save Changes
              </v-btn>
            </v-card-actions>
          </v-form>
        </v-card>
      </v-col>

      <!-- User Minutes Section - Right Column -->
      <v-col
        cols="12"
        md="6"
      >
        <v-card class="mb-4">
          <v-card-title class="d-flex align-center">
            <v-icon
              size="large"
              color="primary"
              class="mr-2"
            >
              mdi-clock-outline
            </v-icon>
            User Minutes
            <v-spacer />
            <v-chip
              v-if="!loadingMinutes"
              color="primary"
              size="small"
              class="text-subtitle-2"
            >
              Total: {{ totalMinutes }} minutes
            </v-chip>
            <v-btn
              v-if="!loadingMinutes"
              class="ml-2"
              size="small"
              variant="text"
              icon="mdi-plus"
              @click="showAddMinutesDialog = true"
            />
          </v-card-title>

          <v-card-text v-if="loadingMinutes">
            <div class="d-flex justify-center py-4">
              <v-progress-circular
                indeterminate
                color="primary"
              />
            </div>
          </v-card-text>

          <v-card-text v-else-if="minutesError">
            <v-alert
              type="error"
              title="Error"
              :text="minutesError"
            />
          </v-card-text>

          <v-card-text v-else-if="userMinutes.length === 0">
            <v-alert
              type="info"
              title="No Records"
              text="This user has no minute records."
            />
          </v-card-text>

          <template v-else>
            <v-data-table
              :headers="minutesHeaders"
              :items="userMinutes"
              :items-per-page="5"
              class="elevation-0"
            >
              <template #item="{ item }">
                <tr>
                  <td>
                    <div
                      v-if="item.note"
                      class="mb-1"
                    >
                      {{ item.note }}
                    </div>
                    <div class="text-caption text-grey">
                      {{ formatDate(item.created_at) }}
                    </div>
                  </td>
                  <td class="text-right">
                    <span :class="{'text-red': item.minutes < 0, 'text-green': item.minutes >= 0}">
                      {{ item.minutes }}
                    </span>
                  </td>
                  <td>
                    <v-chip
                      v-if="item.interview_id"
                      size="small"
                      color="primary"
                      class="text-none"
                      :to="`/interview/${item.interview_id}`"
                    >
                      Interview
                    </v-chip>
                    <v-chip
                      v-else-if="item.transaction_id"
                      size="small"
                      color="success"
                      class="text-none"
                      :to="`/transactions/${item.transaction_id}`"
                    >
                      Transaction
                    </v-chip>
                    <v-chip
                      v-else
                      size="small"
                      color="grey"
                      class="text-none"
                    >
                      Manual
                    </v-chip>
                  </td>
                </tr>
              </template>
            </v-data-table>
          </template>
        </v-card>

        <!-- Transactions Section -->
        <v-card class="mb-4">
          <v-card-title class="d-flex align-center">
            <v-icon
              size="large"
              color="success"
              class="mr-2"
            >
              mdi-cash
            </v-icon>
            Transactions
            <v-spacer />
            <v-chip
              v-if="!loadingTransactions"
              color="success"
              size="small"
              class="text-subtitle-2"
            >
              Total: {{ formatCurrency(totalAmount) }}
            </v-chip>
          </v-card-title>

          <v-card-text v-if="loadingTransactions">
            <div class="d-flex justify-center py-4">
              <v-progress-circular
                indeterminate
                color="primary"
              />
            </div>
          </v-card-text>

          <v-card-text v-else-if="transactionsError">
            <v-alert
              type="error"
              title="Error"
              :text="transactionsError"
            />
          </v-card-text>

          <v-card-text v-else-if="userTransactions.length === 0">
            <v-alert
              type="info"
              title="No Records"
              text="This user has no transaction records."
            />
          </v-card-text>

          <template v-else>
            <v-data-table
              :headers="transactionsHeaders"
              :items="userTransactions"
              :items-per-page="5"
              class="elevation-0"
            >
              <template #item="{ item }">
                <tr>
                  <td>{{ formatDate(item.created_at) }}</td>
                  <td>{{ item.product_name }}</td>
                  <td class="text-right">
                    {{ formatCurrency(item.total) }}
                  </td>
                  <td>{{ item.minutes }}</td>
                  <td>{{ item.referral_code || '-' }}</td>
                  <td>
                    <v-btn
                      v-if="item.payment_intent_id"
                      size="small"
                      color="primary"
                      variant="text"
                      icon
                      :href="getStripeLink(item.payment_intent_id, item.livemode)"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <v-icon>mdi-link</v-icon>
                    </v-btn>
                  </td>
                </tr>
              </template>
            </v-data-table>
          </template>
        </v-card>
      </v-col>
    </v-row>

    <!-- Delete Confirmation Dialog -->
    <v-dialog
      v-model="confirmDelete"
      max-width="500"
    >
      <v-card>
        <v-card-title class="text-h5">
          Confirm Delete
        </v-card-title>
        <v-card-text>
          Are you sure you want to delete this user? This action cannot be undone.
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="confirmDelete = false"
          >
            Cancel
          </v-btn>
          <v-btn
            color="error"
            variant="text"
            :loading="deleting"
            @click="deleteUser"
          >
            Delete
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Add Minutes Dialog -->
    <v-dialog
      v-model="showAddMinutesDialog"
      max-width="500"
    >
      <v-card>
        <v-card-title class="text-h5">
          Add User Minutes
        </v-card-title>
        <v-form
          ref="minutesForm"
          @submit.prevent="addUserMinutes"
        >
          <v-card-text>
            <v-text-field
              v-model.number="newMinutesRecord.minutes"
              label="Minutes"
              type="number"
              variant="outlined"
              density="comfortable"
              :rules="[rules.required]"
              hint="Enter negative value to subtract minutes"
            />

            <v-textarea
              v-model="newMinutesRecord.note"
              label="Note"
              variant="outlined"
              auto-grow
              rows="3"
              class="mt-3"
              :rules="[rules.required]"
              hint="Reason for adding/removing minutes"
            />
          </v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="grey-darken-1"
              variant="text"
              @click="showAddMinutesDialog = false"
            >
              Cancel
            </v-btn>
            <v-btn
              color="primary"
              type="submit"
              :loading="addingMinutes"
            >
              Add Record
            </v-btn>
          </v-card-actions>
        </v-form>
      </v-card>
    </v-dialog>

    <!-- Email Dialog -->
    <v-dialog
      v-model="showEmailDialog"
      max-width="700"
    >
      <v-card>
        <v-card-title class="text-h5">
          Send Email to {{ userProfile.first_name || 'User' }}
        </v-card-title>
        <v-form
          ref="emailForm"
          @submit.prevent="sendEmail"
        >
          <v-card-text>
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="emailData.to"
                  label="To"
                  variant="outlined"
                  density="comfortable"
                  readonly
                  :rules="[rules.required, rules.email]"
                />
              </v-col>

              <v-col cols="12">
                <v-text-field
                  v-model="emailData.from"
                  label="From"
                  variant="outlined"
                  density="comfortable"
                  :rules="[rules.required, rules.email]"
                />
              </v-col>

              <v-col cols="12">
                <v-text-field
                  v-model="emailData.subject"
                  label="Subject"
                  variant="outlined"
                  density="comfortable"
                  :rules="[rules.required]"
                />
              </v-col>

              <v-col cols="12">
                <!-- WYSIWYG Editor -->
                <div class="mb-2">
                  Email Content
                </div>
                <div
                  id="editor-container"
                  class="email-editor"
                  style="height: 250px; border: 1px solid #ccc; border-radius: 4px; margin-bottom: 8px;"
                />
                <div class="text-caption">
                  HTML formatting is supported
                </div>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="grey-darken-1"
              variant="text"
              :disabled="sendingEmail"
              @click="showEmailDialog = false"
            >
              Cancel
            </v-btn>
            <v-btn
              color="primary"
              type="submit"
              :loading="sendingEmail"
            >
              Send Email
            </v-btn>
          </v-card-actions>
        </v-form>
      </v-card>
    </v-dialog>

    <!-- Snackbar for notifications -->
    <v-snackbar
      v-model="showSnackbar"
      :color="snackbarColor"
      :timeout="5000"
    >
      {{ snackbarText }}
      <template #actions>
        <v-btn
          variant="text"
          @click="showSnackbar = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { supabase } from '@/plugins/supabase'

// Debug function
function debug(message, data = {}) {
  console.log(`🔍 [EditUser] ${message}`, data);
}

// For page load debugging
debug('Component script executed', { timestamp: new Date().toISOString() });

// Define route meta data using defineOptions
defineOptions({
  meta: {
    requiresAuth: true,
    requiresAdmin: true,
    layout: 'default'
  },
})

// Accept props from router
const props = defineProps({
  id: String,
  userObject: Object
})

debug('Props received', props);

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Get userId from either props or route params
const userId = computed(() => {
  const id = props.id || route.params.id;
  debug('userId computed', { id, fromProps: !!props.id, fromRoute: !!route.params.id });
  return id;
})

debug('Component setup with userId', {
  userId: userId.value,
  props: props.id,
  routeParams: route.params.id,
  routePath: route.path,
  routeFullPath: route.fullPath
});

// Form data
const form = ref(null)
const userProfile = ref({
  id: '',
  user_id: '',
  email: '',
  first_name: '',
  last_name: '',
  resume: '',
  city: '',
  state: '',
  social_impact_initiative: '',
  pageant_title: '',
  current_events: [],
  current_events_text: '',
  current_events_updated_at: '',
  mobile_phone: '',
  source: '',
  onboarding_complete: false,
  role: '',
  from_email: '',
  created_at: '',
  updated_at: ''
})
const loading = ref(false)
const saving = ref(false)
const deleting = ref(false)
const error = ref(null)
const confirmDelete = ref(false)

// User minutes data
const userMinutes = ref([])
const loadingMinutes = ref(false)
const minutesError = ref(null)

// Add minutes dialog
const showAddMinutesDialog = ref(false)
const minutesForm = ref(null)
const addingMinutes = ref(false)
const newMinutesRecord = ref({
  minutes: 0,
  note: ''
})

// User transactions data
const userTransactions = ref([])
const loadingTransactions = ref(false)
const transactionsError = ref(null)

// Define headers for the data table
const minutesHeaders = [
  { title: 'Date', key: 'created_at' },
  { title: 'Minutes', key: 'minutes', align: 'end' },
  { title: 'Activity', key: 'activity' }
]

// Define headers for the transactions table
const transactionsHeaders = [
  { title: 'Date', key: 'created_at' },
  { title: 'Product', key: 'product_name' },
  { title: 'Amount', key: 'total', align: 'end' },
  { title: 'Minutes', key: 'minutes' },
  { title: 'Referral Code', key: 'referral_code' },
  { title: 'Actions', key: 'actions' }
]

// Form validation rules
const rules = {
  required: v => !!v || 'This field is required',
  email: v => /.+@.+\..+/.test(v) || 'Invalid email format'
}

// Debug watch execution
let watchCount = 0;

// Ensure watch is using computed userId
watch(userId, (newId) => {
  watchCount++;
  debug(`userId watch triggered #${watchCount}`, { newId });
  if (newId) {
    debug('Calling fetchUserProfile from watch');
    fetchUserProfile();
  } else {
    debug('No userId in watch, skipping fetchUserProfile');
  }
}, { immediate: true })

// Extra watcher for robustness: watch route.params.id directly
watch(() => route.params.id, (newId) => {
  if (newId) {
    debug('route.params.id watcher triggered', { newId });
    fetchUserProfile();
  }
});

// Check if user is authorized
onMounted(async () => {
  debug('Component mounted', {
    userId: userId.value,
    routeParams: route.params,
    props,
    isAuthenticated: authStore.isAuthenticated,
    userRole: authStore.user?.user_metadata?.role
  });

  // Set initial loading state
  loading.value = true;

  try {
    // Ensure auth store is loaded
    if (authStore.loading) {
      debug('Waiting for auth to initialize...');
      // Wait for the auth store to finish loading
      let waitCount = 0;
      while (authStore.loading && waitCount < 10) { // Limit to prevent infinite loop
        waitCount++;
        debug(`Auth still loading... attempt ${waitCount}`);
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      if (authStore.loading) {
        debug('Auth initialization timeout - still loading after 2 seconds');
      }
    }

    debug('Auth state after initialization', {
      isAuthenticated: authStore.isAuthenticated,
      user: authStore.user?.id,
      role: authStore.user?.user_metadata?.role,
      loading: authStore.loading
    });

    // Check if user is an admin after auth is loaded
    if (!authStore.user?.user_metadata?.role || authStore.user.user_metadata.role !== 'admin') {
      console.error('Unauthorized access attempt to admin user edit page');
      error.value = 'You do not have permission to access this page';
      debug('User is not admin, redirecting');
      router.push('/interviews');
      return;
    }

    // Only fetch if we have a userId
    if (userId.value) {
      debug('userId available on mount, fetchUserProfile should be called by watch');
      // fetchUserProfile is called by the watch with immediate:true
    } else {
      console.error('No userId available on mount');
      debug('No userId available on mount');
      error.value = 'User ID not found';
      loading.value = false;
    }
  } catch (err) {
    console.error('Error during component initialization:', err);
    debug('Error during initialization', { error: err });
    error.value = 'Failed to initialize page. Please try again.';
    loading.value = false;
  }
})

// Calculate total minutes
const totalMinutes = computed(() => {
  return userMinutes.value.reduce((sum, record) => sum + (record.minutes || 0), 0)
})

// Calculate total amount from transactions
const totalAmount = computed(() => {
  return userTransactions.value.reduce((sum, record) => sum + (Number.parseFloat(record.total) || 0), 0)
})

// Format date
function formatDate(dateString) {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Format currency
function formatCurrency(value) {
  if (!value) return '$0.00'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value)
}

// Get Stripe link
function getStripeLink(paymentIntentId, livemode) {
  // Determine if this is a test or live mode transaction
  const baseUrl = livemode
    ? 'https://dashboard.stripe.com/payments/'
    : 'https://dashboard.stripe.com/test/payments/';

  return baseUrl + paymentIntentId;
}

// Fetch user profile data
async function fetchUserProfile() {
  debug('fetchUserProfile called', { userId: userId.value });

  if (!userId.value) {
    console.error('fetchUserProfile called with no userId');
    debug('fetchUserProfile aborting - no userId');
    error.value = 'User ID not found';
    loading.value = false;
    return;
  }

  loading.value = true;
  error.value = null;

  try {
    // Check for user data in props first (highest priority)
    if (props.userObject) {
      debug('User data found in props', { userObject: props.userObject });

      if (props.userObject.user_id === userId.value) {
        userProfile.value = props.userObject;
        debug('Using user data from props', { userProfile: userProfile.value });

        // Fetch related data
        await Promise.all([
          fetchUserMinutes(),
          fetchUserTransactions()
        ]);

        loading.value = false;
        return;
      }
    }

    // Then check router state (for navigation within the app)
    debug('Checking for user data in router state', { state: router.currentRoute.value.state });
    if (router.currentRoute.value.state?.userObject) {
      const userData = router.currentRoute.value.state.userObject;
      debug('User data found in router state', { userData });

      if (userData && userData.user_id === userId.value) {
        userProfile.value = userData;
        debug('Using user data from router state', { userProfile: userProfile.value });

        await Promise.all([
          fetchUserMinutes(),
          fetchUserTransactions()
        ]);

        loading.value = false;
        return;
      }
    }

    // Finally, fetch from the database (for direct URL access or page refresh)
    debug('Fetching user profile from Supabase', { userId: userId.value });
    const { data, error: fetchError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId.value)
      .single();

    if (fetchError) {
      debug('Error from Supabase', { error: fetchError });
      throw fetchError;
    }

    if (data) {
      debug('Fetched user profile data', { data });
      userProfile.value = data;
    } else {
      debug('User profile not found');
      error.value = 'User profile not found';
      setTimeout(() => {
        router.push('/admin');
      }, 3000);
    }

    // After fetching user profile, also fetch minutes and transactions
    debug('Fetching related data');
    await Promise.all([
      fetchUserMinutes(),
      fetchUserTransactions()
    ]);
  } catch (err) {
    console.error('Error fetching user profile:', err);
    debug('Error fetching user profile', { error: err });
    error.value = 'Failed to load user profile. Please try again.';
  } finally {
    loading.value = false;
    debug('Finished loading user profile');
  }
}

// Fetch user minutes records
async function fetchUserMinutes() {
  loadingMinutes.value = true
  minutesError.value = null

  try {
    // Fetch user minutes from Supabase
    const { data, error: fetchError } = await supabase
      .from('user_minutes')
      .select('*')
      .eq('user_id', userId.value)
      .order('created_at', { ascending: false })

    if (fetchError) throw fetchError

    if (data) {
      console.log('Fetched user minutes data:', data)
      userMinutes.value = data
    }
  } catch (err) {
    console.error('Error fetching user minutes:', err)
    minutesError.value = 'Failed to load user minutes. Please try again.'
  } finally {
    loadingMinutes.value = false
  }
}

// Fetch user transactions
async function fetchUserTransactions() {
  loadingTransactions.value = true
  transactionsError.value = null

  try {
    // Fetch user transactions from Supabase
    const { data, error: fetchError } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', userId.value)
      .order('created_at', { ascending: false })

    if (fetchError) throw fetchError

    if (data) {
      console.log('Fetched user transactions data:', data)
      userTransactions.value = data
    }
  } catch (err) {
    console.error('Error fetching user transactions:', err)
    transactionsError.value = 'Failed to load user transactions. Please try again.'
  } finally {
    loadingTransactions.value = false
  }
}

// Save user profile changes
async function saveUser() {
  if (!form.value?.validate()) return

  saving.value = true
  error.value = null

  try {
    // Log the data we're trying to update
    console.log('Updating user profile with:', {
      first_name: userProfile.value.first_name,
      last_name: userProfile.value.last_name,
      city: userProfile.value.city,
      state: userProfile.value.state
    })

    // Update user profile in Supabase
    const { error: updateError } = await supabase
      .from('user_profiles')
      .update({
        first_name: userProfile.value.first_name,
        last_name: userProfile.value.last_name,
        city: userProfile.value.city,
        state: userProfile.value.state
      })
      .eq('user_id', userProfile.value.user_id)

    if (updateError) throw updateError

    // Navigate back to admin page after successful save
    setTimeout(() => {
      router.push('/admin')
    }, 1000)
  } catch (err) {
    console.error('Error saving user profile:', err)
    error.value = 'Failed to save user profile. Please try again.'
  } finally {
    saving.value = false
  }
}

// Delete user profile
async function deleteUser() {
  deleting.value = true
  error.value = null

  try {
    // Delete user profile from Supabase
    const { error: deleteError } = await supabase
      .from('user_profiles')
      .delete()
      .eq('user_id', userProfile.value.user_id)

    if (deleteError) throw deleteError

    // Navigate back to admin page after successful delete
    confirmDelete.value = false
    router.push('/admin')
  } catch (err) {
    console.error('Error deleting user profile:', err)
    error.value = 'Failed to delete user profile. Please try again.'
    confirmDelete.value = false
  } finally {
    deleting.value = false
  }
}

// Add new user minutes record
async function addUserMinutes() {
  if (!minutesForm.value?.validate()) return

  addingMinutes.value = true
  minutesError.value = null

  try {
    // Prepare the data to insert
    const recordToInsert = {
      user_id: userId.value,
      minutes: newMinutesRecord.value.minutes,
      note: newMinutesRecord.value.note,
      created_by: authStore.user.id
    }

    console.log('Adding user minutes record:', recordToInsert)

    // Insert the new record into Supabase
    const { data, error: insertError } = await supabase
      .from('user_minutes')
      .insert(recordToInsert)
      .select()

    if (insertError) throw insertError

    console.log('Added user minutes record:', data)

    // Reset the form and close the dialog
    newMinutesRecord.value = {
      minutes: 0,
      note: ''
    }
    showAddMinutesDialog.value = false

    // Refresh the minutes list
    await fetchUserMinutes()
  } catch (err) {
    console.error('Error adding user minutes:', err)
    minutesError.value = 'Failed to add minutes record. Please try again.'
  } finally {
    addingMinutes.value = false
  }
}

// Email dialog
const showEmailDialog = ref(false)
const emailForm = ref(null)
const emailData = ref({
  to: '',
  from: '',
  subject: '',
  html: ''
})
const sendingEmail = ref(false)
let quillEditor = null

// Snackbar
const showSnackbar = ref(false)
const snackbarText = ref('')
const snackbarColor = ref('success')

// Open email dialog
async function openEmailDialog() {
  showEmailDialog.value = true

  // Fetch admin's profile to get their from_email
  let adminFromEmail = authStore.user.email // fallback

  try {
    const { data: adminData, error: adminError } = await supabase
      .from('user_profiles')
      .select('from_email')
      .eq('user_id', authStore.user.id)
      .single()

    if (!adminError && adminData?.from_email) {
      adminFromEmail = adminData.from_email
    }
  } catch (err) {
    console.warn('Could not fetch admin from_email, using fallback:', err)
  }

  emailData.value = {
    to: userProfile.value.email,
    from: adminFromEmail,
    subject: '',
    html: `<div style="font-family: Arial, sans-serif; line-height: 1.6;">
  <p>Hello ${userProfile.value.first_name || 'there'},</p>
  <p></p>
  <p></p>
  <p>Thanks,<br>
  ${authStore.user?.user_metadata?.first_name || ''} ${authStore.user?.user_metadata?.last_name || ''}</p>
</div>`
  }

  // Initialize Quill editor after dialog is opened
  nextTick(() => {
    initQuillEditor()
  })
}

// Initialize Quill editor
function initQuillEditor() {
  if (typeof window.Quill === 'undefined') {
    // Load Quill via CDN if not already loaded
    const quillScript = document.createElement('script')
    quillScript.src = 'https://cdn.quilljs.com/1.3.6/quill.min.js'
    document.head.appendChild(quillScript)

    const quillStyle = document.createElement('link')
    quillStyle.rel = 'stylesheet'
    quillStyle.href = 'https://cdn.quilljs.com/1.3.6/quill.snow.css'
    document.head.appendChild(quillStyle)

    quillScript.onload = () => {
      createEditor()
    }
  } else {
    // Quill already loaded
    createEditor()
  }
}

// Create Quill editor instance
function createEditor() {
  // Wait until the element is in the DOM
  const editorContainer = document.getElementById('editor-container')
  if (!editorContainer) {
    setTimeout(createEditor, 100)
    return
  }

  // Destroy existing instance if it exists
  if (quillEditor) {
    quillEditor = null
  }

  // Clear the container
  editorContainer.innerHTML = ''

  // Create new instance
  const Quill = window.Quill // Access Quill from window object
  quillEditor = new Quill('#editor-container', {
    theme: 'snow',
    modules: {
      toolbar: [
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        ['link'],
        ['clean']
      ]
    }
  })

  // Set initial content
  quillEditor.root.innerHTML = emailData.value.html

  // Update model when editor changes
  quillEditor.on('text-change', () => {
    emailData.value.html = quillEditor.root.innerHTML
  })
}

// Send email
async function sendEmail() {
  if (!emailForm.value?.validate()) return

  // Explicit validation for subject field
  if (!emailData.value.subject.trim()) {
    snackbarText.value = 'Email subject is required'
    snackbarColor.value = 'error'
    showSnackbar.value = true
    return
  }

  // Check if editor has content
  if (quillEditor && quillEditor.getText().trim().length === 0 && !quillEditor.root.innerHTML.includes('img')) {
    snackbarText.value = 'Email content is required'
    snackbarColor.value = 'error'
    showSnackbar.value = true
    return
  }

  // Update email content from editor
  if (quillEditor) {
    emailData.value.html = quillEditor.root.innerHTML
  }

  sendingEmail.value = true
  error.value = null

  try {
    // Log the data we're trying to send
    console.log('Sending email with:', {
      to: emailData.value.to,
      from: emailData.value.from,
      subject: emailData.value.subject,
      html: emailData.value.html
    })

    // Call the Supabase Edge Function to send the email
    const { data, error: emailError } = await supabase.functions.invoke('send-email', {
      body: {
        to: emailData.value.to,
        from: emailData.value.from,
        subject: emailData.value.subject,
        html: emailData.value.html,
        text: quillEditor ? quillEditor.getText() : emailData.value.html.replace(/<[^>]*>/g, '') // Plain text version
      }
    })

    if (emailError) throw emailError

    console.log('Email sent successfully:', data)

    // Reset the form and close the dialog
    emailData.value = {
      to: '',
      from: '',
      subject: '',
      html: ''
    }
    showEmailDialog.value = false

    // Show success message to the user
    error.value = null
    snackbarText.value = 'Email sent successfully!'
    snackbarColor.value = 'success'
    showSnackbar.value = true
  } catch (err) {
    console.error('Error sending email:', err)
    error.value = 'Failed to send email. Please try again.'
    snackbarText.value = 'Failed to send email. Please try again.'
    snackbarColor.value = 'error'
    showSnackbar.value = true
  } finally {
    sendingEmail.value = false
  }
}

// Clean up Quill when dialog closes
watch(showEmailDialog, (newValue) => {
  if (!newValue && quillEditor) {
    // Dialog closed, clean up editor if needed
    quillEditor = null
  }
})
</script>

<route>
{
  meta: {
    requiresAuth: true,
    requiresAdmin: true,
    layout: 'default'
  }
}
</route>
