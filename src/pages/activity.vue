<template>
  <div class="flex-grow-1 overflow-y-auto pa-6">
    <div class="d-flex align-center mb-6">
      <h1 class="text-h4">Activity</h1>
      <v-spacer />
      <span
        v-if="!activityLoading && activityItems.length > 0"
        class="text-h6"
      >
        {{ userBalance }} min
      </span>
    </div>

    <v-card
      variant="elevated"
      elevation="3"
    >
      <v-card-text>
        <div
          v-if="activityLoading"
          class="d-flex justify-center align-center my-4"
        >
          <v-progress-circular
            indeterminate
            color="primary"
          />
        </div>

        <v-alert
          v-else-if="activityError"
          type="error"
          class="mb-4"
        >
          {{ activityError }}
        </v-alert>

        <v-alert
          v-else-if="!activityItems.length"
          type="info"
          class="mb-4"
        >
          No activity found.
        </v-alert>

        <div
          v-else
          class="activity-list"
        >
          <!-- Desktop Data Table (md and up) -->
          <v-data-table
            v-show="$vuetify.display.mdAndUp"
            :headers="tableHeaders"
            :items="activityItems"
            :items-per-page="25"
            class="elevation-0"
            density="comfortable"
          >
            <template v-slot:[`item.note`]="{ item }">
              <span>{{ item.note || 'Activity' }}</span>
            </template>

            <template v-slot:[`item.minutes`]="{ item }">
              <span :class="{'text-error': item.minutes < 0, 'text-success': item.minutes >= 0}">
                {{ item.minutes > 0 ? '+' : '' }}{{ item.minutes }}
              </span>
            </template>

            <template v-slot:[`item.created_at`]="{ item }">
              <span class="text-caption">{{ formatDate(item.created_at) }}</span>
            </template>

            <template v-slot:[`item.type`]="{ item }">
              <v-chip
                v-if="item.interview_id"
                size="small"
                color="primary"
                class="text-none"
                :to="`/interview/${item.interview_id}`"
              >
                <v-icon
                  start
                  size="small"
                  class="mr-1"
                >
                  mdi-microphone
                </v-icon>
                Interview
              </v-chip>
              <v-chip
                v-else-if="item.transaction_id"
                size="small"
                color="success"
                class="text-none"
              >
                <v-icon
                  start
                  size="small"
                  class="mr-1"
                >
                  mdi-shopping
                </v-icon>
                Purchase
              </v-chip>
              <span v-else>-</span>
            </template>
          </v-data-table>

          <!-- Mobile List (sm and down) -->
          <v-list
            v-show="$vuetify.display.smAndDown"
            density="compact"
          >
            <v-list-item
              v-for="(item, index) in activityItems"
              :key="index"
            >
              <v-list-item-title class="d-flex justify-space-between align-center">
                <span>{{ item.note || 'Activity' }}</span>
                <span :class="{'text-error': item.minutes < 0, 'text-success': item.minutes >= 0}">
                  {{ item.minutes > 0 ? '+' : '' }}{{ item.minutes }}
                </span>
              </v-list-item-title>
              <v-list-item-subtitle>
                <div class="d-flex justify-space-between align-center">
                  <span class="text-caption">{{ formatDate(item.created_at) }}</span>
                  <v-chip
                    v-if="item.interview_id"
                    size="x-small"
                    color="primary"
                    class="text-none"
                    :to="`/interview/${item.interview_id}`"
                  >
                    <v-icon
                      start
                      size="x-small"
                      class="mr-1"
                    >
                      mdi-microphone
                    </v-icon>
                    Interview
                  </v-chip>
                  <v-chip
                    v-else-if="item.transaction_id"
                    size="x-small"
                    color="success"
                    class="text-none"
                  >
                    <v-icon
                      start
                      size="x-small"
                      class="mr-1"
                    >
                      mdi-shopping
                    </v-icon>
                    Purchase
                  </v-chip>
                  <span v-else>-</span>
                </div>
              </v-list-item-subtitle>
              <v-divider class="mt-3" />
            </v-list-item>
          </v-list>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { supabase } from '@/plugins/supabase'

const authStore = useAuthStore()

// Activity data
const activityItems = ref([])
const activityLoading = ref(false)
const activityError = ref(null)

// Table headers for desktop view
const tableHeaders = [
  { title: 'Description', key: 'note', align: 'start', sortable: false },
  { title: 'Minutes', key: 'minutes', align: 'end', sortable: true },
  { title: 'Date', key: 'created_at', align: 'start', sortable: true },
  { title: 'Type', key: 'type', align: 'center', sortable: false }
]

// Computed user balance
const userBalance = computed(() => {
  // Calculate balance from activity items
  if (!activityItems.value.length) return 0
  return activityItems.value.reduce((total, item) => total + (item.minutes || 0), 0)
})

// Define route meta to require authentication
defineOptions({
  meta: {
    requiresAuth: true
  }
})

// Format date for display
function formatDate(dateString) {
  if (!dateString) return ''

  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
  }).format(date)
}

// Fetch user_minutes data
async function fetchActivityData() {
  if (!authStore.user) return

  activityLoading.value = true
  activityError.value = null

  try {
    // Fetch minutes records from the user_minutes table
    const { data, error } = await supabase
      .from('user_minutes')
      .select('id, created_at, minutes, note, transaction_id, interview_id')
      .eq('user_id', authStore.user.id)
      .order('created_at', { ascending: false })

    if (error) throw error

    activityItems.value = data || []
  } catch (err) {
    console.error('Error fetching activity data:', err)
    activityError.value = 'Failed to load activity data. Please try again.'
  } finally {
    activityLoading.value = false
  }
}

// Fetch data on component mount
onMounted(async () => {
  await fetchActivityData()
})
</script>

<style scoped>
.border-b {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}
</style>

<route>
{
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}
</route>
