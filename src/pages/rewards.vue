<template>
  <!-- Rewards Content -->
  <div class="flex-grow-1 overflow-y-auto pa-6">
    <div class="d-flex align-center mb-6">
      <h1 class="text-h4">Rewards</h1>
    </div>

    <!-- Referral Code Card -->
    <v-card
      variant="elevated"
      class="mb-4 balance-card"
      color="primary"
      elevation="3"
    >
      <v-card-text class="py-5 px-6">
        <v-row
          align="center"
          no-gutters
        >
          <!-- Left column: Referral code info -->
          <v-col cols="5">
            <p class="text-h6 mb-1">
              Your Referral Code
            </p>
            <v-progress-circular
              v-if="referralLoading"
              indeterminate
              color="white"
              class="my-2"
            />
            <template v-else-if="activeReferralCode">
              <p class="text-h3 mb-0 font-weight-bold">
                {{ activeReferralCode.code }}
              </p>
              <p class="text-caption mt-1">
                Used {{ activeReferralCode.use_count }} times!
              </p>
            </template>
            <p
              v-else
              class="text-body-1"
            >
              No referral code yet. Create one now!
            </p>
          </v-col>

          <!-- Right column: Buttons -->
          <v-col
            cols="7"
            class="d-flex flex-column align-end justify-center"
          >
            <v-btn-group
              v-if="activeReferralCode"
              variant="tonal"
              rounded
              density="comfortable"
              size="small"
              class="mb-3"
            >
              <v-btn
                prepend-icon="mdi-content-copy"
                size="small"
                @click="copyToClipboard(activeReferralCode.code)"
              >
                {{ copied ? 'Copied!' : 'Copy' }}
              </v-btn>
              <v-divider vertical />
              <v-btn
                prepend-icon="mdi-share-variant"
                size="small"
                @click="shareReferralCode"
              >
                Share
              </v-btn>
            </v-btn-group>

            <v-btn
              variant="tonal"
              prepend-icon="mdi-refresh"
              size="small"
              @click="showCreateReferralDialog = true"
            >
              {{ activeReferralCode ? 'Create New Code' : 'Create Code' }}
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Rewards Program Info Card -->
    <v-card
      variant="elevated"
      elevation="3"
      class="mb-16"
    >
      <v-card-text>
        <v-row justify="center">
          <v-col
            cols="12"
            class="text-center"
          >
            <v-icon
              size="48"
              class="mb-3"
              color="primary"
            >
              mdi-gift
            </v-icon>
            <h3 class="text-h5 mb-2">
              Rewards Program
            </h3>
            <p>Share your referral code with friends to earn 20% minutes rewards! You both get a 20% boost in minutes when they make their first purchase.</p>
          </v-col>

          <v-col
            v-if="activeReferralCode"
            cols="12"
          >
            <v-card
              variant="outlined"
              class="mt-4"
            >
              <v-card-title class="text-subtitle-1">
                How it works:
              </v-card-title>
              <v-card-text>
                <v-list
                  density="compact"
                  lines="two"
                >
                  <v-list-item
                    prepend-icon="mdi-account-multiple"
                    title="Share your referral code with friends"
                    subtitle="Send them your unique code via text, email, or social media"
                  />
                  <v-list-item
                    prepend-icon="mdi-account-plus"
                    title="When they sign up, they enter your code"
                    subtitle="Your code must be entered during the registration process"
                  />
                  <v-list-item
                    prepend-icon="mdi-star"
                    title="You both receive bonus minutes"
                    subtitle="When they make their first purchase, you'll both be rewarded"
                  />
                </v-list>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Create Referral Code dialog -->
    <v-dialog
      v-model="showCreateReferralDialog"
      max-width="500px"
    >
      <v-card>
        <v-card-title>Create Referral Code</v-card-title>
        <v-card-text>
          <v-alert
            v-if="activeReferralCode"
            density="comfortable"
            type="info"
            variant="tonal"
            class="mb-4"
          >
            Creating a new referral code will deactivate your current code.
          </v-alert>
          <v-alert
            v-else
            density="comfortable"
            type="info"
            variant="tonal"
            class="mb-4"
          >
            Create a unique referral code that friends can use when they sign up.
          </v-alert>

          <v-form
            ref="referralForm"
            @submit.prevent="saveReferralCode"
          >
            <v-text-field
              v-model="newReferralCode"
              label="Referral Code"
              variant="outlined"
              placeholder="Enter a unique code (min 3 letters)"
              :rules="[
                v => !!v || 'Referral code is required',
                v => v.length >= 3 || 'Referral code must be at least 3 characters',
                v => /^[a-zA-Z0-9]+$/.test(v) || 'Only letters and numbers allowed'
              ]"
              :error-messages="referralCodeError"
              autofocus
              @keyup.enter="saveReferralCode"
            />
          </v-form>

          <v-alert
            v-if="referralCodeError"
            type="error"
            density="compact"
            variant="tonal"
            class="mt-2"
          >
            {{ referralCodeError }}
          </v-alert>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="error"
            variant="text"
            @click="cancelCreateReferralDialog"
          >
            CANCEL
          </v-btn>
          <v-btn
            color="success"
            variant="text"
            :loading="savingReferralCode"
            @click="saveReferralCode"
          >
            SAVE
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Snackbar for referral code actions -->
    <v-snackbar
      v-model="showSnackbar"
      :color="snackbarColor"
      :timeout="3000"
      location="top"
    >
      {{ snackbarMessage }}

      <template #actions>
        <v-btn
          variant="text"
          icon="mdi-close"
          @click="showSnackbar = false"
        />
      </template>
    </v-snackbar>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { supabase } from '@/plugins/supabase'

const authStore = useAuthStore()

// Referral code related states
const referralLoading = ref(false)
const activeReferralCode = ref(null)
const showCreateReferralDialog = ref(false)
const newReferralCode = ref('')
const referralCodeError = ref('')
const savingReferralCode = ref(false)
const referralForm = ref(null)
const copied = ref(false)

// Snackbar for referral code actions
const showSnackbar = ref(false)
const snackbarMessage = ref('')
const snackbarColor = ref('success')

// Define route meta to require authentication
defineOptions({
  meta: {
    requiresAuth: true
  }
})

// Referral code related functions
async function fetchReferralCode() {
  if (!authStore.user) return

  referralLoading.value = true
  console.log('Fetching referral code for user:', authStore.user.id)

  try {
    const { data, error } = await supabase
      .from('referral_codes')
      .select('*')
      .eq('user_id', authStore.user.id)
      .eq('status', 'active')
      .single()

    if (error) {
      if (error.code !== 'PGRST116') { // PGRST116 is "no rows returned" which is expected if no code exists
        console.error('Error fetching referral code:', error)
      } else {
        console.log('No active referral code found for user')
      }
    } else {
      console.log('Found active referral code:', data)
    }

    activeReferralCode.value = data || null
  } catch (err) {
    console.error('Exception fetching referral code:', err)
    activeReferralCode.value = null
  } finally {
    referralLoading.value = false
  }
}

async function saveReferralCode() {
  if (!authStore.user) {
    console.error('No authenticated user found')
    return
  }

  referralCodeError.value = ''
  console.log('Attempting to save referral code:', newReferralCode.value)

  // Basic validation
  if (!newReferralCode.value || newReferralCode.value.trim().length < 3) {
    referralCodeError.value = 'Referral code must be at least 3 characters'
    return
  }

  if (!/^[a-zA-Z0-9]+$/.test(newReferralCode.value)) {
    referralCodeError.value = 'Only letters and numbers allowed'
    return
  }

  savingReferralCode.value = true

  try {
    // First, check if the user is trying to reactivate their own previously used code
    const { data: existingUserCode, error: userCodeError } = await supabase
      .from('referral_codes')
      .select('*')
      .eq('user_id', authStore.user.id)
      .eq('code', newReferralCode.value)
      .eq('status', 'inactive')
      .single()

    console.log('Checking for user\'s inactive code:', existingUserCode, userCodeError)

    // If the user has this code but it's inactive, let them reactivate it
    if (existingUserCode && !userCodeError) {
      console.log('User is reactivating their previous code:', existingUserCode)

      // First, deactivate any currently active codes
      if (activeReferralCode.value) {
        const { error: deactivateError } = await supabase
          .from('referral_codes')
          .update({ status: 'inactive' })
          .eq('user_id', authStore.user.id)
          .eq('status', 'active')

        if (deactivateError) {
          console.error('Error deactivating current code:', deactivateError)
        }
      }

      // Now reactivate the previously used code
      const { error: reactivateError } = await supabase
        .from('referral_codes')
        .update({
          status: 'active',
          // Don't reset the use count when reactivating
        })
        .eq('id', existingUserCode.id)
        .select()
        .single()

      if (reactivateError) {
        throw reactivateError
      }

      // Show success message and update UI
      snackbarMessage.value = 'Your previous referral code has been reactivated'
      snackbarColor.value = 'success'
      showSnackbar.value = true
      showCreateReferralDialog.value = false
      newReferralCode.value = ''

      // Refresh the referral code list
      fetchReferralCode()
      return
    }

    // If we're here, it's not a reactivation scenario
    // Check if this code exists and belongs to someone else
    const { data: existingCode, error: checkError } = await supabase
      .from('referral_codes')
      .select('id')
      .eq('code', newReferralCode.value)
      .neq('user_id', authStore.user.id) // Skip checking the user's own codes
      .eq('status', 'active') // Only check active codes from other users
      .single()

    console.log('Checking if code exists for another user:', existingCode, checkError)

    if (existingCode && !checkError) {
      console.log('Code already exists and belongs to another user:', existingCode)
      referralCodeError.value = 'This code is already in use by another user. Please try a different code.'
      savingReferralCode.value = false
      return
    }

    // Deactivate any existing active codes for this user
    if (activeReferralCode.value) {
      console.log('Deactivating existing code:', activeReferralCode.value.code)
      const { error: updateError } = await supabase
        .from('referral_codes')
        .update({ status: 'inactive' })
        .eq('user_id', authStore.user.id)
        .eq('status', 'active')

      if (updateError) {
        console.error('Error deactivating existing code:', updateError)
      }
    }

    // Then insert the new code
    const { data, error } = await supabase
      .from('referral_codes')
      .insert({
        user_id: authStore.user.id,
        code: newReferralCode.value,
        status: 'active',
        use_count: 0
      })
      .select()

    console.log('Insert result:', data, error)

    if (error) {
      // Check if it's a unique constraint violation
      if (error.code === '23505') { // PostgreSQL unique violation code
        referralCodeError.value = 'This code is already in use. Please try another.'
      } else {
        throw error
      }
    } else {
      // Success! Show the success message and close the dialog
      snackbarMessage.value = 'Referral code created successfully'
      snackbarColor.value = 'success'
      showSnackbar.value = true
      showCreateReferralDialog.value = false
      newReferralCode.value = ''

      // Refresh the referral code list
      fetchReferralCode()
    }
  } catch (err) {
    console.error('Error creating referral code:', err)
    referralCodeError.value = 'Failed to create referral code: ' + (err.message || '')
    snackbarMessage.value = 'Failed to create referral code. Please try again.'
    snackbarColor.value = 'error'
    showSnackbar.value = true
  } finally {
    savingReferralCode.value = false
  }
}

function cancelCreateReferralDialog() {
  newReferralCode.value = ''
  referralCodeError.value = ''
  showCreateReferralDialog.value = false
}

// Copy referral code to clipboard
async function copyToClipboard(text) {
  try {
    await navigator.clipboard.writeText(text)
    copied.value = true
    snackbarMessage.value = 'Referral code copied to clipboard'
    snackbarColor.value = 'success'
    showSnackbar.value = true
    setTimeout(() => {
      copied.value = false
    }, 2000)
  } catch (err) {
    console.error('Failed to copy text: ', err)
    snackbarMessage.value = 'Failed to copy to clipboard'
    snackbarColor.value = 'error'
    showSnackbar.value = true
  }
}

// Share referral code
async function shareReferralCode() {
  if (!activeReferralCode.value) return

  const shareText = `Join me on MissInterview.com and use my referral code: ${activeReferralCode.value.code}`

  // Use Web Share API if available
  if (navigator.share) {
    try {
      await navigator.share({
        title: 'MissInterview.com Referral',
        text: shareText,
        url: window.location.origin
      })
      snackbarMessage.value = 'Referral code shared successfully'
      snackbarColor.value = 'success'
      showSnackbar.value = true
    } catch (err) {
      console.error('Error sharing:', err)
      // Fallback to clipboard if sharing fails
      copyToClipboard(shareText)
      snackbarMessage.value = 'Sharing failed, code copied to clipboard instead'
      snackbarColor.value = 'info'
      showSnackbar.value = true
    }
  } else {
    // Fallback to clipboard if Web Share API not available
    copyToClipboard(shareText)
    snackbarMessage.value = 'Referral message copied to clipboard'
    snackbarColor.value = 'success'
    showSnackbar.value = true
  }
}

// Fetch data on component mount
onMounted(async () => {
  await fetchReferralCode()
})
</script>

<style scoped>
.border-b {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

/* Balance card styling */
.balance-card {
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15) !important;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.balance-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2) !important;
}

.balance-card .v-card-text {
  padding: 24px;
}

/* Theme color specific overrides for balance card */
:root {
  --balance-card-shadow-color: rgba(var(--v-theme-primary), 0.15);
  --balance-card-shadow-hover-color: rgba(var(--v-theme-primary), 0.25);
}

.v-theme--dark .balance-card {
  box-shadow: 0 8px 30px var(--balance-card-shadow-color) !important;
}

.v-theme--dark .balance-card:hover {
  box-shadow: 0 10px 40px var(--balance-card-shadow-hover-color) !important;
}
</style>

<route>
{
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}
</route>
