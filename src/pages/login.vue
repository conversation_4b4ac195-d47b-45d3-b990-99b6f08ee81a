<template>
  <v-container
    fluid
    class="fill-height mx-0 px-4"
  >
    <v-row justify="center">
      <v-col
        cols="12"
        sm="8"
        md="6"
        lg="4"
      >
        <!-- Checkout Complete Message -->
        <v-card
          v-if="route.query.checkout === 'complete'"
          class="elevation-12 rounded-lg my-16 text-center pa-6"
        >
          <v-icon
            icon="mdi-check-circle"
            color="success"
            size="64"
            class="mb-4"
          />
          <h1 class="text-h4 font-weight-bold mb-4">
            Checkout Successful!
          </h1>
          <p class="text-body-1 mb-6">
            Please check your email for instructions on how to set your password and access your account.
          </p>
          <v-btn
            color="primary"
            size="large"
            @click="router.push('/login')"
          >
            Return to Login
          </v-btn>
        </v-card>

        <!-- Login/Signup Card -->
        <v-card
          v-else
          class="elevation-12 rounded-lg my-16"
        >
          <v-card-title class="text-center py-4">
            <h1 class="text-h4 font-weight-bold">
              {{ isLoginMode ? 'Login' : 'Sign Up' }}
            </h1>
          </v-card-title>

          <v-card-text>
            <v-alert
              v-if="errorMessage"
              type="error"
              variant="tonal"
              closable
              class="mb-4"
              @click:close="errorMessage = ''"
            >
              {{ errorMessage }}
            </v-alert>

            <v-alert
              v-if="successMessage"
              type="success"
              variant="tonal"
              closable
              class="mb-4"
              @click:close="successMessage = ''"
            >
              {{ successMessage }}
            </v-alert>

            <!-- Google Login/Signup (Primary Option) -->
            <v-btn
              block
              color="primary"
              size="large"
              class="mt-4 mb-3"
              prepend-icon="mdi-google"
              :loading="authStore.loading"
              @click="handleGoogleLogin"
            >
              {{ isLoginMode ? 'Continue with Google' : 'Sign up with Google' }}
            </v-btn>

            <!-- Facebook Login/Signup -->
            <v-btn
              v-if="false"
              block
              color="blue-darken-3"
              size="large"
              class="mb-6"
              prepend-icon="mdi-facebook"
              :loading="authStore.loading"
              @click="handleFacebookLogin"
            >
              {{ isLoginMode ? 'Continue with Facebook' : 'Sign up with Facebook' }}
            </v-btn>

            <!-- Email Login/Signup Toggle -->
            <div class="d-flex align-center mb-4">
              <v-divider />
              <span class="mx-3 text-grey">or</span>
              <v-divider />
            </div>

            <div class="text-center mb-4">
              <v-btn
                variant="text"
                color="primary"
                @click="showEmailForm = !showEmailForm"
              >
                {{ showEmailForm ? 'Hide email form' : `${isLoginMode ? 'Login' : 'Sign up'} with email instead` }}
              </v-btn>
            </div>

            <!-- Email-based Login/Signup Form -->
            <v-expand-transition>
              <v-form
                v-if="showEmailForm"
                ref="form"
                @submit.prevent="submitForm"
              >
                <!-- First Name Field (Sign Up only) -->
                <v-text-field
                  v-if="!isLoginMode"
                  v-model="firstName"
                  label="First Name"
                  prepend-inner-icon="mdi-account-outline"
                  variant="outlined"
                  :rules="[rules.required]"
                  autocomplete="given-name"
                />

                <!-- Last Name Field (Sign Up only) -->
                <v-text-field
                  v-if="!isLoginMode"
                  v-model="lastName"
                  label="Last Name"
                  prepend-inner-icon="mdi-account-outline"
                  variant="outlined"
                  :rules="[rules.required]"
                  autocomplete="family-name"
                />

                <v-text-field
                  v-model="email"
                  label="Email"
                  prepend-inner-icon="mdi-email-outline"
                  variant="outlined"
                  :rules="[rules.required, rules.email]"
                  autocomplete="email"
                />

                <v-text-field
                  v-model="password"
                  label="Password"
                  prepend-inner-icon="mdi-lock-outline"
                  variant="outlined"
                  :append-inner-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                  :type="showPassword ? 'text' : 'password'"
                  :rules="[rules.required, rules.min]"
                  autocomplete="current-password"
                  @click:append-inner="showPassword = !showPassword"
                />

                <!-- Confirm Password Field (Sign Up only) -->
                <v-text-field
                  v-if="!isLoginMode"
                  v-model="confirmPassword"
                  label="Confirm Password"
                  prepend-inner-icon="mdi-lock-outline"
                  variant="outlined"
                  :append-inner-icon="showConfirmPassword ? 'mdi-eye-off' : 'mdi-eye'"
                  :type="showConfirmPassword ? 'text' : 'password'"
                  :rules="[rules.required, rules.min, passwordMatchRule]"
                  autocomplete="new-password"
                  @click:append-inner="showConfirmPassword = !showConfirmPassword"
                />

                <v-btn
                  block
                  color="primary"
                  size="large"
                  type="submit"
                  :loading="authStore.loading"
                  class="mt-4"
                >
                  {{ isLoginMode ? 'Login with Email' : 'Sign Up with Email' }}
                </v-btn>
              </v-form>
            </v-expand-transition>
          </v-card-text>

          <v-card-actions class="justify-center pb-6">
            <v-btn
              variant="text"
              :disabled="authStore.loading"
              @click="toggleMode"
            >
              {{ isLoginMode ? 'Sign Up' : 'Already have an account? Login' }}
            </v-btn>
            <v-btn
              v-if="isLoginMode"
              variant="text"
              :disabled="authStore.loading"
              @click="showForgotPasswordDialog = true"
            >
              Forgot Password?
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <!-- Forgot Password Dialog -->
    <v-dialog
      v-model="showForgotPasswordDialog"
      max-width="500"
    >
      <v-card>
        <v-card-title class="text-h5 pa-4">
          Reset Password
        </v-card-title>

        <v-card-text class="pa-4">
          <v-alert
            v-if="forgotPasswordError"
            type="error"
            variant="tonal"
            closable
            class="mb-4"
            @click:close="forgotPasswordError = ''"
          >
            {{ forgotPasswordError }}
          </v-alert>

          <v-alert
            v-if="forgotPasswordSuccess"
            type="success"
            variant="tonal"
            closable
            class="mb-4"
            @click:close="forgotPasswordSuccess = ''"
          >
            {{ forgotPasswordSuccess }}
          </v-alert>

          <v-form
            ref="forgotPasswordForm"
            @submit.prevent="handleForgotPassword"
          >
            <v-text-field
              v-model="forgotPasswordEmail"
              label="Email"
              prepend-inner-icon="mdi-email-outline"
              variant="outlined"
              :rules="[rules.required, rules.email]"
              autocomplete="email"
            />

            <v-btn
              block
              color="primary"
              size="large"
              type="submit"
              :loading="authStore.loading"
              class="mt-4"
            >
              Send Reset Instructions
            </v-btn>
          </v-form>
        </v-card-text>

        <v-card-actions class="pa-4">
          <v-spacer />
          <v-btn
            variant="text"
            @click="showForgotPasswordDialog = false"
          >
            Close
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Form state
const form = ref(null)
const email = ref('')
const password = ref('')
const firstName = ref('')
const lastName = ref('')
const confirmPassword = ref('')
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const isLoginMode = ref(true)
const errorMessage = ref('')
const successMessage = ref('')
const showEmailForm = ref(false)

// Forgot password state
const forgotPasswordForm = ref(null)
const forgotPasswordEmail = ref('')
const showForgotPasswordDialog = ref(false)
const forgotPasswordError = ref('')
const forgotPasswordSuccess = ref('')

// Function to update component state based on route
function updateModeFromRoute() {
  const wasLogin = isLoginMode.value;

  // If coming from a signup redirect, set signup mode
  if (route.query.mode === 'signup') {
    isLoginMode.value = false;
  } else {
    isLoginMode.value = true;
  }

  // Only reset email form visibility on actual mode changes
  if (wasLogin !== isLoginMode.value) {
    // Default to hiding email form in login mode, showing in signup mode
    showEmailForm.value = !isLoginMode.value;
  }

  // Reset state when mode changes
  errorMessage.value = '';
  successMessage.value = '';

  // Clear form fields when switching to login mode
  if (isLoginMode.value) {
    firstName.value = '';
    lastName.value = '';
    confirmPassword.value = '';
  }
}

// Check URL query parameters on initial load
onMounted(() => {
  updateModeFromRoute()

  // Make email form visible by default in signup mode
  if (!isLoginMode.value) {
    showEmailForm.value = true
  }

  // Check if user is already authenticated and redirect to dashboard
  checkAuthAndRedirect()
})

// Watch for route changes to update mode
watch(
  () => route.fullPath, // Watch the full path including query params
  () => {
    updateModeFromRoute();

    // Make email form visible by default in signup mode
    if (!isLoginMode.value) {
      showEmailForm.value = true
    }
  }
);

// Watch for authentication state changes
watch(
  () => authStore.isAuthenticated,
  (isAuthenticated) => {
    if (isAuthenticated) {
      checkAuthAndRedirect()
    }
  }
)

// Password match validation
const passwordMatchRule = computed(() => {
  return () => password.value === confirmPassword.value || 'Passwords do not match'
})

// Form validation rules
const rules = {
  required: v => !!v || 'This field is required',
  email: v => /.+@.+\..+/.test(v) || 'Please enter a valid email',
  min: v => v.length >= 6 || 'Password must be at least 6 characters'
}

// Toggle between login and signup modes
function toggleMode() {
  // Reset message states
  errorMessage.value = ''
  successMessage.value = ''

  if (isLoginMode.value) {
    // Navigate to signup page
    router.push('/login?mode=signup')
  } else {
    // Navigate to login page
    router.push('/login')
  }
}

// Handle form submission
async function submitForm() {
  const { valid } = await form.value.validate()

  if (!valid) return

  errorMessage.value = ''
  successMessage.value = ''

  if (isLoginMode.value) {
    // Handle login
    const { success, error } = await authStore.login(email.value, password.value)

    if (success) {
      // Redirect to the original requested URL if it exists, otherwise go to interview page
      const redirectPath = route.query.redirect || '/dashboard'
      router.push(redirectPath)
    } else {
      errorMessage.value = error || 'Login failed. Please try again.'
    }
  } else {
    // Handle signup
    const { success, error, message } = await authStore.signUp(
      email.value,
      password.value,
      {
        first_name: firstName.value,
        last_name: lastName.value
      }
    )

    if (success) {
      successMessage.value = message || 'Account created successfully!'
      // Switch to login mode after successful signup
      isLoginMode.value = true
      showEmailForm.value = false
    } else {
      errorMessage.value = error || 'Sign up failed. Please try again.'
    }
  }
}

// Handle forgot password submission
async function handleForgotPassword() {
  const { valid } = await forgotPasswordForm.value.validate()

  if (!valid) return

  forgotPasswordError.value = ''
  forgotPasswordSuccess.value = ''

  const { success, message, error } = await authStore.resetPassword(forgotPasswordEmail.value)

  if (success) {
    forgotPasswordSuccess.value = message
    // Close the dialog after a delay
    setTimeout(() => {
      showForgotPasswordDialog.value = false
      forgotPasswordEmail.value = ''
    }, 3000)
  } else {
    forgotPasswordError.value = error
  }
}

// Handle Google login
async function handleGoogleLogin() {
  errorMessage.value = ''
  successMessage.value = ''

  // Set an informational message before redirection
  successMessage.value = isLoginMode.value
    ? 'Redirecting to Google login...'
    : 'Redirecting to Google for signup...'

  await authStore.loginWithGoogle()
  // The redirect will be handled by Supabase OAuth flow
}

// Handle Facebook login
async function handleFacebookLogin() {
  errorMessage.value = ''
  successMessage.value = ''

  // Set an informational message before redirection
  successMessage.value = isLoginMode.value
    ? 'Redirecting to Facebook login...'
    : 'Redirecting to Facebook for signup...'

  await authStore.loginWithFacebook()
  // The redirect will be handled by Supabase OAuth flow
}

// Function to check authentication and redirect if needed
function checkAuthAndRedirect() {
  // Don't redirect if we're showing the checkout complete message
  if (route.query.checkout === 'complete') {
    return
  }

  // Don't redirect if auth is still loading
  if (authStore.loading) {
    return
  }

  if (authStore.isAuthenticated) {
    // Redirect to the original requested URL if it exists, otherwise go to dashboard
    const redirectPath = route.query.redirect || '/dashboard'
    router.push(redirectPath)
  }
}

// Also watch for loading state changes in case auth store is still initializing
watch(
  () => authStore.loading,
  (isLoading) => {
    // When loading finishes, check if we should redirect
    if (!isLoading) {
      checkAuthAndRedirect()
    }
  }
)
</script>

<route>
{
  meta: {
    requiresAuth: false,
    layout: 'default'
  }
}
</route>

<style scoped>
.v-card {
  border-radius: 16px;
}
</style>
