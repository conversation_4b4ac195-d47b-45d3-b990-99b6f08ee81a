<template>
  <v-container
    fluid
    class="fill-height pa-4 interview-container mx-0"
    bg-color="background"
  >
    <v-card
      ref="interviewCard"
      class="interview-card"
      bg-color="surface"
      elevation="2"
    >
      <v-card-text
        ref="scrollableContent"
        class="scrollable-content"
      >
        <!-- Card header with title and actions - now part of the scrollable area -->
        <div class="header-area mb-4">
          <v-row
            no-gutters
            class="w-100"
          >
            <v-col
              cols="12"
              sm="9"
            >
              <div class="d-flex flex-column align-start">
                <!-- Back link for mobile - visible only on small screens -->
                <v-btn
                  v-if="isMobileDisplay"
                  variant="text"
                  color="primary"
                  size="small"
                  density="compact"
                  prepend-icon="mdi-arrow-left"
                  class="mb-2 align-self-start"
                  @click="goBackToList"
                >
                  Back to Interviews
                </v-btn>

                <div class="title-container mb-1">
                  <template v-if="!isEditingTitle">
                    <h2
                      class="text-h4 clickable-title"
                      @click="startEditingTitle"
                    >
                      {{ interview?.title || 'Untitled Interview' }}
                    </h2>
                  </template>
                  <v-text-field
                    v-else
                    v-model="editedTitle"
                    variant="outlined"
                    density="compact"
                    hide-details
                    class="title-edit-field"
                    autofocus
                    @keyup.enter="saveTitle"
                    @blur="saveTitle"
                  />
                </div>

                <v-chip
                  v-if="interview"
                  size="small"
                  color="grey"
                  variant="flat"
                  prepend-icon="mdi-calendar"
                  class="mt-1"
                >
                  {{ formatDate(interview.created_at) }}
                </v-chip>
              </div>
            </v-col>

            <v-col
              cols="12"
              sm="3"
              class="d-flex justify-end align-start"
            >
              <!-- Menu with badge when share link exists -->
              <v-menu>
                <template #activator="{ props }">
                  <v-badge
                    v-if="shareToken"
                    color="success"
                    dot
                    location="bottom end"
                    offset-x="3"
                    offset-y="3"
                  >
                    <v-btn
                      size="small"
                      icon="mdi-share-variant"
                      v-bind="props"
                      variant="tonal"
                    />
                  </v-badge>
                  <v-btn
                    v-else
                    size="small"
                    icon="mdi-share-variant"
                    v-bind="props"
                    variant="tonal"
                  />
                </template>
                <v-list>
                  <v-list-item
                    :loading="sharing"
                    @click="shareInterview"
                  >
                    <template #prepend>
                      <v-icon>mdi-share-variant</v-icon>
                    </template>
                    <v-list-item-title>Share</v-list-item-title>
                  </v-list-item>
                  <v-list-item
                    :loading="downloading"
                    @click="downloadReport"
                  >
                    <template #prepend>
                      <v-icon>mdi-file-pdf-box</v-icon>
                    </template>
                    <v-list-item-title>Download Report</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
            </v-col>
          </v-row>
        </div>

        <!-- Loading state -->
        <v-sheet
          v-if="loading"
          class="d-flex justify-center align-center my-4"
          color="transparent"
        >
          <v-progress-circular
            indeterminate
            color="primary"
            size="40"
          />
        </v-sheet>

        <!-- Error state -->
        <v-alert
          v-if="error"
          type="error"
          variant="tonal"
          class="mb-4"
          closable
        >
          {{ error }}
        </v-alert>

        <template v-if="!loading && interview">
          <!-- Interview metadata -->
          <v-sheet
            color="transparent"
            class="mb-3"
          >
            <!-- Comment: Old title display is removed -->
          </v-sheet>

          <h3 class="text-h6 mb-3">
            Recording
          </h3>

          <!-- Sticky Audio Player -->
          <div
            class="sticky-player-container"
            :class="{
              'is-sticky': isAudioPlayerSticky,
              'landscape-mode': mobile && isLandscape
            }"
          >
            <v-card
              class="audio-player-card pa-4"
              :class="{
                'sticky-active': isAudioPlayerSticky,
                'landscape-player': mobile && isLandscape
              }"
              flat
            >
              <!-- Single Audio player when available -->
              <template v-if="recordingUrl">
                <div class="d-flex align-center">
                  <audio
                    ref="audioPlayer"
                    controls
                    class="w-100 audio-element"
                    :class="{'landscape-audio': mobile && isLandscape}"
                    :src="recordingUrl"
                    @timeupdate="onTimeUpdate"
                  >
                    Your browser does not support the audio element.
                  </audio>
                </div>
              </template>

              <!-- Loading state for audio -->
              <v-card
                v-else
                variant="tonal"
                class="pa-3 text-center"
                bg-color="surface"
              >
                <v-progress-circular
                  v-if="interview && !interview.completed_at"
                  indeterminate
                  color="primary"
                  class="mb-3"
                />
                <v-card-text v-if="interview && !interview.completed_at">
                  Recording is being processed...
                </v-card-text>
                <v-card-text v-else>
                  No recording available for this interview.
                </v-card-text>
              </v-card>
            </v-card>
          </div>

          <!-- Empty spacer div when player is sticky -->
          <div
            v-if="isAudioPlayerSticky"
            ref="stickyWrapper"
            class="player-spacer mb-4"
          />

          <!-- Non-sticky wrapper when player is not sticky -->
          <div
            v-else
            ref="stickyWrapper"
            class="mb-4"
          />

          <!-- Tabs for Transcript and Analysis -->
          <v-tabs
            v-model="activeTab"
            bg-color="background"
            color="primary"
            class="mb-4 interview-tabs"
            grow
            center-active
          >
            <v-tab
              value="transcript"
              class="text-body-1"
            >
              Transcript
            </v-tab>
            <v-tab
              value="analysis"
              class="text-body-1"
            >
              Analysis
            </v-tab>
          </v-tabs>

          <!-- Transcript Tab Content -->
          <v-window
            ref="tabWindow"
            v-model="activeTab"
          >
            <!-- Transcript Tab -->
            <v-window-item
              ref="transcriptTab"
              value="transcript"
              class="transcript-tab-content"
            >
              <!-- Transcript section -->
              <div
                ref="transcriptContainer"
                class="transcript-container"
              >
                <v-timeline
                  v-if="messages.length"
                  ref="timelineContainer"
                  density="compact"
                  align="start"
                  class="transcript-timeline"
                >
                  <v-timeline-item
                    v-for="(message, index) in messages"
                    :id="`message-${index}`"
                    :key="index"
                    :ref="el => { if(messageRefs[index] === undefined) messageRefs[index] = el }"
                    :dot-color="message.role === 'bot' ? 'primary' : 'secondary'"
                    :icon="message.role === 'bot' ? 'mdi-robot' : 'mdi-account'"
                    :size="'small'"
                    :class="[
                      { 'active-message': isActiveMessage(message) },
                      `timeline-message-${index}`,
                      'timeline-item-mobile'
                    ]"
                  >
                    <template #opposite>
                      <v-chip
                        size="x-small"
                        label
                        variant="flat"
                        color="grey"
                        class="text-caption"
                      >
                        {{ formatSeconds(message.secondsFromStart) }}
                      </v-chip>
                    </template>
                    <v-card
                      variant="tonal"
                      :class="[
                        'pa-3',
                        'word-wrap',
                        'message-card',
                        message.role === 'bot' ? 'bg-primary-lighten-5' : 'bg-primary-lighten-4',
                        isActiveMessage(message) ? 'active-message-card' : '',
                        'clickable-card',
                        `message-card-${index}`
                      ]"
                      @click="seekToMessage(message)"
                    >
                      <v-card-item class="px-0 py-0">
                        <div class="d-flex align-center mb-1 position-relative">
                          <v-card-title class="px-0 py-0 text-no-wrap text-truncate">
                            <span
                              class="font-weight-bold text-body-2"
                            >{{ message.role === 'bot' ? 'Ai Interviewer' : 'You' }}</span>
                          </v-card-title>
                          <v-btn
                            v-if="isActiveMessage(message)"
                            icon
                            density="compact"
                            size="small"
                            :color="message.role === 'bot' ? 'primary' : 'secondary'"
                            class="playback-toggle-btn ms-auto"
                            elevation="0"
                            @click.stop="togglePlayback"
                          >
                            <v-icon>{{ audioPlayer?.paused ? 'mdi-play' : 'mdi-pause' }}</v-icon>
                          </v-btn>
                        </div>
                      </v-card-item>
                      <v-card-text class="px-0 pt-1 pb-0 text-wrap">
                        {{ message.message }}
                      </v-card-text>
                    </v-card>
                  </v-timeline-item>
                </v-timeline>

                <!-- Loading state for transcript -->
                <v-card
                  v-else
                  variant="tonal"
                  class="pa-3 text-center"
                  bg-color="surface"
                >
                  <v-progress-circular
                    v-if="interview && !interview.completed_at && !interview.end_of_call_report?.artifact?.messages"
                    indeterminate
                    color="primary"
                    class="mb-3"
                  />
                  <v-card-text v-if="interview && !interview.completed_at && !interview.end_of_call_report?.artifact?.messages">
                    Transcript is being processed...
                  </v-card-text>
                  <v-card-text v-else>
                    No transcript available for this interview.
                  </v-card-text>
                </v-card>
              </div>
            </v-window-item>

            <!-- Analysis Tab -->
            <v-window-item
              value="analysis"
              class="page-break-before"
            >
              <v-row>
                <!-- Left column: Feedback -->
                <v-col
                  cols="12"
                  md="6"
                >
                  <FeedbackAnalysis
                    :analysis="interview?.analysis"
                    :loading="interview && !interview.completed_at && !interview.analysis"
                  />
                </v-col>

                <!-- Right column: Filler Words Analysis -->
                <v-col
                  cols="12"
                  md="6"
                  class="filler-words-analysis-col print-full-bg"
                  style="display: flex; flex-direction: column; min-height: 100%; height: 100%;"
                >
                  <div
                    class="filler-words-analysis-bg"
                    style="flex: 1; display: flex; flex-direction: column; min-height: 100%; height: 100%; background: var(--v-theme-surface, #fff);"
                  >
                    <FillerWordsAnalysis
                      :analysis="interview?.analysis"
                      :loading="interview && !interview.completed_at && !interview.analysis"
                    />
                  </div>
                </v-col>
              </v-row>
            </v-window-item>
          </v-window>
        </template>

        <!-- Share Dialog -->
        <v-dialog
          v-model="showShareDialog"
          max-width="500"
        >
          <v-card>
            <v-card-title class="text-h6">
              Share Interview
            </v-card-title>
            <v-card-text>
              <p class="mb-4">
                Share this link with anyone you want to give access to this interview:
              </p>
              <v-text-field
                v-model="shareUrl"
                readonly
                variant="outlined"
                append-inner-icon="mdi-content-copy"
                @click:append-inner="copyShareUrl"
              />
              <v-alert
                v-if="copySuccess"
                type="success"
                variant="tonal"
                class="mt-4"
              >
                Link copied to clipboard!
              </v-alert>
            </v-card-text>
            <v-card-actions>
              <v-btn
                v-if="shareToken"
                color="error"
                variant="text"
                prepend-icon="mdi-link-off"
                :loading="revoking"
                @click="revokeShare"
              >
                Revoke Link
              </v-btn>
              <v-btn
                v-if="shareToken"
                color="warning"
                variant="text"
                prepend-icon="mdi-refresh"
                :loading="regenerating"
                @click="confirmRegenerateShare"
              >
                Regenerate Link
              </v-btn>
              <v-spacer />
              <v-btn
                color="primary"
                variant="tonal"
                @click="showShareDialog = false"
              >
                Close
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <!-- Confirm Regenerate Dialog -->
        <v-dialog
          v-model="showRegenerateConfirm"
          max-width="400"
        >
          <v-card>
            <v-card-title class="text-h6">
              Regenerate Share Link
            </v-card-title>
            <v-card-text>
              <p>
                Regenerating will revoke the existing share link and create a new one. Anyone using the old link will lose access.
              </p>
              <p class="mt-2">
                Do you want to proceed?
              </p>
            </v-card-text>
            <v-card-actions>
              <v-spacer />
              <v-btn
                color="grey"
                variant="tonal"
                @click="showRegenerateConfirm = false"
              >
                Cancel
              </v-btn>
              <v-btn
                color="warning"
                :loading="regenerating"
                @click="regenerateShare"
              >
                Regenerate
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>

        <!-- Download Error Dialog -->
        <v-dialog
          v-model="showDownloadError"
          max-width="400"
        >
          <v-card>
            <v-card-title class="text-h6">
              Download Error
            </v-card-title>
            <v-card-text>
              <p>
                {{ downloadError }}
              </p>
            </v-card-text>
            <v-card-actions>
              <v-spacer />
              <v-btn
                color="primary"
                variant="tonal"
                @click="showDownloadError = false"
              >
                Close
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </v-card-text>
    </v-card>

    <!-- PDF Download Snackbar -->
    <v-snackbar
      v-model="showDownloadSnackbar"
      :timeout="-1"
      color="info"
      location="top right"
    >
      <template #default>
        <div class="d-flex align-center">
          <v-progress-circular
            indeterminate
            color="white"
            size="24"
            width="2"
            class="mr-3"
          />
          <span>Generating PDF report...</span>
        </div>
      </template>
    </v-snackbar>
  </v-container>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { supabase } from '@/plugins/supabase'
import FillerWordsAnalysis from '@/components/FillerWordsAnalysis.vue'
import FeedbackAnalysis from '@/components/FeedbackAnalysis.vue'
import { useThemeStore } from '@/stores/theme'
import { useDisplay } from 'vuetify'
import { useRouter } from 'vue-router'

const props = defineProps({
  id: {
    type: String,
    required: true
  }
})

const authStore = useAuthStore()
const themeStore = useThemeStore()
const { mobile, width, height } = useDisplay()
const router = useRouter()

const interview = ref(null)
const loading = ref(true)
const error = ref(null)
const currentTime = ref(0)
const audioPlayer = ref(null)
const selectedMessage = ref(null)
const showShareDialog = ref(false)
const shareUrl = ref('')
const copySuccess = ref(false)
const sharing = ref(false)
const revoking = ref(false)
const shareToken = ref(null)
const isEditingTitle = ref(false)
const editedTitle = ref('')
const interviewSubscription = ref(null)
const showRegenerateConfirm = ref(false)
const regenerating = ref(false)
const activeTab = ref('transcript')
const downloading = ref(false)
const downloadError = ref(null)
const showDownloadError = ref(false)
const showDownloadSnackbar = ref(false)
const timelineContainer = ref(null)
const lastScrolledMessageIndex = ref(-1)
const messageRefs = ref({})
const interviewCard = ref(null)
const transcriptTab = ref(null)
const transcriptContainer = ref(null)
const tabWindow = ref(null)
const isAudioPlayerSticky = ref(false)
const stickyWrapper = ref(null)
const scrollableContent = ref(null)

const isLandscape = computed(() => {
  // Use display width/height
  const isLandscapeRatio = width.value > height.value
  // Also check media query for additional accuracy
  const mediaQueryIsLandscape = window.matchMedia('(orientation: landscape)').matches
  return isLandscapeRatio && mediaQueryIsLandscape
})

const isMobileDisplay = computed(() => mobile.value && !isLandscape.value)

const isManualScrolling = ref(false)
const isManualSeeking = ref(false)
const isProgrammaticScroll = ref(false)

watch(() => props.id, (newId) => {
  if (newId) {
    fetchInterview()
    setupSubscription()
  }
}, { immediate: true })

const updatePlayerDimensions = () => {
  const cardEl = interviewCard.value?.$el
  if (!cardEl) return

  const cardRect = cardEl.getBoundingClientRect()
  const rightEdge = window.innerWidth - cardRect.right
  const container = document.querySelector('.sticky-player-container')

  if (container) {
    // Check if we're in landscape mode on mobile
    const isLandscapeMobile = mobile.value && isLandscape.value

    if (isLandscapeMobile) {
      // In landscape mobile, use a percentage of the viewport width
      // and don't set the right position (we're using transform: translateX instead)
      container.style.setProperty('--player-width', `calc(100% - 16px)`)

      // Remove right position for landscape as we're centering with transform
      container.style.removeProperty('--player-right')
    } else {
      // Standard dimensions for portrait/desktop
      container.style.setProperty('--player-width', `${cardRect.width}px`)
      container.style.setProperty('--player-right', `${rightEdge}px`)
    }
  }
}

// Debounced resize handler
const debouncedResize = debounce(updatePlayerDimensions, 250)

// Simple debounce function
function debounce(func, delay) {
  let timeout
  return function(...args) {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(this, args), delay)
  }
}

onMounted(() => {
  fetchInterview()
  setupSubscription()

  // Set up scroll listeners
  nextTick(() => {
    const interviewCardEl = interviewCard.value?.$el

    if (interviewCardEl) {
      // Add scroll listener to the card text element which is the scrollable container
      const scrollableContentEl = interviewCardEl.querySelector('.v-card-text')
      if (scrollableContentEl) {
        scrollableContentEl.addEventListener('scroll', throttledHandleScroll)
      }

      // Add resize listener
      window.addEventListener('resize', debouncedResize)

      // Initialize scroll state and CSS variables for player
      setTimeout(() => {
        // Initialize the player width CSS variable
        updatePlayerDimensions()
        throttledHandleScroll()
      }, 500)
    }
  })
})

onUnmounted(() => {
  cleanupSubscription()

  // Clean up scroll event listeners
  const interviewCardEl = interviewCard.value?.$el
  if (interviewCardEl) {
    const scrollableContentEl = interviewCardEl.querySelector('.v-card-text')
    if (scrollableContentEl) {
      scrollableContentEl.removeEventListener('scroll', throttledHandleScroll)
    }
  }

  // Clean up resize listener
  window.removeEventListener('resize', debouncedResize)

  // Clean up debounced scroll timeout
  if (debouncedScrollTimeout) {
    clearTimeout(debouncedScrollTimeout);
    debouncedScrollTimeout = null;
  }
})

// Optimized scroll handler
const throttledHandleScroll = throttle(function() {
  // Don't flag manual scrolling if we're doing programmatic scroll
  if (!isProgrammaticScroll.value) {
    isManualScrolling.value = true;
  }

  const wrapperEl = stickyWrapper.value
  const cardEl = interviewCard.value?.$el

  if (!wrapperEl || !cardEl) {
    isManualScrolling.value = false;
    return;
  }

  // Get the position of the wrapper relative to the viewport
  const rect = wrapperEl.getBoundingClientRect()

  // Account for the header height when determining sticky state
  // Use a smaller header height in landscape mode on mobile
  const isLandscapeMobile = mobile.value && isLandscape.value
  const headerHeight = isLandscapeMobile ? 48 : 64;

  // Get the bounds of the interview card to constrain sticky player
  const cardRect = cardEl.getBoundingClientRect()

  // Store the original width in a CSS variable before going sticky
  if (!isAudioPlayerSticky.value) {
    updatePlayerDimensions()
  }

  // Only activate sticky mode if we're within the interview card area
  if (rect.top <= headerHeight + (isLandscapeMobile ? 32 : 64) && cardRect.top <= headerHeight) {
    isAudioPlayerSticky.value = true;
  } else {
    isAudioPlayerSticky.value = false;
  }

  // Reset manual scrolling flag after a delay
  setTimeout(() => {
    isManualScrolling.value = false;
  }, 500);
}, 100);

const recordingUrl = computed(() => interview.value?.end_of_call_report?.recordingUrl || null)
const messages = computed(() => {
  if (!interview.value?.end_of_call_report?.artifact?.messages) return []
  return interview.value.end_of_call_report.artifact.messages.filter(
    msg => msg.role === 'bot' || msg.role === 'user'
  )
})

function formatDate(dateString) {
  if (!dateString) return ''
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

function formatSeconds(seconds) {
  if (seconds === undefined || seconds === null) return '00:00'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

function throttle(func, delay) {
  let lastCall = 0;
  return function(...args) {
    const now = new Date().getTime();
    if (now - lastCall >= delay) {
      lastCall = now;
      return func(...args);
    }
  };
}

function onTimeUpdate(event) {
  // Don't update if we're in the middle of a manual scroll operation
  if (isManualScrolling.value) return;

  currentTime.value = event.target.currentTime

  // Find the active message based on the current time, but do it less frequently
  if (!isManualSeeking.value && !debouncedScrollInProgress) {
    debouncedScrollToActiveMessage();
  }
}

// Add variables to control debounced scrolling
let debouncedScrollInProgress = false;
let debouncedScrollTimeout = null;

// Debounced function to scroll to the active message
function debouncedScrollToActiveMessage() {
  // Mark that we're in the process of debounced scrolling
  debouncedScrollInProgress = true;

  // Clear any existing timeout
  if (debouncedScrollTimeout) {
    clearTimeout(debouncedScrollTimeout);
  }

  // Set a new timeout
  debouncedScrollTimeout = setTimeout(() => {
    // Find the active message index
    const activeIndex = findActiveMessageIndex();

    // Only scroll if we found an active message and it's different from the last one
    if (activeIndex !== -1 && activeIndex !== lastScrolledMessageIndex.value) {
      console.log(`Auto-scrolling to message ${activeIndex} at time ${currentTime.value.toFixed(1)}`);
      scrollToMessageByIndex(activeIndex);
    }

    // Reset the debounced scroll flag
    debouncedScrollInProgress = false;
  }, 500); // Use a longer delay for smoother behavior
}

function findActiveMessageIndex() {
  if (!messages.value || !messages.value.length || !currentTime.value) {
    return -1;
  }

  return messages.value.findIndex((message, index) => {
    const adjustedStart = Math.max(0, (message.secondsFromStart || 0) - 1);
    const nextMsgStart = index < messages.value.length - 1
      ? Math.max(0, (messages.value[index + 1].secondsFromStart || Infinity) - 1)
      : Infinity;

    return currentTime.value >= adjustedStart && currentTime.value < nextMsgStart;
  });
}

function scrollToMessageByIndex(index) {
  if (index === -1 || activeTab.value !== 'transcript') return;

  // Prevent duplicate scrolling
  if (lastScrolledMessageIndex.value === index) return;

  // Update the last scrolled index
  lastScrolledMessageIndex.value = index;

  console.log(`Attempting to scroll to message index: ${index}`);

  // Set programmatic scroll flag
  isProgrammaticScroll.value = true;

  // Use nextTick to ensure DOM is ready
  nextTick(() => {
    // Use a much more direct and specific selector to find the message card
    const messageCardSelector = `.message-card-${index}`;
    const messageCard = document.querySelector(messageCardSelector);

    // Check if we found the message card
    if (!messageCard) {
      console.error(`Could not find message card with selector: ${messageCardSelector}`);
      isProgrammaticScroll.value = false;
      return;
    }

    // Get the scrollable container
    const container = scrollableContent.value?.$el || document.querySelector('.interview-card .v-card-text');
    if (!container) {
      console.error('Could not find scrollable container');
      isProgrammaticScroll.value = false;
      return;
    }

    // Log element details for debugging
    console.log('Message card:', messageCard);
    console.log('Container:', container);

    try {
      // Get the actual visible height of the container (not the scrollable height)
      const containerVisibleHeight = container.clientHeight;
      const containerScrollTop = container.scrollTop;

      // Calculate the message's position relative to the container's content
      const messageOffsetTop = messageCard.offsetTop;

      // Calculate where to scroll to center the message in the visible area
      // Account for the sticky player height
      const stickyPlayerHeight = isAudioPlayerSticky.value ? 110 : 0;
      const availableHeight = containerVisibleHeight - stickyPlayerHeight;
      const targetScrollTop = messageOffsetTop - (availableHeight / 2) + (messageCard.offsetHeight / 2);

      console.log('Container visible height:', containerVisibleHeight);
      console.log('Container scroll top:', containerScrollTop);
      console.log('Message offset top:', messageOffsetTop);
      console.log('Target scroll position:', targetScrollTop);

      // Execute the scroll with smooth behavior
      container.scrollTo({
        top: Math.max(0, targetScrollTop),
        behavior: 'smooth'
      });

      // Reset programmatic scroll flag after animation completes
      setTimeout(() => {
        isProgrammaticScroll.value = false;
      }, 800); // Slightly longer than the smooth scroll animation

    } catch (err) {
      console.error('Error scrolling to message:', err);
      isProgrammaticScroll.value = false;

      // Fallback approach - use scrollIntoView
      try {
        messageCard.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        });

        setTimeout(() => {
          isProgrammaticScroll.value = false;
        }, 800);
      } catch (fallbackErr) {
        console.error('Even fallback scrolling failed:', fallbackErr);
        isProgrammaticScroll.value = false;
      }
    }
  });
}

function seekToMessage(message) {
  if (!recordingUrl.value) return;

  if (!audioPlayer.value) return;

  // Set flags to indicate this is a manual seek operation
  isManualSeeking.value = true;
  selectedMessage.value = message;

  // Get the index of the message
  const index = messages.value.indexOf(message);
  if (index === -1) {
    console.error('Cannot find message index to seek to');
    isManualSeeking.value = false;
    return;
  }

  console.log(`Seeking to message at index ${index} at time ${message.secondsFromStart}`);

  // Seek to 1 second before the message starts
  const seekTime = Math.max(0, (message.secondsFromStart || 0) - 1);
  audioPlayer.value.currentTime = seekTime;

  // Scroll to the message - passing the index directly
  scrollToMessageByIndex(index);

  // Start playback
  audioPlayer.value.play()
    .then(() => {
      // Reset the flags after a delay
      setTimeout(() => {
        isManualSeeking.value = false;
      }, 500);
    })
    .catch(err => {
      console.error('Error playing audio:', err);
      isManualSeeking.value = false;
    });
}

function togglePlayback() {
  if (!audioPlayer.value) return

  if (audioPlayer.value.paused) {
    audioPlayer.value.play().catch(err => console.error('Error playing audio:', err))
  } else {
    audioPlayer.value.pause()
  }
}

function isActiveMessage(message) {
  if (!currentTime.value || !message.secondsFromStart) return false

  const index = messages.value.indexOf(message)
  const adjustedStart = Math.max(0, (message.secondsFromStart || 0) - 1)
  const nextMsgStart = index < messages.value.length - 1
    ? Math.max(0, (messages.value[index + 1].secondsFromStart || Infinity) - 1)
    : Infinity

  return (
    currentTime.value >= adjustedStart &&
    currentTime.value < nextMsgStart
  ) || message === selectedMessage.value
}

function setupSubscription() {
  cleanupSubscription()

  if (!props.id) return

  interviewSubscription.value = supabase
    .channel(`interview-${props.id}`)
    .on('postgres_changes', {
      event: 'UPDATE',
      schema: 'public',
      table: 'interviews',
      filter: `id=eq.${props.id}`
    }, (payload) => {
      console.log('Interview updated via realtime:', payload)
      if (payload.new) {
        if (interview.value) {
          const end_of_call_report = interview.value.end_of_call_report || null;

          console.log('Current end_of_call_report:', end_of_call_report);
          console.log('Payload end_of_call_report:', payload.new.end_of_call_report);

          interview.value = {
            ...interview.value,
            ...payload.new,
            end_of_call_report: payload.new.end_of_call_report || end_of_call_report,
            analysis: payload.new.analysis || interview.value.analysis
          }

          console.log('Updated interview with merged data:', interview.value);
        } else {
          console.log('No existing interview data, fetching complete interview');
          fetchInterview();
        }
      }
    })
    .subscribe((status) => {
      console.log('Subscription status:', status)
    })
}

function cleanupSubscription() {
  if (interviewSubscription.value) {
    supabase.removeChannel(interviewSubscription.value)
    interviewSubscription.value = null
  }
}

async function fetchInterview() {
  if (!props.id) return

  loading.value = true
  error.value = null

  try {
    const { data: interviewData, error: fetchError } = await supabase
      .from('interviews')
      .select('*, end_of_call_report, analysis')
      .eq('id', props.id)
      .single()

    if (fetchError) throw fetchError

    if (!interviewData) {
      error.value = 'Interview not found'
      return
    }

        // Check if current user is the interview owner
    const isOwner = interviewData.user_id === authStore.user.id

    // Ensure user profile is loaded
    if (!authStore.userProfile && !isOwner) {
      await authStore.fetchUserProfile()
    }

    // Check if user is admin using the auth store's isAdmin computed property
    const isAdmin = authStore.isAdmin

    // Allow access to admins or the interview owner
    if (!isOwner && !isAdmin) {
      error.value = 'You do not have access to this interview'
      return
    }

    interview.value = interviewData
    await fetchShareToken()
  } catch (err) {
    console.error('Error fetching interview:', err)
    error.value = 'Failed to load interview. Please try again.'
  } finally {
    loading.value = false
  }
}

async function shareInterview() {
  sharing.value = true
  try {
    if (shareToken.value) {
      const themeParams = new URLSearchParams({
        theme: themeStore.themeName,
        mode: themeStore.mode
      }).toString()

      shareUrl.value = `${window.location.origin}/shared/${shareToken.value}?${themeParams}`
      showShareDialog.value = true
      return
    }

    const interviewId = parseInt(interview.value.id, 10);
    if (isNaN(interviewId)) {
      throw new Error('Invalid interview ID format');
    }

    const { data: tokenData, error: tokenError } = await supabase.rpc('generate_share_token', {
      p_interview_id: interviewId,
      p_self_destruct: false,
      p_theme_name: themeStore.themeName,
      p_theme_mode: themeStore.mode
    })

    if (tokenError) throw tokenError

    shareToken.value = tokenData

    shareUrl.value = `${window.location.origin}/shared/${shareToken.value}`
    showShareDialog.value = true
  } catch (err) {
    console.error('Error generating share token:', err)
    downloadError.value = err.message || 'Failed to generate share link. Please try again.';
    showDownloadError.value = true;
  } finally {
    sharing.value = false
  }
}

async function revokeShare() {
  revoking.value = true
  try {
    const interviewId = parseInt(interview.value.id, 10);
    if (isNaN(interviewId)) {
      throw new Error('Invalid interview ID format');
    }

    const { error: revokeError } = await supabase
      .from('share_tokens')
      .update({ revoked_at: new Date().toISOString() })
      .eq('interview_id', interviewId)
      .is('revoked_at', null)

    if (revokeError) throw revokeError

    shareToken.value = null
    shareUrl.value = ''

    if (!regenerating.value) {
      showShareDialog.value = false
    }
  } catch (err) {
    console.error('Error revoking share token:', err)
    downloadError.value = err.message || 'Failed to revoke share link. Please try again.';
    showDownloadError.value = true;
  } finally {
    revoking.value = false
  }
}

async function copyShareUrl() {
  try {
    await navigator.clipboard.writeText(shareUrl.value)
    copySuccess.value = true
  } catch (err) {
    console.error('Error copying to clipboard:', err)
  }
}

async function fetchShareToken() {
  try {
    if (!interview.value) {
      console.log('No interview loaded yet, skipping fetchShareToken');
      return;
    }

    const interviewId = parseInt(interview.value.id, 10);
    if (isNaN(interviewId)) {
      console.error('Invalid interview ID format in fetchShareToken');
      return;
    }

    const { data: tokenData, error: tokenError } = await supabase
      .from('share_tokens')
      .select('token, self_destruct')
      .eq('interview_id', interviewId)
      .is('revoked_at', null)
      .eq('self_destruct', false)
      .maybeSingle()

    if (tokenError) throw tokenError

    if (tokenData) {
      shareToken.value = tokenData.token

      shareUrl.value = `${window.location.origin}/shared/${shareToken.value}?${themeStore.getThemeParams()}`
    }
  } catch (err) {
    console.error('Error fetching share token:', err)
  }
}

function startEditingTitle() {
  if (!interview.value) return
  editedTitle.value = interview.value.title || ''
  isEditingTitle.value = true
}

async function saveTitle() {
  if (!interview.value) return

  if (editedTitle.value === interview.value.title) {
    isEditingTitle.value = false
    return
  }

  try {
    const { error: updateError } = await supabase
      .from('interviews')
      .update({ title: editedTitle.value || null })
      .eq('id', interview.value.id)

    if (updateError) throw updateError

    interview.value.title = editedTitle.value

    const event = new CustomEvent('interview-updated', {
      detail: {
        id: interview.value.id,
        title: editedTitle.value
      },
      bubbles: true
    })
    document.dispatchEvent(event)
  } catch (err) {
    console.error('Error updating interview title:', err)
  } finally {
    isEditingTitle.value = false
  }
}

function confirmRegenerateShare() {
  showRegenerateConfirm.value = true
}

async function regenerateShare() {
  regenerating.value = true
  try {
    await revokeShare()
    await shareInterview()
  } catch (err) {
    console.error('Error regenerating share:', err)
    downloadError.value = err.message || 'Failed to regenerate share link. Please try again.';
    showDownloadError.value = true;
  } finally {
    regenerating.value = false
    showRegenerateConfirm.value = false
  }
}

async function downloadReport() {
  downloading.value = true
  downloadError.value = null;
  showDownloadSnackbar.value = true;
  console.log('Setting showDownloadSnackbar to true', showDownloadSnackbar.value);
  try {
    if (!interview.value || !interview.value.id) {
      throw new Error('Invalid interview data. Please refresh the page and try again.')
    }
    const isLocalDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
    if (isLocalDev) {
      console.log('Local development detected - simulating PDF generation')
      await new Promise(resolve => setTimeout(resolve, 5000))
      console.log('Simulation complete after 5 seconds')
      const blob = new Blob(['Dummy PDF content'], { type: 'application/pdf' })
      const downloadUrl = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = downloadUrl
      a.download = `${interview.value.title || 'Interview'}_report.pdf`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(downloadUrl)
      return
    }
    const interviewId = parseInt(interview.value.id, 10);
    if (isNaN(interviewId)) {
      throw new Error('Invalid interview ID format');
    }
    console.log('Interview ID:', interviewId, 'Type:', typeof interviewId)
    const { data: tokenData, error: tokenError } = await supabase.rpc('generate_share_token', {
      p_interview_id: interviewId,
      p_self_destruct: true,
      p_theme_name: themeStore.themeName,
      p_theme_mode: themeStore.mode
    })
    if (tokenError) {
      console.error('Token generation error:', tokenError)
      throw new Error('Failed to generate share token: ' + tokenError.message)
    }
    if (!tokenData) {
      throw new Error('No token returned from server')
    }
    const shareUrl = `${window.location.origin}/shared/${tokenData}?self_destruct=true`
    console.log('Generated self-destructing share URL with theme params:', shareUrl)
    const response = await fetch('https://automation.aiedgemedia.com/webhook/generate-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ url: shareUrl })
    })
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error')
      console.error('PDF generation failed, status:', response.status, 'response:', errorText)
      throw new Error(`Failed to generate PDF: ${response.status} ${response.statusText}`)
    }
    const blob = await response.blob()
    const downloadUrl = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = downloadUrl
    a.download = `${interview.value.title || 'Interview'}_report.pdf`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(downloadUrl)
  } catch (err) {
    console.error('Error downloading report:', err)
    downloadError.value = err.message || 'Failed to download report. Please try again.';
    showDownloadError.value = true;
  } finally {
    downloading.value = false
    showDownloadSnackbar.value = false;
    console.log('Setting showDownloadSnackbar to false', showDownloadSnackbar.value);
  }
}

function goBackToList() {
  router.push('/interviews')
}
</script>

<style scoped>
.interview-container {
  position: relative;
  padding: 0 !important;
  height: calc(100vh - 64px - 56px); /* Account for header (64px) and footer (56px) */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent container from scrolling */
}

.interview-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
  max-height: calc(100vh - 64px - 56px); /* Limit to viewport height minus header and footer */
  overflow: hidden;
  border-radius: 8px;
}

.interview-card .v-card-text {
  flex: 1;
  overflow-y: auto; /* Enable scrolling in the card content */
  padding: 24px 24px 120px; /* Add extra padding at the bottom for better scroll experience */
  scroll-behavior: smooth; /* Ensure smooth scrolling behavior everywhere */
  height: 100%;
  overscroll-behavior: contain; /* Prevent scroll chaining */
}

.header-area {
  padding-top: 12px;
  padding-bottom: 12px;
}

/* Sticky audio player styles */
.sticky-player-container {
  position: relative;
  transition: all 0.3s ease;
  z-index: 5;
  width: 100%; /* Ensure full width even when not sticky */
  box-sizing: border-box; /* Include padding in width calculation */
  max-width: 100%; /* Ensure it never exceeds container width */
}

.sticky-player-container.is-sticky {
  position: fixed;
  /* Adjust top position to account for the site header height */
  top: 64px; /* Standard header height */
  /* Match the width of the parent container exactly */
  width: var(--player-width, 100%);
  right: var(--player-right, 24px);
  background-color: var(--v-theme-surface);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0 24px;
  z-index: 100;
}

.audio-player-card {
  transition: all 0.3s ease;
  width: 100%;
  max-width: 100%; /* Ensure it never exceeds container width */
  box-sizing: border-box; /* Include padding in width calculation */
}

.sticky-player-container.is-sticky .audio-player-card {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  border-radius: 4px;
}

.sticky-active {
  border-left: 3px solid var(--v-primary-base) !important;
  background-color: var(--v-surface-variant-base) !important;
}

.sticky-label {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

/* Spacer to preserve layout when player becomes sticky */
.player-spacer {
  height: 98px; /* Adjust based on the height of your audio player */
  width: 100%;
}

.transcript-container {
  position: relative;
  width: 100%;
  max-width: 100%;
}

.transcript-timeline {
  position: relative;
  width: 100%;
  max-width: 100%;
}

.transcript-tab-content {
  overflow: visible !important;
  height: auto !important;
}

.v-timeline-item {
  scroll-margin-top: 170px; /* Increase to account for the sticky player */
  scroll-margin-bottom: 50px;
}

.message-card {
  width: 100%;
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  box-sizing: border-box;
}

.timeline-item-mobile {
  width: 100%;
  box-sizing: border-box;
}

:deep(.v-timeline-item__body) {
  max-width: 100% !important;
  width: 100% !important;
  box-sizing: border-box !important;
  padding-right: 8px;
}

.clickable-title {
  cursor: pointer;
}

.clickable-title:hover {
  text-decoration: underline;
}

.title-edit-field {
  min-width: 300px;
}

.active-message {
  position: relative;
}

.active-message::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: var(--v-primary-base);
}

.clickable-card {
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
}

.clickable-card:hover {
  transform: translateX(4px);
}

.active-message-card {
  border: 2px solid var(--v-primary-base);
  border-radius: 8px;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.15);
  transform: translateX(4px);
}

.user-message-card {
  background-color: var(--v-secondary-lighten-5, #f5f5f5);
}

.user-name {
  color: var(--v-primary-base, #1976d2);
}

.user-message-text {
  color: var(--v-primary-darken-1, #1565c0);
}

.playback-toggle-btn {
  position: relative;
  top: auto;
  right: auto;
  margin-left: auto;
}

/* Text wrapping utilities */
.text-wrap {
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  width: 100%;
}

.word-wrap {
  max-width: 100%;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  hyphens: auto;
}

/* Make sure the Vuetify window doesn't have any hidden overflows */
:deep(.v-window) {
  overflow: visible !important;
}

:deep(.v-window__container) {
  overflow: visible !important;
}

@media print {
  .page-break-before {
    page-break-before: always;
  }
  .filler-words-analysis-col.print-full-bg,
  .filler-words-analysis-bg {
    min-height: 100vh !important;
    height: 100vh !important;
    background: var(--v-theme-surface, #fff) !important;
    box-sizing: border-box;
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;
  }
  .filler-words-analysis-bg {
    padding-bottom: 0 !important;
  }
  .v-dialog,
  .v-menu,
  .v-snackbar,
  .v-app-bar,
  .v-btn-group.v-theme--light.bg-primary {
    display: none !important;
  }
}

@media (max-width: 960px) {
  .sticky-player-container.is-sticky {
    width: var(--player-width, 100%); /* Use the same dynamic width variable */
    left: 0;
    right: 0;
    margin-left: 0;
    padding: 0 16px;
  }

  .sticky-player-container,
  .audio-player-card {
    max-width: 100%;
    width: 100%;
  }
}

@media (max-width: 600px) {
  .interview-card {
    height: calc(100vh - 32px);
  }

  .interview-card .v-card-text {
    padding: 16px 16px 100px; /* Reduce padding for mobile */
  }

  .sticky-player-container.is-sticky {
    padding: 0 12px;
    width: 100%; /* Full width on mobile */
    left: 0;
    right: 0;
    /* Adjust top position for mobile */
    top: 56px; /* Mobile header is usually shorter */
  }

  .sticky-player-container,
  .audio-player-card {
    max-width: 100%;
    width: 100%;
    padding-left: 0;
    padding-right: 0;
  }

  .player-spacer {
    height: 90px; /* Smaller spacer on mobile */
  }

  /* Adjust timeline layout for mobile */
  .v-timeline {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
  }

  :deep(.v-timeline-divider__dot) {
    margin-right: 8px;
  }

  :deep(.v-timeline-divider__inner-dot) {
    height: 24px;
    width: 24px;
    margin: 0;
  }

  :deep(.v-timeline-item__body) {
    max-width: 100% !important;
    padding-right: 8px;
    box-sizing: border-box !important;
  }

  :deep(.v-timeline-item__opposite) {
    max-width: 50px;
    margin-right: 4px;
  }

  /* Ensure the button stays visible and properly positioned */
  .playback-toggle-btn {
    position: relative;
    top: auto;
    right: auto;
    margin-left: auto;
  }

  .message-card {
    max-width: 100% !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  .audio-element {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Make v-chip in opposite slot more compact */
  :deep(.v-timeline-item__opposite .v-chip) {
    font-size: 10px !important;
    height: 20px !important;
    min-width: 40px !important;
  }
}

/* Landscape mode specific styles */
@media (max-width: 960px) and (orientation: landscape) {
  .interview-card {
    height: calc(100vh - 16px);
    max-height: 100vh;
  }

  .interview-card .v-card-text {
    padding: 12px 16px 80px;
  }

  .sticky-player-container {
    max-width: 100%;
  }

  .sticky-player-container.is-sticky {
    top: 48px; /* Reduced top position in landscape mode */
    padding: 0 8px;
    max-width: calc(100% - 16px);
    margin: 0 auto;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 16px) !important;
  }

  /* Make the audio player more compact in landscape */
  .audio-player-card {
    padding: 8px !important;
  }

  .landscape-player {
    padding: 4px !important;
  }

  .landscape-audio {
    height: 36px !important;
  }

  .landscape-mode.is-sticky {
    z-index: 999;
    width: calc(100% - 16px) !important;
    margin: 0 auto;
  }

  /* Adjust the transcript timeline for landscape */
  .transcript-timeline {
    padding-left: 8px;
    padding-right: 8px;
  }

  /* Reduce spacing in the timeline for landscape orientation */
  :deep(.v-timeline-divider__dot) {
    margin-right: 4px;
  }

  :deep(.v-timeline-item__body) {
    padding-left: 8px;
    padding-right: 4px;
  }

  /* Adjust the transcript cards for landscape */
  .message-card {
    padding: 6px !important;
  }

  /* Adjust tabs for landscape */
  .interview-tabs :deep(.v-tab) {
    min-width: 90px;
    padding: 0 8px;
  }

  /* Make header area more compact in landscape */
  .header-area {
    padding-top: 6px;
    padding-bottom: 6px;
    margin-bottom: 12px !important;
  }

  /* Adjust title size in landscape */
  .clickable-title {
    font-size: 1.25rem !important;
  }

  /* Player spacer adjustment for landscape */
  .player-spacer {
    height: 52px;
  }

  /* Force center alignment in landscape mode */
  .audio-element {
    margin: 0 auto;
  }

  /* Timeline item adjustments for landscape */
  :deep(.v-timeline-item) {
    margin-bottom: 8px !important;
    min-height: unset !important;
  }

  :deep(.v-timeline-item__opposite) {
    max-width: 40px;
    padding: 0 4px !important;
  }

  :deep(.v-timeline-item__opposite .v-chip) {
    min-width: 35px !important;
    padding: 0 2px !important;
    font-size: 10px !important;
  }

  /* Adjust message card text size */
  .message-card .v-card-text {
    font-size: 0.875rem !important;
    padding-top: 4px !important;
  }

  .message-card .v-card-title {
    font-size: 0.875rem !important;
  }

  /* Force center alignment in landscape mode */
  .audio-element {
    margin: 0 auto;
  }
}
</style>

<route>
{
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}
</route>

