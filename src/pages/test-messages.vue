<template>
  <div class="pa-4">
    <h1>Messaging System Test</h1>

    <v-card class="mb-4">
      <v-card-title>Authentication Status</v-card-title>
      <v-card-text>
        <p><strong>Authenticated:</strong> {{ authStore.isAuthenticated }}</p>
        <p><strong>User ID:</strong> {{ authStore.user?.id }}</p>
        <p><strong>Email:</strong> {{ authStore.userProfile?.email }}</p>
        <p><strong>Role:</strong> {{ authStore.userProfile?.role }}</p>
      </v-card-text>
    </v-card>

    <v-card class="mb-4">
      <v-card-title>Messages Store Status</v-card-title>
      <v-card-text>
        <p><strong>Loading:</strong> {{ messagesStore.loading }}</p>
        <p><strong>Error:</strong> {{ messagesStore.error }}</p>
        <p><strong>Conversations Count:</strong> {{ messagesStore.conversations.length }}</p>
        <p><strong>Active Conversation ID:</strong> {{ messagesStore.activeConversationId }}</p>
        <p><strong>Active Messages Count:</strong> {{ messagesStore.activeMessages.length }}</p>
        <p><strong>Communities Count:</strong> {{ messagesStore.communities.length }}</p>
      </v-card-text>
    </v-card>

    <v-card class="mb-4">
      <v-card-title>Test Login</v-card-title>
      <v-card-text>
        <v-text-field
          v-model="testEmail"
          label="Test Email"
          variant="outlined"
          class="mb-2"
        />
        <v-text-field
          v-model="testPassword"
          label="Test Password"
          type="password"
          variant="outlined"
          class="mb-2"
        />
        <v-btn @click="testLogin" color="success" class="mr-2">
          Test Login
        </v-btn>
        <v-btn @click="testLogout" color="warning">
          Logout
        </v-btn>
      </v-card-text>
    </v-card>

    <v-card class="mb-4">
      <v-card-title>Actions</v-card-title>
      <v-card-text>
        <v-btn @click="loadConversations" class="mr-2" color="primary">
          Load Conversations
        </v-btn>
        <v-btn @click="testCreateMessage" class="mr-2" color="secondary">
          Test Create Message
        </v-btn>
        <v-btn @click="initializeAuth" color="info">
          Initialize Auth
        </v-btn>
      </v-card-text>
    </v-card>

    <v-card v-if="messagesStore.conversations.length > 0">
      <v-card-title>Conversations</v-card-title>
      <v-card-text>
        <v-list>
          <v-list-item
            v-for="conversation in messagesStore.conversations"
            :key="conversation.id"
            @click="selectConversation(conversation.id)"
          >
            <v-list-item-title>
              {{ conversation.title || `${conversation.type} conversation` }}
            </v-list-item-title>
            <v-list-item-subtitle>
              Type: {{ conversation.type }} | ID: {{ conversation.id }}
            </v-list-item-subtitle>
          </v-list-item>
        </v-list>
      </v-card-text>
    </v-card>

    <v-card v-if="messagesStore.activeMessages.length > 0" class="mt-4">
      <v-card-title>Messages ({{ messagesStore.activeMessages.length }})</v-card-title>
      <v-card-text>
        <v-list>
          <v-list-item
            v-for="message in messagesStore.activeMessages"
            :key="message.id"
          >
            <v-list-item-title>
              {{ message.content }}
            </v-list-item-title>
            <v-list-item-subtitle>
              From: {{ message.sender?.first_name }} {{ message.sender?.last_name }} |
              {{ new Date(message.created_at).toLocaleString() }}
            </v-list-item-subtitle>
          </v-list-item>
        </v-list>
      </v-card-text>
    </v-card>

    <v-card class="mt-4">
      <v-card-title>Test New Message</v-card-title>
      <v-card-text>
        <v-text-field
          v-model="testMessageContent"
          label="Test Message Content"
          variant="outlined"
          class="mb-2"
        />
        <v-btn
          @click="sendTestMessage"
          color="primary"
          :disabled="!messagesStore.activeConversationId || !testMessageContent"
        >
          Send Test Message
        </v-btn>
      </v-card-text>
    </v-card>

    <v-card class="mt-4">
      <v-card-title>Debug Information</v-card-title>
      <v-card-text>
        <pre>{{ debugInfo }}</pre>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup>
import { onMounted, ref, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useMessagesStore } from '@/stores/messages'

const authStore = useAuthStore()
const messagesStore = useMessagesStore()

// Test login variables
const testEmail = ref('<EMAIL>')
const testPassword = ref('')
const testMessageContent = ref('Hello from the test page!')

// Debug information
const debugInfo = computed(() => {
  return {
    auth: {
      isAuthenticated: authStore.isAuthenticated,
      userId: authStore.user?.id,
      userEmail: authStore.userProfile?.email,
      userRole: authStore.userProfile?.role,
      loading: authStore.loading,
      error: authStore.error
    },
    messages: {
      conversationsCount: messagesStore.conversations.length,
      activeConversationId: messagesStore.activeConversationId,
      activeMessagesCount: messagesStore.activeMessages.length,
      communitiesCount: messagesStore.communities.length,
      loading: messagesStore.loading,
      error: messagesStore.error
    }
  }
})

onMounted(async () => {
  console.log('Test page mounted')
  await initializeAuth()
  await loadConversations()
})

async function initializeAuth() {
  console.log('Initializing auth...')
  await authStore.initialize()
  console.log('Auth initialized:', authStore.isAuthenticated)
}

async function loadConversations() {
  console.log('Loading conversations...')
  await messagesStore.fetchConversations()
  console.log('Conversations loaded:', messagesStore.conversations.length)
}

async function selectConversation(conversationId) {
  console.log('Selecting conversation:', conversationId)
  await messagesStore.fetchMessages(conversationId)
  messagesStore.setActiveConversation(conversationId)
  console.log('Messages loaded:', messagesStore.messages[conversationId]?.length || 0)
}

async function testCreateMessage() {
  if (!messagesStore.activeConversationId) {
    alert('Please select a conversation first')
    return
  }

  console.log('Creating test message...')
  await messagesStore.sendMessage(messagesStore.activeConversationId, 'This is a test message from the test page!')
  console.log('Test message sent')
}

async function testLogin() {
  if (!testEmail.value || !testPassword.value) {
    alert('Please enter email and password')
    return
  }

  console.log('Attempting test login...')
  const result = await authStore.login(testEmail.value, testPassword.value)

  if (result.success) {
    console.log('Login successful!')
    await loadConversations()
  } else {
    console.error('Login failed:', result.error)
    alert(`Login failed: ${result.error}`)
  }
}

async function testLogout() {
  console.log('Logging out...')
  await authStore.logout()
  console.log('Logged out')
}

async function sendTestMessage() {
  if (!messagesStore.activeConversationId || !testMessageContent.value) {
    alert('Please select a conversation and enter a message')
    return
  }

  try {
    console.log('Sending test message:', testMessageContent.value)
    await messagesStore.sendMessage(messagesStore.activeConversationId, testMessageContent.value)
    console.log('Test message sent successfully')
    testMessageContent.value = 'Hello from the test page!' // Reset to default
  } catch (error) {
    console.error('Failed to send test message:', error)
    alert(`Failed to send message: ${error.message}`)
  }
}
</script>
