<template>
  <div>
    <!-- Hero Section -->
    <v-container
      id="home"
      fluid
      class="hero-section pa-0 d-flex align-center mx-0"
    >
      <v-row
        no-gutters
        justify="center"
        class="align-center"
        style="min-height: inherit;"
      >
        <v-col
          cols="12"
          md="8"
          lg="6"
          class="d-flex flex-column justify-center pa-16 text-center hero-content"
        >
          <h1 class="text-h2 font-weight-bold mb-4">
            <span class="text-h4">From Practice to Podium:</span><br> <span class="font-italic primary-text">Where Winners Perfect Their Interview with AI</span>
          </h1>
          <p
            class="text-body-1 mb-8 mx-auto"
            style="max-width: 600px;"
          >
            Perfect your pageant interview skills with our AI-powered voice coach. Practice anytime, anywhere, and win with confidence.
          </p>
          <div class="d-flex flex-wrap justify-center">
            <v-btn
              class="mr-4 mb-4"
              color="accent"
              size="large"
              rounded="pill"
              :to="authStore.isAuthenticated ? '/dashboard' : '/login?mode=signup'"
            >
              {{ authStore.isAuthenticated ? 'Go to Dashboard' : 'Get Started' }}
            </v-btn>
            <v-btn
              class="mb-4"
              variant="outlined"
              size="large"
              rounded="pill"
              @click="navigateTo('/', '#about');"
            >
              Learn More
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </v-container>

    <!-- Categories Section -->
    <v-container
      id="about"
      fluid
      class="my-16 text-center mx-0 px-4"
    >
      <div class="max-content-width mx-auto">
        <h2 class="text-h4 font-italic mb-12">
          Explore our categories
        </h2>
        <v-row>
          <v-col
            cols="12"
            md="4"
          >
            <v-card
              flat
              class="transparent-card"
            >
              <v-img
                src="https://bvgyhorawikkarjcucxv.supabase.co/storage/v1/object/public/images//miss-interview-demo7.jpg"
                height="240"
                cover
                class="rounded-lg mb-4"
              />
              <h3 class="text-h6 font-weight-bold">
                Personal Questions
              </h3>
              <p class="text-body-2">
                Master responses about your background, values, and aspirations. Customize by adding your profile information.
              </p>
            </v-card>
          </v-col>
          <v-col
            cols="12"
            md="4"
          >
            <v-card
              flat
              class="transparent-card"
            >
              <v-img
                src="https://bvgyhorawikkarjcucxv.supabase.co/storage/v1/object/public/images//miss-interview-demo6.jpg"
                height="240"
                cover
                class="rounded-lg mb-4"
              />
              <h3 class="text-h6 font-weight-bold">
                Current Events
              </h3>
              <p class="text-body-2">
                Prepare for questions on today's hot topics and global issues. Add your pageant location for current events specific to your area.
              </p>
            </v-card>
          </v-col>
          <v-col
            cols="12"
            md="4"
          >
            <v-card
              flat
              class="transparent-card"
            >
              <v-img
                src="https://bvgyhorawikkarjcucxv.supabase.co/storage/v1/object/public/images//miss-interview-demo1.jpg"
                height="240"
                cover
                class="rounded-lg mb-4"
              />
              <h3 class="text-h6 font-weight-bold">
                Platform Practice
              </h3>
              <p class="text-body-2">
                Articulate your social impact platform clearly and passionately. Add your social impact document to interact with the interviewer based on your platform.
              </p>
            </v-card>
          </v-col>
        </v-row>
      </div>
    </v-container>

    <!-- Experience Section -->
    <v-container
      v-if="false"
      id="about"
      fluid
      class="my-16 py-16 bg-grey-lighten-4 mx-0 px-4"
    >
      <div class="max-content-width mx-auto">
        <v-row justify="center">
          <v-col
            cols="12"
            md="8"
            lg="6"
            class="text-center"
          >
            <h2 class="text-h4 font-italic mb-6">
              Extensive experience in the pageant industry
            </h2>
            <p class="text-body-1 mb-8">
              Our team of former judges, coaches, and titleholders have created thousands of authentic interview questions to help you prepare for the crown.
            </p>
            <v-btn
              color="accent"
              variant="flat"
              rounded="pill"
            >
              About Us
            </v-btn>
          </v-col>
        </v-row>
      </div>
    </v-container>

    <!-- Services Section -->
    <v-container
      v-if="false"
      id="services"
      fluid
      class="my-16 mx-0 px-4"
    >
      <div class="max-content-width mx-auto">
        <h2 class="text-h4 font-italic text-center mb-12">
          Browse our services
        </h2>
        <v-row>
          <v-col
            cols="12"
            md="4"
          >
            <v-card
              id="practice"
              flat
              class="transparent-card mb-6"
            >
              <v-img
                src="https://images.unsplash.com/photo-1542744173-8e7e53415bb0?q=80&w=2070&auto=format&fit=crop"
                height="200"
                cover
                class="rounded-lg mb-4"
              />
              <h3 class="text-h6 font-weight-bold">
                Practice Sessions
              </h3>
              <p class="text-caption">
                From $19.99
              </p>
            </v-card>
          </v-col>
          <v-col
            cols="12"
            md="4"
          >
            <v-card
              id="coaching"
              flat
              class="transparent-card mb-6"
            >
              <v-img
                src="https://images.unsplash.com/photo-1543269865-cbf427effbad?q=80&w=2070&auto=format&fit=crop"
                height="200"
                cover
                class="rounded-lg mb-4"
              />
              <h3 class="text-h6 font-weight-bold">
                Private Coaching
              </h3>
              <p class="text-caption">
                From $59.99
              </p>
            </v-card>
          </v-col>
          <v-col
            cols="12"
            md="4"
          >
            <v-card
              flat
              class="transparent-card mb-6"
            >
              <v-img
                src="https://images.unsplash.com/photo-1503428593586-e225b39bddfe?q=80&w=2070&auto=format&fit=crop"
                height="200"
                cover
                class="rounded-lg mb-4"
              />
              <h3 class="text-h6 font-weight-bold">
                Feedback Analysis
              </h3>
              <p class="text-caption">
                From $29.99
              </p>
            </v-card>
          </v-col>
          <v-col
            cols="12"
            md="4"
          >
            <v-card
              id="workshops"
              flat
              class="transparent-card"
            >
              <v-img
                src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=2071&auto=format&fit=crop"
                height="200"
                cover
                class="rounded-lg mb-4"
              />
              <h3 class="text-h6 font-weight-bold">
                Group Workshops
              </h3>
              <p class="text-caption">
                From $39.99
              </p>
            </v-card>
          </v-col>
          <v-col
            cols="12"
            md="4"
          >
            <v-card
              flat
              class="transparent-card"
            >
              <v-img
                src="https://images.unsplash.com/photo-1504384308090-c894fdcc538d?q=80&w=2070&auto=format&fit=crop"
                height="200"
                cover
                class="rounded-lg mb-4"
              />
              <h3 class="text-h6 font-weight-bold">
                Content Creation
              </h3>
              <p class="text-caption">
                From $49.99
              </p>
            </v-card>
          </v-col>
          <v-col
            cols="12"
            md="4"
          >
            <v-card
              flat
              class="transparent-card"
            >
              <v-img
                src="https://images.unsplash.com/photo-1519834785169-98be25ec3f84?q=80&w=1964&auto=format&fit=crop"
                height="200"
                cover
                class="rounded-lg mb-4"
              />
              <h3 class="text-h6 font-weight-bold">
                Mental Preparation
              </h3>
              <p class="text-caption">
                From $24.99
              </p>
            </v-card>
          </v-col>
        </v-row>
        <div class="text-center mt-8">
          <v-btn
            color="primary"
            variant="flat"
            rounded="pill"
            :to="authStore.isAuthenticated ? '/interviews' : '/login'"
          >
            Explore All Services
          </v-btn>
        </div>
      </div>
    </v-container>

    <!-- How It Works Section -->
    <v-container
      v-if="false"
      class="my-16"
    >
      <div class="max-content-width mx-auto">
        <h2 class="text-h4 font-italic text-center mb-12">
          Watch how our AI interviewer works
        </h2>
        <v-row>
          <v-col
            cols="12"
            md="6"
          >
            <v-card
              elevation="0"
              class="rounded-lg overflow-hidden"
            >
              <div class="position-relative">
                <v-img
                  src="https://bvgyhorawikkarjcucxv.supabase.co/storage/v1/object/public/images//miss-interview-demo7.jpg"
                  height="400"
                  cover
                />
                <div class="video-overlay d-flex justify-center align-center">
                  <v-btn
                    icon="mdi-play"
                    color="white"
                    size="x-large"
                    variant="flat"
                    rounded
                  />
                </div>
              </div>
            </v-card>
          </v-col>
          <v-col
            cols="12"
            md="6"
          >
            <v-row>
              <v-col
                cols="12"
                md="6"
              >
                <v-card
                  flat
                  class="pa-4 h-100 bg-grey-lighten-4 rounded-lg"
                >
                  <h3 class="text-h6 mb-2">
                    Voice-powered AI
                  </h3>
                  <p class="text-body-2">
                    Our natural-sounding AI creates an authentic interview experience.
                  </p>
                </v-card>
              </v-col>
              <v-col
                cols="12"
                md="6"
              >
                <v-card
                  flat
                  class="pa-4 h-100 bg-grey-lighten-4 rounded-lg"
                >
                  <h3 class="text-h6 mb-2">
                    Detailed feedback
                  </h3>
                  <p class="text-body-2">
                    Get personalized insights on your responses and performance.
                  </p>
                </v-card>
              </v-col>
              <v-col
                cols="12"
                md="6"
              >
                <v-card
                  flat
                  class="pa-4 h-100 bg-grey-lighten-4 rounded-lg"
                >
                  <h3 class="text-h6 mb-2">
                    100% Confidential
                  </h3>
                  <p class="text-body-2">
                    Practice in private without the fear of judgment.
                  </p>
                </v-card>
              </v-col>
              <v-col
                cols="12"
                md="6"
              >
                <v-card
                  flat
                  class="pa-4 h-100 bg-grey-lighten-4 rounded-lg"
                >
                  <h3 class="text-h6 mb-2">
                    Judge-approved
                  </h3>
                  <p class="text-body-2">
                    Questions created by actual pageant judges and winners.
                  </p>
                </v-card>
              </v-col>
            </v-row>
            <div class="mt-6">
              <v-btn
                color="accent"
                variant="flat"
                rounded="pill"
                :to="authStore.isAuthenticated ? '/interviews' : '#pricing'"
              >
                Try It Now
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </div>
    </v-container>

    <!-- Phone Practice Section -->
    <v-container
      fluid
      class="my-16 py-16 bg-grey-lighten-4"
    >
      <div class="max-content-width mx-auto">
        <v-row align="center">
          <v-col
            cols="12"
            md="6"
            order="2"
            order-md="1"
          >
            <h2 class="text-h4 font-italic mb-6">
              Practice on the go, anytime, anywhere
            </h2>
            <p class="text-body-1 mb-4">
              No app or login required. Simply call our dedicated AI interview practice line for an instant session tailored to your pageant needs.
            </p>
            <div class="d-flex align-center mb-8">
              <v-icon
                size="x-large"
                color="accent"
                class="mr-4"
              >
                mdi-phone
              </v-icon>
              <div>
                <p class="text-h5 font-weight-bold mb-1">
                  (*************
                </p>
                <p class="text-caption">
                  Toll-free in the United States
                </p>
              </div>
            </div>
            <v-chip
              color="primary"
              variant="outlined"
              class="mb-6"
            >
              Available 24/7
            </v-chip>
            <p class="text-body-2">
              Perfect for practicing while traveling to competitions, during spare moments, or whenever inspiration strikes. Our AI coach is always ready to help you prepare for the crown.
            </p>
            <div class="mt-6">
              <v-btn
                color="accent"
                variant="flat"
                rounded="pill"
                href="tel:8882976477"
                class="mr-4"
              >
                <v-icon start>
                  mdi-phone
                </v-icon>
                Call for a Free Demo
              </v-btn>
              <v-btn
                color="primary"
                variant="text"
                class="text-none"
                :to="authStore.isAuthenticated ? '/interviews' : '/login?mode=signup'"
              >
                Or Sign Up Online
              </v-btn>
            </div>
          </v-col>
          <v-col
            cols="12"
            md="6"
            order="1"
            order-md="2"
          >
            <v-img
              src="https://bvgyhorawikkarjcucxv.supabase.co/storage/v1/object/public/images//miss-interview-demo5.jpg"
              height="400"
              cover
              class="rounded-lg"
            />
          </v-col>
        </v-row>
      </div>
    </v-container>

    <!-- Testimonials Section -->
    <v-container
      v-if="false"
      fluid
      class="my-16 py-16 bg-grey-lighten-4"
    >
      <div class="max-content-width mx-auto">
        <h2 class="text-h4 font-italic text-center mb-12">
          Don't take our word for it, take theirs
        </h2>
        <v-row align="center">
          <v-col
            cols="12"
            md="6"
          >
            <p class="text-body-1 mb-6">
              "MissInterview helped me practice my responses until they flowed naturally. The AI gave me feedback on my pacing and confidence that was spot-on. I felt so prepared walking into the interview room, and it showed—I won Miss State 2023!"
            </p>
            <p class="text-subtitle-1 font-weight-bold">
              Jessica Smith
            </p>
            <p class="text-caption">
              Miss State 2023
            </p>
          </v-col>
          <v-col
            cols="12"
            md="6"
          >
            <v-img
              src="https://images.unsplash.com/photo-1534751516642-a1af1ef26a56?q=80&w=1989&auto=format&fit=crop"
              height="400"
              cover
              class="rounded-lg"
            />
          </v-col>
        </v-row>
      </div>
    </v-container>

    <!-- Pricing Section -->
    <v-container
      id="pricing"
      fluid
      class="mx-0 px-4"
    >
      <div class="max-content-width mx-auto">
        <h2 class="text-h4 font-italic text-center mb-12">
          Choose your perfect plan
        </h2>
        <HomePagePricing />
      </div>
    </v-container>

    <!-- Contact Section -->
    <v-container
      id="contact"
      fluid
      class="my-16 mx-0 px-4"
    >
      <div class="max-content-width mx-auto">
        <h2 class="text-h4 font-italic text-center mb-12">
          Contact us
        </h2>
        <v-row justify="center">
          <v-col
            cols="12"
            md="6"
            lg="5"
          >
            <v-card
              flat
              class="pa-4"
            >
              <div
                v-if="formSubmitted"
                class="thank-you-message text-center py-8"
              >
                <v-icon
                  color="success"
                  size="x-large"
                  class="mb-4"
                >
                  mdi-check-circle
                </v-icon>
                <h3 class="text-h5 mb-2">
                  Thank you for contacting us.
                </h3>
                <p class="text-body-1">
                  We'll get back to you as soon as possible.
                </p>
              </div>
              <v-form
                v-else
                v-model="isFormValid"
                @submit.prevent="submitForm"
              >
                <v-text-field
                  v-model="contactForm.name"
                  label="Name"
                  variant="outlined"
                  placeholder="Your Name"
                  class="mb-2"
                  :rules="[v => !!v || 'Name is required']"
                />

                <v-text-field
                  v-model="contactForm.email"
                  label="Email"
                  variant="outlined"
                  placeholder="<EMAIL>"
                  type="email"
                  class="mb-2"
                  :rules="[
                    v => !!v || 'Email is required',
                    v => /.+@.+\..+/.test(v) || 'Email must be valid'
                  ]"
                />

                <v-textarea
                  v-model="contactForm.message"
                  label="Message"
                  variant="outlined"
                  placeholder="Your message"
                  rows="4"
                  class="mb-4"
                  :rules="[v => !!v || 'Message is required']"
                />

                <v-btn
                  color="accent"
                  variant="flat"
                  rounded="pill"
                  block
                  type="submit"
                  :loading="isSubmitting"
                  :disabled="!isFormValid"
                >
                  Send Message
                </v-btn>
              </v-form>
            </v-card>
          </v-col>
          <v-col
            cols="12"
            md="5"
            lg="4"
            class="d-flex flex-column justify-start"
          >
            <v-card
              flat
              class="pa-4"
            >
              <h3 class="text-h6 mb-4">
                Get in touch
              </h3>
              <v-list
                density="compact"
                class="transparent-list"
              >
                <v-list-item prepend-icon="mdi-phone">
                  <v-list-item-title>(*************</v-list-item-title>
                </v-list-item>
                <v-list-item prepend-icon="mdi-email">
                  <v-list-item-title><EMAIL></v-list-item-title>
                </v-list-item>
              </v-list>
            </v-card>
          </v-col>
        </v-row>
      </div>
    </v-container>

    <!-- Instagram Section -->
    <v-container
      v-if="false"
      class="my-16"
    >
      <div class="max-content-width mx-auto">
        <h2 class="text-h4 font-italic text-center mb-12">
          Follow us on Instagram
        </h2>
        <v-row>
          <v-col
            v-for="i in 6"
            :key="i"
            cols="6"
            sm="4"
            md="2"
          >
            <v-img
              :src="`https://images.unsplash.com/photo-1548515943-ad2d3dda67f5?q=80&w=1964&auto=format&fit=crop`"
              aspect-ratio="1"
              cover
              class="rounded-lg"
            />
          </v-col>
        </v-row>
        <div class="text-center mt-8">
          <v-btn
            color="black"
            variant="flat"
            rounded="pill"
          >
            @missinterview
          </v-btn>
        </div>
      </div>
    </v-container>

    <!-- Policies Section (hidden but accessible via hash links) -->
    <section class="mt-16 pt-16">
      <v-container>
        <div
          id="privacy"
          class="d-none"
        >
          Privacy Policy
        </div>
        <div
          id="terms"
          class="d-none"
        >
          Terms of Service
        </div>
        <div
          id="cookies"
          class="d-none"
        >
          Cookie Policy
        </div>
        <div
          id="mental"
          class="d-none"
        >
          Mental Preparation
        </div>
      </v-container>
    </section>
  </div>
</template>

<script setup>
import { useAuthStore } from '@/stores/auth'
import HomePagePricing from '@/components/HomePagePricing.vue'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

// Contact form data
const contactForm = ref({
  name: '',
  email: '',
  message: ''
})

const isFormValid = ref(false)
const isSubmitting = ref(false)
const formSubmitted = ref(false)

const submitForm = async () => {
  isSubmitting.value = true

  try {
    const response = await fetch('https://automation.aiedgemedia.com/webhook/contact-form', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(contactForm.value)
    })

    if (response.ok) {
      formSubmitted.value = true
    } else {
      console.error('Error submitting form:', await response.text())
      // You could add error handling here
    }
  } catch (error) {
    console.error('Error submitting form:', error)
    // You could add error handling here
  } finally {
    isSubmitting.value = false
  }
}

function navigateTo(path, hash) {
  router.push({ path, hash })
}
</script>

<route>
{
  meta: {
    requiresAuth: false
  }
}
</route>

<style scoped>
.hero-section {
  position: relative;
  min-height: 600px;
  background-image: url('https://bvgyhorawikkarjcucxv.supabase.co/storage/v1/object/public/images//miss-interview-demo8-large.jpg');
  background-size: cover;
  background-position: center;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
}

.hero-content {
  position: relative;
  z-index: 2;
  color: white;
}

.hero-content h1 {
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.primary-text {
  color: rgb(var(--v-theme-accent));
  font-weight: bold;
}

.transparent-card {
  background-color: transparent !important;
  box-shadow: none;
}

.transparent-list {
  background-color: transparent !important;
}

.transparent-table {
  background-color: transparent !important;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
}

.gradient-overlay {
  position: relative;
}

.gradient-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, rgba(0,0,0,0.1), rgba(0,0,0,0));
}

.max-content-width {
  max-width: 1200px;
  width: 100%;
}

.thank-you-message {
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
