<template>
  <!-- Interviews Content -->
  <v-container fluid class="pa-0 fill-height">
    <v-row
      no-gutters
      class="fill-height"
      :class="{ 'landscape-row': isLandscape }"
    >
      <!-- Left Column - Interviews List -->
      <v-col
        cols="12"
        xs="12"
        sm="12"
        md="4"
        lg="3"
        :class="[
          'border-r',
          'interviews-sidebar',
          'sidebar-bg',
          'sidebar-column',
          { 'slide-left': isMobilePortrait && currentInterviewId },
          { 'landscape-sidebar': isLandscape }
        ]"
      >
        <v-card
          flat
          class="h-100 d-flex flex-column sidebar-bg"
        >
          <v-card-title class="text-h5 d-flex align-center px-4 py-4 flex-shrink-0">
            My Interviews
            <v-spacer />
            <v-btn
              color="accent"
              prepend-icon="mdi-plus"
              rounded
              size="small"
              @click="openVoiceAssistant"
            >
              New
            </v-btn>
          </v-card-title>

          <v-card-text class="px-2 flex-grow-1 overflow-y-auto">
            <!-- Loading state -->
            <div
              v-if="loading"
              class="d-flex justify-center align-center my-4"
            >
              <v-progress-circular
                indeterminate
                color="primary"
              />
            </div>

            <!-- Error state -->
            <v-alert
              v-if="error"
              type="error"
              class="mx-2 mb-4"
            >
              {{ error }}
            </v-alert>

            <!-- No interviews message -->
            <v-alert
              v-if="!loading && !interviews.length"
              type="info"
              class="mx-2 mb-4"
            >
              You don't have any interviews yet.
            </v-alert>

            <!-- Interviews List -->
            <v-list
              v-if="!loading && interviews.length"
              lines="two"
              class="interviews-list pa-2"
              bg-color="transparent"
              density="comfortable"
            >
              <v-list-item
                v-for="interview in interviews"
                :key="interview.id"
                :value="interview.id"
                :to="{
                  name: 'interview-details',
                  params: { id: interview.id }
                }"
                class="mb-2"
                rounded="lg"
                :bg-color="currentInterviewId === interview.id ? 'primary' : 'surface'"
                :class="{
                  'text-primary': currentInterviewId === interview.id,
                  'border': currentInterviewId !== interview.id
                }"
                :ripple="false"
              >
                <template #prepend>
                  <v-icon
                    :color="currentInterviewId === interview.id ? 'primary' : 'grey'"
                    icon="mdi-text-box-outline"
                    class="mr-2"
                  />
                </template>

                <v-list-item-title>
                  {{ interview.title || 'Untitled Interview' }}
                </v-list-item-title>
                <v-list-item-subtitle>
                  {{ formatDate(interview.created_at) }}
                </v-list-item-subtitle>

                <template #append>
                  <v-btn
                    icon="mdi-delete"
                    size="small"
                    variant="text"
                    elevation="0"
                    class="delete-btn"
                    @click.stop.prevent="confirmDelete(interview)"
                  />
                </template>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Right Column - Interview Details -->
      <v-col
        xs="12"
        sm="12"
        md="8"
        lg="9"
        class="h-100 d-flex flex-grow-1 details-column"
        :class="{ 'landscape-details': isLandscape }"
      >
        <router-view v-slot="{ Component }">
          <component
            :is="Component"
            :key="currentInterviewId"
            class="flex-grow-1"
          />
        </router-view>
      </v-col>
    </v-row>
  </v-container>

  <!-- Voice Assistant Dialog -->
  <VoiceAssistantDialog v-model="voiceAssistantDialog" />

  <!-- Delete Confirmation Dialog -->
  <v-dialog
    v-model="deleteDialog"
    max-width="400"
    persistent
  >
    <v-card>
      <v-card-title class="text-h6 pa-4">
        Delete Interview?
      </v-card-title>
      <v-card-text class="pa-4">
        Are you sure you want to delete "<span class="font-weight-medium">{{ interviewToDelete?.title || 'Untitled Interview' }}</span>"? This action cannot be undone.
      </v-card-text>
      <v-card-actions class="pa-4">
        <v-spacer />
        <v-btn
          color="grey"
          variant="text"
          @click="cancelDelete"
        >
          Cancel
        </v-btn>
        <v-btn
          color="error"
          variant="flat"
          :loading="deleteLoading"
          @click="deleteInterview"
        >
          Delete
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRoute, useRouter } from 'vue-router'
import { supabase } from '@/plugins/supabase'
import VoiceAssistantDialog from '@/components/VoiceAssistantDialog.vue'
import { useDisplay } from 'vuetify'

const authStore = useAuthStore()
const route = useRoute()
const router = useRouter()
const { mobile, width, height } = useDisplay()

const interviews = ref([])
const loading = ref(false)
const error = ref(null)

// Form related refs
const voiceAssistantDialog = ref(false)

// Delete related refs
const deleteDialog = ref(false)
const deleteLoading = ref(false)
const interviewToDelete = ref(null)

// Responsive layout computed properties
const isLandscape = computed(() => {
  // Use display width/height
  const isLandscapeRatio = width.value > height.value
  // Also check media query for additional accuracy
  const mediaQueryIsLandscape = window.matchMedia('(orientation: landscape)').matches
  return isLandscapeRatio && mediaQueryIsLandscape
})

const isMobilePortrait = computed(() => {
  return mobile.value && !isLandscape.value
})

const currentInterviewId = computed(() => {
  const id = route.params.id
  return id ? String(id) : null
})

// Watch for route param changes
watch(currentInterviewId, () => {
  // Layout is handled via CSS classes now
})

// Load interviews on component mount
onMounted(async () => {
  await fetchInterviews()

  // Listen for interview updates
  document.addEventListener('interview-updated', (event) => {
    // Find and update the interview in the list
    const updatedInterview = interviews.value.find(i => i.id === event.detail.id)
    if (updatedInterview) {
      updatedInterview.title = event.detail.title
    }
  })

  // Listen for window resize events with a passive event listener for better performance
  window.addEventListener('resize', handleResize, { passive: true })
})

// Clean up event listeners on unmount
onUnmounted(() => {
  document.removeEventListener('interview-updated', null)
  window.removeEventListener('resize', handleResize)
})

// Debounce helper to avoid excessive resize handling
let resizeTimeout = null

// Simplified resize handler
function handleResize() {
  if (resizeTimeout) {
    clearTimeout(resizeTimeout)
  }

  resizeTimeout = setTimeout(() => {
    // Force a repaint of the container - helps fix certain browser rendering issues
    const container = document.querySelector('.v-container')
    if (container) {
      container.style.display = 'none'
      // Force reflow
      container.offsetHeight
      container.style.display = ''
    }
    resizeTimeout = null
  }, 100)
}

// Fetch interviews from Supabase
async function fetchInterviews() {
  loading.value = true
  error.value = null

  try {
    // Only fetch interviews for the current user
    const { data, error: fetchError } = await supabase
      .from('interviews')
      .select('*')
      .eq('user_id', authStore.user.id)
      .order('created_at', { ascending: false })

    if (fetchError) throw fetchError

    interviews.value = data || []
  } catch (err) {
    console.error('Error fetching interviews:', err)
    error.value = 'Failed to load interviews. Please try again.'
  } finally {
    loading.value = false
  }
}

// Format date for display
function formatDate(dateString) {
  if (!dateString) return ''

  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
  }).format(date)
}

// Open the voice assistant dialog
function openVoiceAssistant() {
  voiceAssistantDialog.value = true
}

// Cancel delete and reset state
function cancelDelete() {
  deleteDialog.value = false
  interviewToDelete.value = null
  error.value = null
}

// Confirm interview deletion
function confirmDelete(interview) {
  error.value = null
  interviewToDelete.value = interview
  deleteDialog.value = true
}

// Delete the interview
async function deleteInterview() {
  if (!interviewToDelete.value) return

  console.log('Current route ID:', currentInterviewId.value, typeof currentInterviewId.value)
  console.log('Interview to delete ID:', interviewToDelete.value.id, typeof interviewToDelete.value.id)

  const isCurrentInterview = String(interviewToDelete.value.id) === String(currentInterviewId.value)
  console.log('isCurrentInterview', isCurrentInterview)

  deleteLoading.value = true
  error.value = null

  try {
    const { error: deleteError } = await supabase
      .from('interviews')
      .delete()
      .eq('id', interviewToDelete.value.id)
      .eq('user_id', authStore.user.id)

    if (deleteError) throw deleteError

    // Close dialog first
    deleteDialog.value = false

    // If we deleted the current interview, navigate to interviews route first
    if (isCurrentInterview) {
      // Force navigation to /interviews
      await router.push({ path: '/interviews', replace: true })
      // Refresh interviews list after navigation
      await fetchInterviews()
    } else {
      // For non-current interviews, just refresh the list
      await fetchInterviews()
    }

    // Reset state
    interviewToDelete.value = null
    error.value = null
  } catch (err) {
    console.error('Error deleting interview:', err)
    error.value = 'Failed to delete interview. Please try again.'
  } finally {
    deleteLoading.value = false
  }
}
</script>

<style scoped>
.border-b {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.interviews-sidebar {
  height: 100%;
  overflow: hidden;
  background-color: var(--v-theme-surface-variant) !important;
  color: var(--v-theme-on-surface) !important;
}

.interviews-sidebar .v-card {
  background-color: var(--v-theme-surface-variant) !important;
  color: var(--v-theme-on-surface) !important;
}

.interviews-sidebar .v-card-title,
.interviews-sidebar .v-card-text,
.interviews-sidebar .v-list-item-title,
.interviews-sidebar .v-list-item-subtitle {
  color: var(--v-theme-on-surface) !important;
}

.interviews-list {
  height: 100%;
}

.border-r {
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

:deep(.v-list-item) {
  opacity: 1 !important;
}

:deep(.v-list-item--active) {
  opacity: 1 !important;
}

:deep(.v-list-item.border) {
  border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

:deep(.v-list-item:hover:not(.v-list-item--active)) {
  background-color: rgb(var(--v-theme-surface-variant), 0.08);
}

:deep(.delete-btn) {
  opacity: 0;
  transition: opacity 0.2s ease;
}

:deep(.v-list-item:hover .delete-btn) {
  opacity: 1;
}

.sidebar-bg {
  background-color: var(--v-theme-sidebar) !important;
  color: var(--v-theme-on-surface) !important;
}

.v-theme--light .sidebar-bg {
  background-color: #000000 !important;
  color: #23272b !important;
}

/* Mobile layout styles using CSS-driven transitions */
.sidebar-column {
  width: 100%;
  max-width: 100%;
  transition: transform 0.3s ease, position 0.3s ease;
  position: relative;
  z-index: 2;
}

.details-column {
  width: 100%;
  position: relative;
  z-index: 1;
  transition: transform 0.3s ease;
}

/* Mobile portrait with interview selected */
@media (max-width: 600px) and (orientation: portrait) {
  .slide-left {
    transform: translateX(-100%);
    position: absolute;
  }

  .details-column {
    position: absolute;
    width: 100%;
  }
}

/* Landscape layout styles */
@media (orientation: landscape) {
  /* In landscape, force desktop-like layout */
  .sidebar-column {
    position: relative !important;
    transform: none !important;
    max-width: 33% !important; /* Set a max-width to ensure it doesn't take up too much space */
  }

  /* Make sure the details column is visible in landscape mode */
  .details-column {
    position: relative !important;
    display: flex !important;
    width: 67% !important; /* Ensure the details take up remaining space */
  }

  .back-button {
    display: none !important;
  }
}

@media (max-width: 600px) and (orientation: landscape) {
  /* For smaller landscape screens */
  .sidebar-column {
    max-width: 40% !important;
  }

  .details-column {
    width: 60% !important;
  }
}

.landscape-sidebar {
  max-width: 40%;
  flex-basis: 40%;
  flex-grow: 0;
}

.landscape-details {
  flex-basis: 60%;
  flex-grow: 1;
}

@media (min-width: 768px) {
  .landscape-sidebar {
    max-width: 33%;
    flex-basis: 33%;
  }

  .landscape-details {
    flex-basis: 67%;
  }
}

.landscape-row {
  display: flex !important;
  flex-direction: row !important;
  width: 100% !important;
}
</style>

<route>
{
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}
</route>
