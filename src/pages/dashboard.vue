<template>
  <!-- Dashboard Content -->
  <div class="flex-grow-1 overflow-y-auto pa-6">
    <!-- Onboarding component replaces the Welcome Dialog -->
    <Onboarding
      v-model="showOnboarding"
      @completed="onboardingCompleted"
    />

    <div class="d-flex align-center mb-6">
      <h1 class="text-h4">Dashboard</h1>
      <v-spacer />
      <div class="d-flex align-center">
        <v-btn
          v-if="!profileFullyComplete"
          prepend-icon="mdi-account-edit"
          class="mr-3"
          flat
          variant="tonal"
          @click="openOnboarding"
        >
          <span class="d-none d-sm-inline">Complete </span>Onboarding
        </v-btn>
        <v-btn
          prepend-icon="mdi-plus"
          class="rounded-e-0"
          @click="openVoiceAssistant"
        >
          <span class="d-none d-sm-inline">New </span>Interview
        </v-btn>
        <v-menu>
          <template #activator="{ props }">
            <v-btn
              class="rounded-s-0"
              variant="elevated"
              v-bind="props"
            >
              <v-icon>mdi-menu-down</v-icon>
            </v-btn>
          </template>
          <v-list>
            <v-list-item @click="openVoiceAssistant">
              <v-list-item-title>New Interview</v-list-item-title>
            </v-list-item>
            <v-list-item @click="goToInterviews">
              <v-list-item-title>My Interviews</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
    </div>

    <!-- Usage Minutes Chart -->
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex align-center py-3">
            <v-icon
              icon="mdi-chart-line"
              color="primary"
              size="28"
              class="mr-3"
            />
            <span class="text-h6">Usage Minutes & Overall Score</span>
          </v-card-title>
          <v-divider />
          <v-card-text>
            <div
              v-if="loadingMinutes"
              class="d-flex justify-center"
            >
              <v-progress-circular
                indeterminate
                color="primary"
              />
            </div>
            <div
              v-else-if="minutesError"
              class="text-center text-body-1 text-red"
            >
              {{ minutesError }}
            </div>
            <div
              v-else-if="!hasValidInterviewData"
              class="text-center py-4"
            >
              <p class="text-subtitle-1">
                No usage data available yet.
              </p>
              <p class="text-body-2 text-medium-emphasis">
                As you use the platform, your usage will be tracked here.
              </p>

              <!-- Debug Info Section -->
              <div
                v-if="false"
                class="mt-6 text-left"
              >
                <v-alert
                  type="info"
                  title="Debug Info"
                  text="Interview and score data details"
                  variant="tonal"
                >
                  <v-expansion-panels>
                    <v-expansion-panel>
                      <v-expansion-panel-title>Raw Interview Data</v-expansion-panel-title>
                      <v-expansion-panel-text>
                        <v-table density="compact">
                          <thead>
                            <tr>
                              <th>ID</th>
                              <th>Date</th>
                              <th>Duration (min)</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr
                              v-for="item in interviewData"
                              :key="item.id"
                            >
                              <td>{{ item.id }}</td>
                              <td>{{ new Date(item.created_at).toLocaleString() }}</td>
                              <td>{{ item.duration_minutes || '(none)' }}</td>
                            </tr>
                          </tbody>
                        </v-table>
                      </v-expansion-panel-text>
                    </v-expansion-panel>

                    <v-expansion-panel>
                      <v-expansion-panel-title>Extracted Score Data</v-expansion-panel-title>
                      <v-expansion-panel-text>
                        <p
                          v-if="!interviewData.some(i => i.scores)"
                          class="text-body-2"
                        >
                          No scores data found.
                        </p>
                        <v-table
                          v-else
                          density="compact"
                        >
                          <thead>
                            <tr>
                              <th>Interview ID</th>
                              <th>Date</th>
                              <th>Overall</th>
                              <th
                                v-for="category in categoryList"
                                :key="category"
                              >
                                {{ category }}
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr
                              v-for="interview in interviewData.filter(i => i.scores)"
                              :key="interview.id"
                            >
                              <td>{{ interview.id }}</td>
                              <td>{{ new Date(interview.created_at).toLocaleDateString() }}</td>
                              <td>{{ interview.scores.overall }}</td>
                              <td
                                v-for="category in categoryList"
                                :key="category"
                              >
                                {{ interview.scores[category] || '-' }}
                              </td>
                            </tr>
                          </tbody>
                        </v-table>
                      </v-expansion-panel-text>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </v-alert>
              </div>
            </div>
            <div
              v-else
              id="minutes-chart-container"
              style="height: 300px; position: relative;"
            >
              <canvas
                ref="usageChart"
                width="800"
                height="300"
              />
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Performance Metrics & Recent Transactions -->
    <v-row class="mt-4">
      <!-- Performance Metrics -->
      <v-col
        cols="12"
        md="8"
      >
        <v-card>
          <v-card-title class="d-flex align-center py-3">
            <v-icon
              icon="mdi-chart-areaspline"
              color="primary"
              size="28"
              class="mr-3"
            />
            <span class="text-h6">Interview Performance by Category</span>
          </v-card-title>
          <v-divider />
          <v-card-text>
            <div
              v-if="!hasValidInterviewData"
              class="text-center py-6"
            >
              <p class="text-subtitle-1">
                No performance data available yet
              </p>
              <p class="text-body-2 text-medium-emphasis">
                Complete interviews to see your performance metrics
              </p>
            </div>
            <v-row v-else>
              <v-col
                v-for="(category, index) in categoryList.slice(0, 2)"
                :key="category"
                cols="12"
                sm="6"
              >
                <h3 class="text-subtitle-1 mb-2">
                  {{ category }}
                </h3>
                <div style="height: 150px">
                  <canvas
                    v-if="index === 0"
                    :id="`chart-${category.replace(/\s+/g, '-').toLowerCase()}`"
                    ref="personalChart"
                  />
                  <canvas
                    v-else
                    :id="`chart-${category.replace(/\s+/g, '-').toLowerCase()}`"
                    ref="platformChart"
                  />
                </div>
              </v-col>
            </v-row>
            <v-row
              v-if="categoryList.length > 2"
              class="mt-4"
            >
              <v-col
                v-for="(category, index) in categoryList.slice(2, 4)"
                :key="category"
                cols="12"
                sm="6"
              >
                <h3 class="text-subtitle-1 mb-2">
                  {{ category }}
                </h3>
                <div style="height: 150px">
                  <canvas
                    v-if="index === 0"
                    :id="`chart-${category.replace(/\s+/g, '-').toLowerCase()}`"
                    ref="eventsChart"
                  />
                  <canvas
                    v-else
                    :id="`chart-${category.replace(/\s+/g, '-').toLowerCase()}`"
                    ref="overallChart"
                  />
                </div>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Recent Transactions -->
      <v-col
        cols="12"
        md="4"
      >
        <v-card>
          <v-card-title class="d-flex align-center py-3">
            <v-icon
              icon="mdi-cash-multiple"
              color="primary"
              size="28"
              class="mr-3"
            />
            <span class="text-h6">Transactions</span>
            <v-spacer />
            <v-btn
              color="primary"
              size="small"
              prepend-icon="mdi-cart-plus"
              @click="showPurchaseDialog = true"
            >
              Buy Minutes
            </v-btn>
          </v-card-title>
          <v-divider />
          <v-card-text>
            <div
              v-if="loadingTransactions"
              class="d-flex justify-center"
            >
              <v-progress-circular
                indeterminate
                color="primary"
              />
            </div>
            <div
              v-else-if="transactionsError"
              class="text-center text-body-1 text-red"
            >
              {{ transactionsError }}
            </div>
            <div
              v-else-if="transactions.length === 0"
              class="text-center py-4"
            >
              <p class="text-subtitle-1">
                No transactions yet
              </p>
              <p class="text-body-2 text-medium-emphasis">
                Your recent purchases will appear here.
              </p>
            </div>
            <v-list
              v-else
              lines="two"
            >
              <v-list-item
                v-for="transaction in transactions"
                :key="transaction.id"
                :title="transaction.product_name"
                :subtitle="`${transaction.minutes} minutes • $${parseFloat(transaction.total).toFixed(2)}`"
              >
                <template #prepend>
                  <v-avatar
                    color="primary"
                    class="text-white"
                    size="36"
                  >
                    <v-icon icon="mdi-clock-outline" />
                  </v-avatar>
                </template>
                <template #append>
                  <div class="text-caption">
                    {{ new Date(transaction.created_at).toLocaleDateString() }}
                  </div>
                </template>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Recent Interviews -->
    <v-row class="mt-4">
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex align-center py-3">
            <v-icon
              icon="mdi-account-voice"
              color="primary"
              size="28"
              class="mr-3"
            />
            <span class="text-h6">Interviews</span>
            <v-spacer />
            <v-btn
              color="primary"
              variant="tonal"
              size="small"
              to="/interviews"
            >
              All Interviews
            </v-btn>
          </v-card-title>
          <v-divider />
          <v-card-text>
            <div
              v-if="loadingInterviews"
              class="d-flex justify-center"
            >
              <v-progress-circular
                indeterminate
                color="primary"
              />
            </div>
            <div
              v-else-if="interviewsError"
              class="text-center text-body-1 text-red"
            >
              {{ interviewsError }}
            </div>
            <div
              v-else-if="interviews.length === 0"
              class="text-center py-4"
            >
              <p class="text-subtitle-1">
                No interviews yet
              </p>
              <p class="text-body-2 text-medium-emphasis">
                Start a new interview to see it listed here.
              </p>
              <v-btn
                color="primary"
                class="mt-2"
                to="/interviews"
              >
                Start Interview
              </v-btn>
            </div>
            <div v-else>
              <v-data-table
                id="interviews-data-table"
                :headers="interviewsHeaders"
                :items="interviews"
                :items-per-page="interviewsItemsPerPage"
                :page="interviewsPage"
                :sort-by="interviewsSortBy"
                density="comfortable"
                hover
                class="elevation-0"
                item-value="id"
                @update:page="interviewsPage = $event"
                @update:sort-by="interviewsSortBy = $event"
              >
                <template #item="{ item }">
                  <tr v-if="item">
                    <td>{{ item.title || 'Untitled Interview' }}</td>
                    <td>{{ item.created_at ? new Date(item.created_at).toLocaleDateString() : '-' }}</td>
                    <td class="text-end">
                      {{ item.duration_minutes || 0 }}
                    </td>
                    <td class="text-end">
                      <v-chip
                        v-if="item.overall != null"
                        :color="getScoreColor(item.overall)"
                        size="small"
                        variant="flat"
                        class="font-weight-medium score-chip"
                      >
                        {{ Number(item.overall).toFixed(1) }}
                      </v-chip>
                      <span v-else>-</span>
                    </td>
                    <td class="text-end">
                      <v-chip
                        v-if="item.fillerWords != null"
                        :color="getFillerWordColor(item.fillerWords)"
                        size="small"
                        variant="flat"
                        class="font-weight-medium score-chip"
                      >
                        {{ Number(item.fillerWords).toFixed(1) + '%' }}
                      </v-chip>
                      <span v-else>-</span>
                    </td>

                    <!-- Dynamic category columns -->
                    <td
                      v-for="category in categoryList"
                      :key="category"
                      class="text-end"
                    >
                      <v-chip
                        v-if="item[category] != null"
                        :color="getScoreColor(item[category])"
                        size="small"
                        variant="flat"
                        class="font-weight-medium score-chip"
                      >
                        {{ Number(item[category]).toFixed(1) }}
                      </v-chip>
                      <span v-else>-</span>
                    </td>

                    <td class="text-end">
                      <v-btn
                        size="small"
                        variant="text"
                        color="primary"
                        :to="{ name: 'interview-details', params: { id: item.id } }"
                      >
                        View
                      </v-btn>
                    </td>
                  </tr>
                </template>
              </v-data-table>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Voice Assistant Dialog -->
    <VoiceAssistantDialog v-model="voiceAssistantDialog" />

    <!-- Purchase Minutes Dialog -->
    <PurchaseMinutesDialog
      v-model="showPurchaseDialog"
      :loading="purchaseLoading"
      @purchase="handlePurchase"
      @coupon-redeemed="fetchInterviewData"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick, onBeforeUnmount, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { supabase } from '@/plugins/supabase'
import Chart from 'chart.js/auto'
import VoiceAssistantDialog from '@/components/VoiceAssistantDialog.vue'
import PurchaseMinutesDialog from '@/components/PurchaseMinutesDialog.vue'
import Onboarding from '@/components/Onboarding.vue'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

// Data
const transactions = ref([])
const interviews = ref([])
const interviewData = ref([]) // Processed interview data with scores
const categoryList = ref([]) // Categories found in the analysis
const profileData = ref(null)

// Canvas refs
const usageChart = ref(null)
const personalChart = ref(null)
const platformChart = ref(null)
const eventsChart = ref(null)
const overallChart = ref(null)

// UI state
const loadingMinutes = ref(true)
const loadingTransactions = ref(true)
const loadingInterviews = ref(true)
const minutesError = ref(null)
const transactionsError = ref(null)
const interviewsError = ref(null)

// Onboarding dialog state (replacing Welcome dialog)
const showOnboarding = ref(false)

// Voice Assistant dialog
const voiceAssistantDialog = ref(false)

// Purchase Minutes dialog
const showPurchaseDialog = ref(false)
const purchaseLoading = ref(false)

// Charts instances
let minutesChartInstance = null
let performanceChartInstances = {}
let chartsInitialized = false

// Pagination state for interviews table
const interviewsPage = ref(1)
const interviewsItemsPerPage = ref(10)
const interviewsHeaders = ref([
  { title: 'Title', key: 'title', sortable: true },
  { title: 'Date', key: 'created_at', sortable: true },
  { title: 'Duration (min)', key: 'duration_minutes', sortable: true, align: 'end' },
  { title: 'Overall Score', key: 'overall', sortable: true, align: 'end' },
  { title: 'Filler Words %', key: 'fillerWords', sortable: true, align: 'end' },
  { title: 'Actions', key: 'actions', sortable: false, align: 'end' }
])
const interviewsSortBy = ref([{ key: 'created_at', order: 'desc' }])

// Create delay function
const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

// Computed values
const hasValidInterviewData = computed(() => {
  // Check if we have any interviews with duration and scores
  return interviewData.value.some(
    interview => interview.duration_minutes && interview.duration_minutes > 0 && interview.scores
  )
})

// Check if all profile fields are complete, including optional fields
const profileFullyComplete = computed(() => {
  // Return false if we don't have user profile data
  if (!profileData.value) return false

  // Required fields: first name and last name
  const requiredFieldsComplete = !!(
    profileData.value.first_name &&
    profileData.value.last_name
  )

  if (!requiredFieldsComplete) return false

  // Check optional fields - at least THREE should be filled in
  let optionalFieldsCount = 0

  if (profileData.value.mobile_phone) optionalFieldsCount++
  if (profileData.value.city) optionalFieldsCount++
  if (profileData.value.state) optionalFieldsCount++
  if (profileData.value.pageant_title) optionalFieldsCount++
  if (profileData.value.current_events && profileData.value.current_events.length > 0) optionalFieldsCount++
  if (profileData.value.resume) optionalFieldsCount++
  if (profileData.value.social_impact_initiative) optionalFieldsCount++

  // Consider profile fully complete if at least 3 optional fields are filled in
  return optionalFieldsCount >= 6
})

// Check if user profile needs completion
const checkProfileCompletion = async () => {
  try {
    // Get user profile data
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', authStore.user.id)
      .single()

    if (error) throw error

    // Store the profile data for the profileFullyComplete computed property
    profileData.value = data || {}

    // Show onboarding if onboarding_complete is not true
    const needsProfileCompletion = !data || data.onboarding_complete !== true

    if (needsProfileCompletion) {
      // Show the onboarding component
      showOnboarding.value = true
    }
  } catch (error) {
    console.error('Error checking profile completion:', error)
  }
}

// Handle onboarding completion
const onboardingCompleted = async () => {
  console.log('Onboarding completed')

  try {
    // Update user profile to mark onboarding as complete
    const { error } = await supabase
      .from('user_profiles')
      .update({ onboarding_complete: true })
      .eq('user_id', authStore.user.id)

    if (error) throw error

    // Refresh profile data
    await checkProfileCompletion()

  } catch (error) {
    console.error('Error updating onboarding status:', error)
  }
}

// Process interview data for chart
const processInterviewData = () => {
  // Group interviews by date and calculate total minutes per day
  const interviewsByDate = {}

  console.log('Processing interview data for chart with data:', interviewData.value)

  interviewData.value.forEach(item => {
    // Make sure we have a valid duration (default to 0 if null/undefined)
    const duration = Number(item.duration_minutes) || 0
    const date = new Date(item.created_at).toLocaleDateString()

    if (!interviewsByDate[date]) {
      interviewsByDate[date] = {
        minutes: 0,
        overallScores: []
      }
    }
    interviewsByDate[date].minutes += duration

    // Find overall score for this interview if it exists
    const overallScore = item.scores?.overall
    if (overallScore) {
      interviewsByDate[date].overallScores.push(Number(overallScore))
      console.log(`Found overall score ${overallScore} for interview on ${date}`)
    }
  })

  console.log('Interviews grouped by date:', interviewsByDate)

  // Convert to arrays for charting
  const dates = Object.keys(interviewsByDate)
  const minutesPerDay = dates.map(date => interviewsByDate[date].minutes)

  // Get overall grades from scores data if available
  const gradeData = dates.map(date => {
    const scoresList = interviewsByDate[date].overallScores
    if (scoresList.length === 0) return 7 // Default value if no scores

    const sum = scoresList.reduce((total, score) => total + Number(score), 0)
    return sum / scoresList.length
  })

  console.log('Processed data for chart:', { dates, minutesPerDay, gradeData })

  return {
    dates,
    minutesPerDay,
    gradeData
  }
}

// Load interview data with scores for all charts
const fetchInterviewData = async () => {
  try {
    loadingMinutes.value = true
    minutesError.value = null

    console.log('Fetching interview data for user:', authStore.user.id)

    // Get all interviews for the user but exclude large text fields
    const { data: interviewsData, error: interviewsError } = await supabase
      .from('interviews')
      .select('id, created_at, duration_minutes, title, analysis')
      .eq('user_id', authStore.user.id)
      .order('created_at', { ascending: true })

    if (interviewsError) throw interviewsError

    console.log('Raw interviews data received:', interviewsData)

    // If we have interviews, extract the scores from the analysis field
    if (interviewsData && interviewsData.length > 0) {
      // Process interviews to extract scores
      const processedInterviews = interviewsData.map(interview => {
        let scores = null
        let fillerWordsPercentage = null
        let overall = null

        // Try to extract scores from analysis JSON
        if (interview.analysis) {
          try {
            const analysis = typeof interview.analysis === 'string'
              ? JSON.parse(interview.analysis)
              : interview.analysis

            // Get filler words percentage if available
            if (analysis.filler_words) {
              // Use the filler_word_percentage field if available
              if (analysis.filler_words.filler_word_percentage !== undefined) {
                fillerWordsPercentage = analysis.filler_words.filler_word_percentage;
              }
              // Otherwise calculate it from total_words and total_filler_words
              else if (analysis.filler_words.total_words && analysis.filler_words.total_filler_words) {
                const totalWords = analysis.filler_words.total_words;
                const totalFillerWords = analysis.filler_words.total_filler_words;

                if (totalWords > 0) {
                  fillerWordsPercentage = (totalFillerWords / totalWords) * 100;
                }
              }

              if (fillerWordsPercentage !== null) {
                console.log(`Found filler words percentage: ${fillerWordsPercentage.toFixed(2)}% for interview ${interview.id}`);
              }
            }

            if (analysis.grading) {
              // Get overall score
              overall = analysis.grading.overall?.score || null

              // Get category scores
              const categoryScores = {}
              if (analysis.grading.categories) {
                Object.entries(analysis.grading.categories).forEach(([category, data]) => {
                  // Skip Composure and Delivery category and Personal Introduction
                  if (category !== 'Composure and Delivery' && category !== 'Personal Introduction') {
                    categoryScores[category] = data.score

                    // Add to category list for dynamic chart creation
                    if (!categoryList.value.includes(category)) {
                      categoryList.value.push(category)
                    }
                  }
                })
              }

              scores = {
                overall,
                ...categoryScores
              }
            }
          } catch (e) {
            console.error(`Failed to parse analysis for interview ${interview.id}:`, e)
          }
        }

        // Process the interview to include flattened score values for data table
        const processedItem = {
          ...interview,
          scores,
          fillerWords: fillerWordsPercentage,
          overall
        }

        // Add each category as a direct property for the data table
        if (scores) {
          categoryList.value.forEach(category => {
            if (scores[category] !== undefined) {
              processedItem[category] = scores[category]
            }
          })
        }

        return processedItem
      })

      console.log('Processed interviews with scores:', processedInterviews)
      console.log('Categories found:', categoryList.value)

      // Update the data ref
      interviewData.value = processedInterviews

      // Only include interviews that have scores for further processing
      interviews.value = processedInterviews
    } else {
      interviewData.value = []
      interviews.value = []
      console.log('No interviews found for user')
    }

    return true
  } catch (error) {
    console.error('Error fetching interview data:', error)
    minutesError.value = 'Failed to load usage data: ' + error.message
    return false
  } finally {
    loadingMinutes.value = false
  }
}

// Load transactions data
const fetchTransactions = async () => {
  loadingTransactions.value = true
  transactionsError.value = null

  try {
    const { data, error } = await supabase
      .from('transactions')
      .select('*')
      .eq('user_id', authStore.user.id)
      .order('created_at', { ascending: false })
      .limit(5)

    if (error) throw error

    transactions.value = data || []
  } catch (error) {
    console.error('Error fetching transactions:', error)
    transactionsError.value = 'Failed to load transaction data'
  } finally {
    loadingTransactions.value = false
  }
}

// Load interviews data
const fetchInterviews = async () => {
  loadingInterviews.value = true
  interviewsError.value = null

  try {
    // We'll use the processed interview data from fetchInterviewData instead of making a separate query
    // This ensures we have all the scores and analysis data available
    if (interviewData.value.length > 0) {
      interviews.value = interviewData.value
    } else {
      // Fallback in case interviewData wasn't loaded yet
      const { data, error } = await supabase
        .from('interviews')
        .select('*')
        .eq('user_id', authStore.user.id)
        .order('created_at', { ascending: false })

      if (error) throw error

      interviews.value = data || []
    }

    // Filter out Personal Introduction from category list if it exists
    categoryList.value = categoryList.value.filter(category =>
      category !== 'Personal Introduction'
    );

    // Add headers for each category dynamically
    if (categoryList.value.length > 0) {
      // Start with the basic headers
      const updatedHeaders = [
        { title: 'Title', key: 'title', sortable: true },
        { title: 'Date', key: 'created_at', sortable: true },
        { title: 'Duration (min)', key: 'duration_minutes', sortable: true, align: 'end' },
        { title: 'Overall Score', key: 'overall', sortable: true, align: 'end' },
        { title: 'Filler Words %', key: 'fillerWords', sortable: true, align: 'end' }
      ]

      // Add a header for each category
      categoryList.value.forEach(category => {
        if (!updatedHeaders.some(header => header.key === category)) {
          updatedHeaders.push({
            title: category,
            key: category,
            sortable: true,
            align: 'end'
          })
        }
      })

      // Add the actions column last
      updatedHeaders.push({
        title: 'Actions',
        key: 'actions',
        sortable: false,
        align: 'end'
      })

      interviewsHeaders.value = updatedHeaders
    }
  } catch (error) {
    console.error('Error fetching interviews:', error)
    interviewsError.value = 'Failed to load interview data'
  } finally {
    loadingInterviews.value = false
  }
}

onMounted(async () => {
  if (authStore.isAuthenticated) {
    // Check profile completion
    await checkProfileCompletion()

    // Fetch data
    await fetchInterviewData() // Make sure interview data is fetched first
    await fetchTransactions()
    await fetchInterviews() // Fetch interviews after interview data is processed

    // Initialize charts after data is loaded
    await initCharts()
  }
})

// Watch for changes in the interview data and update charts
watch(interviewData, async () => {
  console.log('Interview data changed, updating charts...')
  if (interviewData.value.length > 0) {
    // Reset initialized flag to force re-initialization
    chartsInitialized = false
    // Make sure to destroy charts first
    destroyAllCharts()
    // Re-initialize charts after a short delay
    await delay(100)
    await initCharts()
  }
}, { deep: true })

// Clean up charts before component is unmounted
onBeforeUnmount(() => {
  destroyAllCharts()
})

// Helper function to destroy all chart instances
const destroyAllCharts = () => {
  console.log('Destroying all chart instances')

  // Clear existing chart instances
  if (minutesChartInstance) {
    console.log('Destroying minutes chart instance')
    try {
      minutesChartInstance.destroy()
    } catch (e) {
      console.log('Error destroying minutes chart:', e)
    }
    minutesChartInstance = null
  }

  // Destroy performance chart instances
  Object.keys(performanceChartInstances).forEach(key => {
    if (performanceChartInstances[key]) {
      console.log(`Destroying chart instance: ${key}`)
      try {
        performanceChartInstances[key].destroy()
      } catch (e) {
        console.log(`Error destroying performance chart ${key}:`, e)
      }
    }
  })

  // Reset the instances object
  performanceChartInstances = {}
}

// Initialize all charts
const initCharts = async () => {
  if (chartsInitialized) {
    console.log('Charts already initialized, skipping')
    return
  }

  // Make sure we're mounted and DOM is ready
  await nextTick()
  await delay(200)

  console.log('Initializing charts with data validation:', hasValidInterviewData.value,
              'Chart elements exist:', !!usageChart.value,
              'Interview data length:', interviewData.value.length)

  // Destroy any existing charts first
  destroyAllCharts()

  // Initialize usage chart if we have data
  if (hasValidInterviewData.value && usageChart.value) {
    try {
      console.log('Initializing usage chart')

      // Process data
      const { dates, minutesPerDay, gradeData } = processInterviewData()

      console.log('Chart data ready:', { dates, minutesPerDay, gradeData })

      // Create chart
      minutesChartInstance = new Chart(usageChart.value, {
        type: 'bar',
        data: {
          labels: dates,
          datasets: [
            {
              label: 'Minutes Used per Day',
              data: minutesPerDay,
              borderColor: '#3949AB',
              backgroundColor: 'rgba(57, 73, 171, 0.8)',
              borderWidth: 1,
              borderRadius: 4,
              barPercentage: 0.6,
              categoryPercentage: 0.7,
              yAxisID: 'y'
            },
            {
              label: 'Overall Score',
              data: gradeData,
              type: 'line',
              borderColor: '#F57C00',
              backgroundColor: 'rgba(245, 124, 0, 0.1)',
              borderWidth: 3,
              borderDash: [5, 5],
              fill: false,
              tension: 0.4,
              pointRadius: 5,
              pointBackgroundColor: '#F57C00',
              pointHoverRadius: 7,
              yAxisID: 'y1'
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          interaction: {
            mode: 'index',
            intersect: false,
          },
          animation: {
            duration: 1000,
            easing: 'easeOutQuart'
          },
          plugins: {
            legend: {
              display: true,
              position: 'top',
              labels: {
                usePointStyle: true,
                padding: 20
              }
            },
            tooltip: {
              mode: 'index',
              intersect: false,
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              titleColor: '#fff',
              bodyColor: '#fff',
              borderColor: '#6200EA',
              borderWidth: 1,
              callbacks: {
                label: function(context) {
                  let label = context.dataset.label || ''
                  if (label) {
                    label += ': '
                  }
                  if (context.datasetIndex === 0) {
                    label += context.parsed.y + ' minutes' + (context.parsed.y === 1 ? '' : '')
                  } else {
                    label += context.parsed.y.toFixed(1) + ' / 10'
                  }
                  return label
                }
              }
            }
          },
          scales: {
            x: {
              title: {
                display: true,
                text: 'Date',
                color: '#3949AB'
              },
              grid: {
                display: false
              }
            },
            y: {
              type: 'linear',
              display: true,
              position: 'left',
              title: {
                display: true,
                text: 'Minutes per Day',
                color: '#3949AB'
              },
              beginAtZero: true,
              suggestedMax: Math.max(...minutesPerDay) * 1.2, // Add 20% to the max value for visual padding
              grid: {
                color: 'rgba(0, 0, 0, 0.05)'
              },
              ticks: {
                precision: 0,
                stepSize: Math.max(1, Math.ceil(Math.max(...minutesPerDay) / 5)) // Set reasonable step size
              }
            },
            y1: {
              type: 'linear',
              display: true,
              position: 'right',
              title: {
                display: true,
                text: 'Score',
                color: '#F57C00'
              },
              min: 0,
              max: 10,
              grid: {
                drawOnChartArea: false,
              },
              ticks: {
                color: '#F57C00',
                stepSize: 2,
                callback: function(value) {
                  return value.toFixed(0)
                }
              }
            }
          }
        }
      })

      console.log('Usage chart initialized successfully')
    } catch (error) {
      console.error('Error initializing usage chart:', error)
    }
  }

  // Initialize performance charts
  await initPerformanceCharts()

  chartsInitialized = true
}

// Initialize performance charts
const initPerformanceCharts = async () => {
  try {
    console.log('Initializing performance charts')

    // Create a mapping of categories to colors
    const colorMap = {
      'Personal Background': '#00897B',           // Teal
      'Hobbies and Interests': '#1E88E5',         // Bright blue
      'Communication Skills': '#FB8C00',          // Bright orange
      'Authenticity and Connection': '#8E24AA',   // Bright purple
      // Add more colors for potential other categories
      'default': '#546E7A'                        // Blue-grey
    }

    // Wait for DOM to be updated with canvases
    await nextTick()
    await delay(200)

    // Get the chart map based on their position
    const chartCanvasMap = {
      0: document.getElementById(`chart-${categoryList.value[0]?.replace(/\s+/g, '-').toLowerCase()}`),
      1: document.getElementById(`chart-${categoryList.value[1]?.replace(/\s+/g, '-').toLowerCase()}`),
      2: document.getElementById(`chart-${categoryList.value[2]?.replace(/\s+/g, '-').toLowerCase()}`),
      3: document.getElementById(`chart-${categoryList.value[3]?.replace(/\s+/g, '-').toLowerCase()}`)
    }

    // Create charts for each category (but only up to 4)
    const categoriesToShow = categoryList.value.slice(0, 4)
    for (let i = 0; i < categoriesToShow.length; i++) {
      const category = categoriesToShow[i]
      const canvasElement = chartCanvasMap[i]

      await delay(50) // Small delay between each chart creation

      if (!canvasElement) {
        console.error(`Canvas not found for category position ${i}: ${category}`)
        continue
      }

      // Skip if we already have a chart for this canvas
      const canvasId = canvasElement.id
      if (performanceChartInstances[canvasId]) {
        console.log(`Chart for ${canvasId} already exists, skipping`)
        continue
      }

      console.log(`Creating chart for ${category} with canvas ID: ${canvasId}`)

      // Get data for this category
      const chartData = getScoresByCategory(category)
      console.log(`Chart data for ${category}:`, chartData)

      // Create chart instance
      try {
        const ctx = canvasElement.getContext('2d')
        const chartInstance = new Chart(ctx, {
          type: 'line',
          data: {
            labels: chartData.dates,
            datasets: [{
              label: category,
              data: chartData.scores,
              borderColor: colorMap[category] || colorMap.default,
              backgroundColor: `${colorMap[category] || colorMap.default}22`,  // More transparent background
              borderWidth: 3,  // Slightly thicker line
              fill: true,
              tension: 0.4,
              pointRadius: 4,  // Slightly larger points
              pointHoverRadius: 6
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                mode: 'index',
                intersect: false,
                callbacks: {
                  label: function(context) {
                    let label = context.dataset.label || ''
                    if (label) {
                      label += ': '
                    }
                    if (context.parsed.y !== null) {
                      label += context.parsed.y.toFixed(1) + ' / 10'
                    }
                    return label
                  }
                }
              }
            },
            scales: {
              x: {
                display: true,
                grid: {
                  display: false
                }
              },
              y: {
                display: true,
                min: 0,
                max: 10,
                ticks: {
                  stepSize: 2,
                  callback: function(value) {
                    return value.toFixed(0)
                  }
                },
                grid: {
                  color: 'rgba(0, 0, 0, 0.05)'
                }
              }
            },
            animation: {
              duration: 1000
            }
          }
        })

        // Store the chart instance for cleanup later
        performanceChartInstances[canvasId] = chartInstance
        console.log(`Chart for ${category} created successfully`)
      } catch (err) {
        console.error(`Error creating chart for ${category}:`, err)
      }
    }

    console.log('Performance charts initialized successfully')
  } catch (error) {
    console.error('Error initializing performance charts:', error)
  }
}

// Open the voice assistant dialog
function openVoiceAssistant() {
  voiceAssistantDialog.value = true
}

// Open the onboarding dialog
function openOnboarding() {
  showOnboarding.value = true
}

// Handle purchase
async function handlePurchase(plan) {
  purchaseLoading.value = true
  try {
    console.log('Processing purchase for plan:', plan)
    // Close the dialog after purchase is handled by the PurchaseMinutesDialog component
    showPurchaseDialog.value = false
    // Refresh transactions after purchase
    await fetchTransactions()
  } catch (error) {
    console.error('Error processing purchase:', error)
  } finally {
    purchaseLoading.value = false
  }
}

// Navigate to interviews page
function goToInterviews() {
  try {
    // Use router to navigate to interviews page
    router.push('/interviews')
  } catch (error) {
    console.error('Error navigating to interviews page:', error)
    // Fallback to direct URL if router navigation fails
    window.location.href = '/interviews'
  }
}

// Define meta for page
defineOptions({
  name: 'DashboardPage',
  meta: {
    layout: 'default',
    requiresAuth: true
  }
})

// Helper function to get scores by category over time
const getScoresByCategory = (category) => {
  // Group by date first
  const scoresByDate = {}

  console.log(`Getting scores for category: ${category}`, interviewData.value)

  // Go through all interviews with their scores
  interviewData.value.forEach(interview => {
    if (!interview.scores || !interview.scores[category]) return

    const date = new Date(interview.created_at).toLocaleDateString()
    const score = Number(interview.scores[category])

    if (score) {
      console.log(`Found score ${score} for ${category} on ${date}`)
      if (!scoresByDate[date]) {
        scoresByDate[date] = []
      }

      scoresByDate[date].push(score)
    }
  })

  // Convert to arrays for charting
  const dates = Object.keys(scoresByDate)

  // Calculate average score per date
  const avgScores = dates.map(date => {
    const scoresOnDate = scoresByDate[date]
    const sum = scoresOnDate.reduce((total, score) => total + Number(score), 0)
    return sum / scoresOnDate.length
  })

  console.log(`Processed scores for ${category}:`, { dates, scores: avgScores })

  // If we have no data, return a placeholder point
  if (dates.length === 0) {
    console.log(`No data found for ${category}, returning placeholder`)
    return {
      dates: [new Date().toLocaleDateString()],
      scores: [7] // Default placeholder score
    }
  }

  return { dates, scores: avgScores }
}

// Helper function to get color based on score
const getScoreColor = (score) => {
  if (score >= 8) return 'green-lighten-2'  // Green for excellent scores
  if (score >= 6) return 'blue-lighten-2'      // Blue for good scores
  if (score >= 4) return 'orange-lighten-2'   // Yellow/orange for average scores
  return 'red-lighten-2'                     // Red for low scores
}

// Helper function to get color based on filler word percentage
const getFillerWordColor = (percentage) => {
  if (percentage <= 2) return 'green-lighten-2'  // Green for excellent (low filler words)
  if (percentage <= 5) return 'blue-lighten-2'      // Blue for good
  if (percentage <= 10) return 'orange-lighten-2'  // Yellow/orange for average
  return 'red-lighten-2'                          // Red for high filler words
}

// Add a watch for the onboarding dialog
watch(showOnboarding, async (newVal) => {
  // When the onboarding dialog is closed, refresh profile data
  if (!newVal) {
    await checkProfileCompletion()
  }
})
</script>

<style scoped>
/* Ensure dialog is truly fullscreen on mobile */
:deep(.mobile-fullscreen-dialog) {
  margin: 0 !important;
  padding: 0 !important;
  height: 100% !important;
  width: 100% !important;
  max-width: 100% !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

:deep(.mobile-fullscreen-dialog .v-overlay__content) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

:deep(.mobile-fullscreen-dialog .v-card) {
  height: 100vh !important;
  width: 100% !important;
  max-width: 100% !important;
  border-radius: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure score chips have good contrast in all themes */
.score-chip {
  font-weight: 700 !important;
  color: white !important;
  text-shadow: 0px 0px 1px rgba(0, 0, 0, 0.3);
}

:deep(.v-theme--light) .score-chip {
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.1);
}

:deep(.v-theme--dark) .score-chip {
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

/* Style score chips by color */
:deep(.score-chip.bg-success) {
  background-color: #43A047 !important;
}

:deep(.score-chip.bg-info) {
  background-color: #1E88E5 !important;
}

:deep(.score-chip.bg-warning) {
  background-color: #FB8C00 !important;
}

:deep(.score-chip.bg-error) {
  background-color: #E53935 !important;
}
</style>

<route>
{
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}
</route>
