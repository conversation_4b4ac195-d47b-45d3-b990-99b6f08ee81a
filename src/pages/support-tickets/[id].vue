<template>
  <div class="support-ticket-layout">
    <v-row
      no-gutters
      class="fill-height"
    >
      <!-- Left Column - Ticket List -->
      <v-col
        v-if="!mobile || !messagesStore.activeConversationId"
        cols="auto"
        class="ticket-list-column border-e"
        :style="!mobile ? 'width: 320px;' : ''"
      >
        <div class="d-flex flex-column h-100">
          <!-- Header -->
          <div class="pa-3 border-b flex-shrink-0 d-flex align-center">
            <!-- Dropdown Menu for Staff/Admin Options -->
            <v-menu v-if="messagesStore.isSupportStaff">
              <template #activator="{ props }">
                <v-btn
                  icon="mdi-dots-vertical"
                  variant="text"
                  size="small"
                  v-bind="props"
                  class="me-2"
                />
              </template>
              <v-list>
                <v-list-item>
                  <v-switch
                    v-model="messagesStore.showClosedConversations"
                    color="primary"
                    density="compact"
                    hide-details
                    class="ma-0"
                  >
                    <template #label>
                      <span class="text-body-2 ml-2">Show closed tickets</span>
                    </template>
                  </v-switch>
                </v-list-item>
              </v-list>
            </v-menu>

            <h2 class="text-h6 flex-grow-1">
              Support Tickets
            </h2>

            <!-- New Ticket button for regular users -->
            <v-btn
              v-if="!messagesStore.isSupportStaff"
              icon="mdi-plus"
              variant="text"
              @click="createNewTicket"
            />
            <!-- New Message button for staff/admin -->
            <v-btn
              v-else
              icon="mdi-plus"
              variant="text"
              @click="showNewMessageInterface = true"
            />
          </div>

          <!-- Search -->
          <div class="pa-3 border-b flex-shrink-0">
            <v-text-field
              v-model="searchQuery"
              placeholder="Search tickets..."
              prepend-inner-icon="mdi-magnify"
              variant="outlined"
              density="compact"
              hide-details
            />
          </div>

          <!-- Ticket List -->
          <div class="flex-grow-1 overflow-y-auto">
            <v-list
              density="compact"
              nav
              class="pa-0"
            >
              <v-list-item
                v-for="ticket in filteredTickets"
                :key="ticket.id"
                :active="messagesStore.activeConversationId === ticket.id"
                class="mb-1"
                @click="selectTicket(ticket.id)"
              >
                <template #prepend>
                  <UserAvatar
                    :user="getUserParticipant(ticket)?.user"
                    size="32"
                  />
                </template>

                <v-list-item-title class="text-body-2">
                  {{ getTicketTitle(ticket) }}
                </v-list-item-title>

                <v-list-item-subtitle class="text-caption">
                  {{ getLastMessagePreview(ticket) }}
                </v-list-item-subtitle>

                <template #append>
                  <div class="d-flex flex-column align-end">
                    <v-chip
                      v-if="getTicketStatus(ticket) === 'new'"
                      size="x-small"
                      color="error"
                      variant="flat"
                    >
                      New
                    </v-chip>
                    <span class="text-caption text-grey">
                      {{ formatTime(ticket.updated_at) }}
                    </span>
                  </div>
                </template>
              </v-list-item>
            </v-list>

            <!-- Empty state -->
            <div
              v-if="supportTickets.length === 0"
              class="pa-4 text-center"
            >
              <v-icon
                size="48"
                color="grey-lighten-2"
              >
                mdi-ticket
              </v-icon>
              <p class="text-grey mt-2">
                {{ messagesStore.isSupportStaff ? 'No support tickets yet' : 'No tickets created yet' }}
              </p>
              <v-btn
                v-if="!messagesStore.isSupportStaff"
                color="primary"
                variant="outlined"
                class="mt-2"
                @click="createNewTicket"
              >
                Create Support Ticket
              </v-btn>
            </div>
          </div>
        </div>
      </v-col>

      <!-- Right Column - Chat Area -->
      <v-col
        v-if="!mobile || messagesStore.activeConversationId"
        class="d-flex flex-column"
      >
        <div class="chat-container">
          <!-- Chat Header -->
          <div
            v-if="messagesStore.activeConversation"
            class="chat-header pa-4 border-b"
          >
            <div class="d-flex align-center justify-space-between">
              <div class="d-flex align-center">
                <!-- Mobile Back Button -->
                <v-btn
                  v-if="mobile"
                  icon="mdi-arrow-left"
                  variant="text"
                  class="me-2"
                  @click="goBackToTicketList"
                />

                <UserAvatar
                  :user="getUserParticipant(messagesStore.activeConversation)?.user"
                  size="32"
                  class="me-3"
                />
                <div>
                  <div class="text-h6">
                    {{ getTicketTitle(messagesStore.activeConversation) }}
                  </div>
                  <div class="text-caption text-grey">
                    {{ getTicketSubtitle(messagesStore.activeConversation) }}
                  </div>
                </div>
              </div>

              <div class="d-flex align-center">
                <v-chip
                  :color="getTicketStatusColor(messagesStore.activeConversation)"
                  variant="flat"
                  size="small"
                  class="me-2"
                >
                  {{ getTicketStatus(messagesStore.activeConversation) }}
                </v-chip>

                <!-- Admin Actions -->
                <template v-if="messagesStore.isSupportStaff">
                  <v-menu v-if="messagesStore.activeConversation.status !== 'closed'">
                    <template #activator="{ props }">
                      <v-btn
                        icon="mdi-close-circle"
                        variant="text"
                        size="small"
                        color="error"
                        v-bind="props"
                        class="me-2"
                      />
                    </template>
                    <v-list>
                      <v-list-item @click="closeTicket">
                        <template #prepend>
                          <v-icon>mdi-close-circle</v-icon>
                        </template>
                        <v-list-item-title>Close Ticket</v-list-item-title>
                      </v-list-item>
                    </v-list>
                  </v-menu>

                  <v-btn
                    v-else
                    icon="mdi-refresh"
                    variant="text"
                    size="small"
                    color="success"
                    class="me-2"
                    @click="reopenTicket"
                  />
                </template>
              </div>
            </div>
          </div>

          <!-- Messages Area -->
          <div
            v-if="messagesStore.activeConversation"
            ref="messagesContainer"
            class="messages-area pa-4"
          >
            <div
              v-for="(message, index) in messagesStore.activeMessages"
              :key="message.id"
              class="mb-2"
            >
              <!-- Show timestamp if it's the first message or there's a significant time gap -->
              <div
                v-if="shouldShowTimestamp(message, index)"
                class="text-center my-4"
              >
                <v-chip
                  size="small"
                  variant="outlined"
                  color="grey"
                  class="text-caption"
                >
                  {{ formatMessageTimestamp(message.created_at) }}
                </v-chip>
              </div>

              <!-- Show sender name if it's from a different sender than previous message -->
              <div
                v-if="shouldShowSenderName(message, index)"
                :class="[
                  'text-caption text-grey mb-1',
                  message.sender_id === authStore.user?.id ? 'text-end' : 'text-start'
                ]"
              >
                {{ getSenderName(message) }}
              </div>

              <div
                :class="[
                  'd-flex',
                  message.sender_id === authStore.user?.id ? 'justify-end' : 'justify-start'
                ]"
              >
                <div
                  :class="[
                    'message-bubble pa-3',
                    message.sender_id === authStore.user?.id
                      ? 'bg-primary text-white sent-message'
                      : 'bg-grey-lighten-4 received-message'
                  ]"
                  style="max-width: 70%;"
                >
                  <div class="text-body-2">
                    {{ message.content }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div
            v-else-if="!showNewMessageInterface"
            class="messages-area d-flex align-center justify-center"
          >
            <div class="text-center">
              <v-icon
                size="64"
                color="grey-lighten-2"
              >
                mdi-ticket-outline
              </v-icon>
              <div class="text-h6 mt-4 text-grey">
                Ticket not found
              </div>
              <div class="text-body-2 text-grey mt-2">
                This ticket may not exist or you don't have access to it
              </div>
              <v-btn
                color="primary"
                variant="outlined"
                class="mt-4"
                @click="goBackToTicketList"
              >
                Back to Tickets
              </v-btn>
            </div>
          </div>

          <!-- New Message Interface (for staff/admin creating new support conversations) -->
          <div
            v-else-if="showNewMessageInterface"
            class="messages-area"
          >
            <NewMessageInterface
              message-type="support"
              :show-to-field="true"
              @close="showNewMessageInterface = false"
              @conversation-created="handleConversationCreated"
            />
          </div>

          <!-- Message Input -->
          <div
            v-if="messagesStore.activeConversation"
            class="message-input border-t pa-4"
          >
            <div class="d-flex align-center">
              <v-text-field
                v-model="newMessage"
                placeholder="Type a message..."
                variant="outlined"
                density="compact"
                hide-details
                class="flex-grow-1 me-2"
                @keyup.enter="sendMessage"
              />
              <v-btn
                icon="mdi-send"
                color="primary"
                :disabled="!newMessage.trim()"
                @click="sendMessage"
              />
            </div>
          </div>
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useDisplay } from 'vuetify'
import { useMessagesStore } from '@/stores/messages'
import { useAuthStore } from '@/stores/auth'
import { useRouter, useRoute } from 'vue-router'
import NewMessageInterface from '@/components/NewMessageInterface.vue'
import UserAvatar from '@/components/UserAvatar.vue'

// Define route meta to require authentication
defineOptions({
  meta: {
    requiresAuth: true
  }
})

// Props for the dynamic route
const props = defineProps({
  id: {
    type: String,
    required: true
  }
})

// Stores
const messagesStore = useMessagesStore()
const authStore = useAuthStore()
const router = useRouter()
const route = useRoute()

// Display composable for responsive design
const { mobile } = useDisplay({ mobileBreakpoint: 'md' })

// State
const searchQuery = ref('')
const newMessage = ref('')
const showNewMessageInterface = ref(false)
const messagesContainer = ref(null)

// Computed
const supportTickets = computed(() => {
  return messagesStore.conversationsByCategory.support || []
})

const filteredTickets = computed(() => {
  if (!searchQuery.value) return supportTickets.value

  const query = searchQuery.value.toLowerCase()
  return supportTickets.value.filter(ticket => {
    const title = getTicketTitle(ticket).toLowerCase()
    const preview = getLastMessagePreview(ticket).toLowerCase()
    return title.includes(query) || preview.includes(query)
  })
})

// Methods (same as original support-tickets.vue)
function getTicketTitle(ticket) {
  // Handle pending conversation
  if (ticket.id === 'pending') {
    return 'New Support Request'
  }

  if (messagesStore.isSupportStaff) {
    // For staff, show the user's name who created the ticket
    const userParticipant = ticket.participants?.find(
      p => p.user?.role === 'user'
    )
    if (userParticipant?.user) {
      const userName = userParticipant.user.first_name
        ? `${userParticipant.user.first_name} ${userParticipant.user.last_name || ''}`.trim()
        : userParticipant.user.email
      return userName
    }
    return 'Unknown User'
  }
  // For regular users, just show "Support Request"
  return `Support Request #${ticket.id.slice(0, 8)}`
}

function getTicketSubtitle(ticket) {
  // Handle pending conversation
  if (ticket.id === 'pending') {
    return 'Start typing to create your support request'
  }

  if (messagesStore.isSupportStaff) {
    // For staff, show the user's email
    const userParticipant = ticket.participants?.find(
      p => p.user?.role === 'user'
    )
    return userParticipant?.user?.email || 'No email available'
  }
  // For regular users
  return `Created ${formatDate(ticket.created_at)}`
}

function getTicketStatus(ticket) {
  // Handle pending conversation
  if (ticket.id === 'pending') {
    return 'draft'
  }

  // Check if ticket is closed
  if (ticket.status === 'closed') {
    return 'closed'
  }

  const ticketMessages = messagesStore.messages[ticket.id] || []

  if (ticketMessages.length === 0) {
    return 'new'
  }

  // Check if there are any staff/admin replies
  const staffMessages = ticketMessages.filter(msg => {
    const isStaff = msg.sender?.role === 'staff' || msg.sender?.role === 'admin'
    return isStaff
  })

  if (staffMessages.length === 0) {
    return 'new'
  }

  // Find the last message in the conversation
  const lastMessage = ticketMessages[ticketMessages.length - 1]

  if (lastMessage?.sender?.role === 'staff' || lastMessage?.sender?.role === 'admin') {
    return 'answered'
  } else {
    return 'waiting'
  }
}

function getTicketStatusColor(ticket) {
  const status = getTicketStatus(ticket)
  switch (status) {
    case 'new': return 'error'
    case 'waiting': return 'warning'
    case 'answered': return 'success'
    case 'closed': return 'grey'
    case 'draft': return 'info'
    default: return 'grey'
  }
}

function getLastMessagePreview(ticket) {
  // Handle pending conversation
  if (ticket.id === 'pending') {
    return 'Start typing your message...'
  }

  const lastMessage = ticket.last_message?.[0]
  if (!lastMessage) return 'No messages yet'

  return lastMessage.content.length > 50
    ? `${lastMessage.content.substring(0, 50)}...`
    : lastMessage.content
}

function getSenderName(message) {
  if (!message.sender) return 'Unknown'

  // Show role badge for staff/admin
  const role = message.sender.role
  const name = message.sender.first_name
    ? `${message.sender.first_name} ${message.sender.last_name || ''}`.trim()
    : message.sender.email

  if (role === 'admin' || role === 'staff') {
    return `${name} (Support)`
  }

  return name
}

function formatTime(timestamp) {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  const now = new Date()
  const diffInHours = (now - date) / (1000 * 60 * 60)

  if (diffInHours < 24) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }
  if (diffInHours < 168) { // 7 days
    return date.toLocaleDateString([], { weekday: 'short' })
  }
  return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
}

function formatDate(timestamp) {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleDateString([], {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

function shouldShowTimestamp(message, index) {
  if (index === 0) return true // Always show timestamp for first message

  const previousMessage = messagesStore.activeMessages[index - 1]
  if (!previousMessage) return true

  const currentTime = new Date(message.created_at)
  const previousTime = new Date(previousMessage.created_at)
  const timeDiff = (currentTime - previousTime) / (1000 * 60) // Difference in minutes

  return timeDiff > 5 // Show timestamp if more than 5 minutes apart
}

function shouldShowSenderName(message, index) {
  if (index === 0) return true // Always show sender name for first message

  const previousMessage = messagesStore.activeMessages[index - 1]
  if (!previousMessage) return true

  // Show sender name if different from previous message sender
  return message.sender_id !== previousMessage.sender_id
}

function formatMessageTimestamp(timestamp) {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  const now = new Date()
  const diffInHours = (now - date) / (1000 * 60 * 60)
  const diffInDays = diffInHours / 24

  if (diffInDays < 1) {
    // Today - show time
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  } else if (diffInDays < 7) {
    // This week - show day and time
    return date.toLocaleDateString([], {
      weekday: 'short',
      hour: '2-digit',
      minute: '2-digit'
    })
  } else {
    // Older - show date and time
    return date.toLocaleDateString([], {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

async function selectTicket(ticketId) {
  // Update the URL when selecting a ticket
  if (route.params.id !== ticketId) {
    await router.push(`/support-tickets/${ticketId}`)
  }

  messagesStore.setActiveConversation(ticketId)

  // Scroll to bottom after messages load
  await nextTick()
  scrollToBottom()
}

function goBackToTicketList() {
  router.push('/support-tickets')
}

async function sendMessage() {
  if (!newMessage.value.trim() || !messagesStore.activeConversationId) return

  try {
    await messagesStore.sendMessage(
      messagesStore.activeConversationId,
      newMessage.value.trim()
    )
    newMessage.value = ''

    // Scroll to bottom after sending
    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('Failed to send message:', error)
  }
}

async function createNewTicket() {
  try {
    // Create a pending support conversation and set it as active
    messagesStore.createPendingSupportConversation()

    // Navigate to the pending conversation
    await router.push('/support-tickets/pending')
  } catch (error) {
    console.error('Failed to create new ticket:', error)
  }
}

function handleConversationCreated(conversation) {
  showNewMessageInterface.value = false

  if (conversation.id) {
    nextTick(() => {
      selectTicket(conversation.id)
    })
  }
}

function scrollToBottom() {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// Watch for new messages to auto-scroll
watch(
  () => messagesStore.activeMessages.length,
  () => {
    nextTick(() => scrollToBottom())
  }
)

// Watch for route parameter changes to load the correct ticket
watch(
  () => props.id,
  async (newId) => {
    if (newId && newId !== messagesStore.activeConversationId) {
      messagesStore.setActiveConversation(newId)
      await nextTick()
      scrollToBottom()
    }
  },
  { immediate: true }
)

// Lifecycle
onMounted(async () => {
  // Check if user has access to support tickets
  if (!messagesStore.isSupportStaff && authStore.userProfile?.role === 'user') {
    // Regular users can access support tickets
  } else if (!messagesStore.isSupportStaff) {
    // Redirect if not authorized
    router.push('/messages')
    return
  }

  // Initialize data
  await messagesStore.fetchConversationsEnhanced()
  messagesStore.subscribeToConversations()

  // Set the active conversation based on the route parameter
  if (props.id) {
    messagesStore.setActiveConversation(props.id)
    await nextTick()
    scrollToBottom()
  }
})

onUnmounted(() => {
  messagesStore.setActiveConversation(null)
})

async function closeTicket() {
  if (!messagesStore.activeConversationId || !messagesStore.isSupportStaff) return

  try {
    await messagesStore.updateConversationStatus(messagesStore.activeConversationId, 'closed')
    console.log('Ticket closed successfully')
  } catch (error) {
    console.error('Failed to close ticket:', error)
  }
}

async function reopenTicket() {
  if (!messagesStore.activeConversationId || !messagesStore.isSupportStaff) return

  try {
    await messagesStore.updateConversationStatus(messagesStore.activeConversationId, 'open')
    console.log('Ticket reopened successfully')
  } catch (error) {
    console.error('Failed to reopen ticket:', error)
  }
}

// Get user participant from a ticket
function getUserParticipant(ticket) {
  if (!ticket.participants) return null

  if (messagesStore.isSupportStaff) {
    // For staff, get the user (non-staff) participant
    return ticket.participants.find(p => p.user?.role === 'user')
  } else {
    // For regular users, get the current user participant
    return ticket.participants.find(p => p.user_id === authStore.user?.id)
  }
}
</script>

<style scoped>
.support-ticket-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ticket-list-column {
  min-width: 320px;
  max-width: 320px;
}

.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.chat-header {
  flex-shrink: 0;
}

.messages-area {
  flex: 1;
  overflow-y: auto;
  min-height: calc(100vh - 282px);
  max-height: calc(100vh - 282px);
}

.message-input {
  flex-shrink: 0;
  background: white;
}

@media (max-width: 599px) {
  .ticket-list-column {
    min-width: 100%;
    max-width: 100%;
  }
}

.message-bubble {
  word-wrap: break-word;
  border-radius: 18px;
  position: relative;
}

.sent-message {
  border-bottom-right-radius: 4px;
}

.received-message {
  border-bottom-left-radius: 4px;
}

.border-e {
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.border-b {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.border-t {
  border-top: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

/* Fix for v-row height */
.support-ticket-layout .v-row {
  margin: 0;
}

/* Ensure full height inheritance */
:deep(.v-main__wrap) {
  display: flex;
  flex-direction: column;
}

.h-100 {
  height: 100%;
}

.fill-height {
  height: 100%;
}
</style>

<route>
{
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}
</route>
