<template>
  
    <!-- Documentation Content -->
    <div class="flex-grow-1 overflow-y-auto pa-6">
      <div class="d-flex align-center mb-6">
        <h1 class="text-h4">Documentation</h1>
      </div>

      <!-- Placeholder content -->
      <v-card
        variant="elevated"
        elevation="3"
      >
        <v-card-text class="text-center py-16">
          <v-icon
            size="64"
            color="primary"
            class="mb-4"
          >
            mdi-book-open-page-variant
          </v-icon>
          <h2 class="text-h5 mb-4">
            Documentation Coming Soon
          </h2>
          <p class="text-body-1 text-medium-emphasis">
            We're working on comprehensive documentation to help you get the most out of StageCoach AI.
          </p>
        </v-card-text>
      </v-card>
    </div>
  
</template>

<script setup>

// Define route meta to require authentication
defineOptions({
  meta: {
    requiresAuth: true
  }
})
</script>

<style scoped>
.border-b {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}
</style>

<route>
{
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}
</route>
