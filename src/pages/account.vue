<template>
  
    <!-- Account Content -->
    <div class="flex-grow-1 overflow-y-auto pa-6">
      <div class="d-flex align-center mb-6">
        <h1 class="text-h4">Account</h1>
      </div>

      <!-- Account Information Card -->
      <v-card
        variant="elevated"
        class="mb-4"
        elevation="3"
      >
        <v-card-title class="d-flex align-center">
          <span class="text-h6">Account Information</span>
          <v-spacer />
          <v-btn
            color="primary"
            variant="text"
            density="comfortable"
            size="small"
            @click="startEditingAccount"
          >
            EDIT
          </v-btn>
        </v-card-title>
        <v-card-text>
          <div
            v-if="profileLoading"
            class="d-flex justify-center pa-4"
          >
            <v-progress-circular
              indeterminate
              color="primary"
            />
          </div>

          <div v-else>
            <v-list>
              <v-list-item>
                <v-list-item-title>Email</v-list-item-title>
                <v-list-item-subtitle>
                  {{ authStore.user?.email || 'Not available' }}
                </v-list-item-subtitle>
              </v-list-item>

              <v-list-item>
                <v-list-item-title>Name</v-list-item-title>
                <v-list-item-subtitle>
                  {{ userProfile?.name || 'Not specified' }}
                </v-list-item-subtitle>
              </v-list-item>

              <v-list-item>
                <v-list-item-title>Phone</v-list-item-title>
                <v-list-item-subtitle>
                  {{ userProfile?.phone || 'Not specified' }}
                </v-list-item-subtitle>
              </v-list-item>

              <v-list-item>
                <v-list-item-title>Member Since</v-list-item-title>
                <v-list-item-subtitle>
                  {{ authStore.user?.created_at ? new Date(authStore.user.created_at).toLocaleDateString() : 'Unknown' }}
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </div>
        </v-card-text>
      </v-card>

      <!-- Billing & Subscription Card -->
      <v-card
        variant="elevated"
        class="mb-4"
        elevation="3"
      >
        <v-card-title class="text-h6">
          Billing & Subscription
        </v-card-title>
        <v-card-text>
          <v-alert
            type="info"
            variant="tonal"
            class="mb-4"
          >
            <v-icon
              start
              icon="mdi-information"
            />
            Currently using a pay-per-use model. Purchase minutes as needed.
          </v-alert>

          <div class="d-flex align-center justify-space-between">
            <div>
              <h4 class="text-subtitle-1">
                Current Balance
              </h4>
              <p class="text-body-2 text-medium-emphasis">
                Available interview minutes
              </p>
            </div>
            <div class="text-right">
              <span class="text-h5 font-weight-bold">
                {{ userBalance }} min
              </span>
            </div>
          </div>

          <v-divider class="my-4" />

          <v-btn
            color="primary"
            prepend-icon="mdi-cart-plus"
            @click="showPurchaseDialog = true"
          >
            Buy More Minutes
          </v-btn>
        </v-card-text>
      </v-card>

      <!-- Account Settings Card -->
      <v-card
        variant="elevated"
        class="mb-4"
        elevation="3"
      >
        <v-card-title class="text-h6">
          Account Settings
        </v-card-title>
        <v-card-text>
          <v-list>
            <v-list-item
              prepend-icon="mdi-key-variant"
              title="Change Password"
              subtitle="Update your account password"
              @click="showChangePasswordDialog = true"
            />
            <v-list-item
              prepend-icon="mdi-delete-forever"
              title="Delete Account"
              subtitle="Permanently delete your account and all data"
              class="text-error"
              @click="showDeleteAccountDialog = true"
            />
          </v-list>
        </v-card-text>
      </v-card>

      <!-- Account Information editing dialog -->
      <v-dialog
        v-model="isEditingAccount"
        max-width="600px"
      >
        <v-card>
          <v-card-title>Edit Account Information</v-card-title>
          <v-card-text>
            <v-text-field
              v-model="editedAccount.name"
              label="Name"
              variant="outlined"
              class="mb-4"
            />

            <v-text-field
              v-model="editedAccount.phone"
              label="Phone"
              variant="outlined"
              placeholder="(*************"
            />
          </v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="error"
              variant="text"
              @click="cancelEditingAccount"
            >
              CANCEL
            </v-btn>
            <v-btn
              color="success"
              variant="text"
              :loading="savingAccount"
              @click="saveAccount"
            >
              SAVE
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Change Password Dialog -->
      <v-dialog
        v-model="showChangePasswordDialog"
        max-width="500px"
      >
        <v-card>
          <v-card-title>Change Password</v-card-title>
          <v-card-text>
            <v-form
              ref="passwordForm"
              @submit.prevent="changePassword"
            >
              <v-text-field
                v-model="passwordData.currentPassword"
                label="Current Password"
                type="password"
                variant="outlined"
                class="mb-4"
                :rules="[v => !!v || 'Current password is required']"
              />

              <v-text-field
                v-model="passwordData.newPassword"
                label="New Password"
                type="password"
                variant="outlined"
                class="mb-4"
                :rules="[
                  v => !!v || 'New password is required',
                  v => v.length >= 6 || 'Password must be at least 6 characters'
                ]"
              />

              <v-text-field
                v-model="passwordData.confirmPassword"
                label="Confirm New Password"
                type="password"
                variant="outlined"
                :rules="[
                  v => !!v || 'Please confirm your password',
                  v => v === passwordData.newPassword || 'Passwords do not match'
                ]"
              />
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="error"
              variant="text"
              @click="cancelChangePassword"
            >
              CANCEL
            </v-btn>
            <v-btn
              color="success"
              variant="text"
              :loading="changingPassword"
              @click="changePassword"
            >
              CHANGE PASSWORD
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Delete Account Dialog -->
      <v-dialog
        v-model="showDeleteAccountDialog"
        max-width="500px"
      >
        <v-card>
          <v-card-title class="text-error">
            Delete Account
          </v-card-title>
          <v-card-text>
            <v-alert
              type="error"
              variant="tonal"
              class="mb-4"
            >
              <strong>Warning:</strong> This action cannot be undone. All your data will be permanently deleted.
            </v-alert>

            <p class="mb-4">
              To confirm account deletion, please type your email address below:
            </p>

            <v-text-field
              v-model="deleteConfirmEmail"
              label="Email Address"
              variant="outlined"
              :error="deleteEmailError"
              :error-messages="deleteEmailError ? 'Email does not match your account email' : ''"
            />
          </v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="primary"
              variant="text"
              @click="cancelDeleteAccount"
            >
              CANCEL
            </v-btn>
            <v-btn
              color="error"
              variant="text"
              :loading="deletingAccount"
              :disabled="deleteConfirmEmail !== authStore.user?.email"
              @click="deleteAccount"
            >
              DELETE ACCOUNT
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Purchase Minutes Dialog -->
      <PurchaseMinutesDialog
        v-model="showPurchaseDialog"
        :loading="purchaseLoading"
        @purchase="handlePurchase"
        @coupon-redeemed="fetchUserBalance"
      />

      <!-- Snackbar for messages -->
      <v-snackbar
        v-model="showSnackbar"
        :color="snackbarColor"
        :timeout="3000"
        location="top"
      >
        {{ snackbarMessage }}

        <template #actions>
          <v-btn
            variant="text"
            icon="mdi-close"
            @click="showSnackbar = false"
          />
        </template>
      </v-snackbar>
    </div>
  
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { supabase } from '@/plugins/supabase'
import { useRouter } from 'vue-router'
import PurchaseMinutesDialog from '@/components/PurchaseMinutesDialog.vue'

const authStore = useAuthStore()
const router = useRouter()

// User profile data
const userProfile = ref(null)
const profileLoading = ref(false)

// Account editing
const isEditingAccount = ref(false)
const editedAccount = ref({
  name: '',
  phone: ''
})
const savingAccount = ref(false)

// Password change
const showChangePasswordDialog = ref(false)
const passwordData = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})
const changingPassword = ref(false)
const passwordForm = ref(null)

// Account deletion
const showDeleteAccountDialog = ref(false)
const deleteConfirmEmail = ref('')
const deleteEmailError = ref(false)
const deletingAccount = ref(false)

// Purchase dialog
const showPurchaseDialog = ref(false)
const purchaseLoading = ref(false)

// User balance
const userBalance = ref(0)

// Snackbar for messages
const showSnackbar = ref(false)
const snackbarMessage = ref('')
const snackbarColor = ref('success')

// Define route meta to require authentication
defineOptions({
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
})

// Fetch user profile data
async function fetchUserProfile() {
  if (!authStore.user) return

  profileLoading.value = true

  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', authStore.user.id)
      .single()

    if (error) throw error

    userProfile.value = data
  } catch (err) {
    console.error('Error fetching user profile:', err)
  } finally {
    profileLoading.value = false
  }
}

// Fetch user balance
async function fetchUserBalance() {
  if (!authStore.user) return

  try {
    const { data, error } = await supabase
      .from('user_minutes')
      .select('minutes')
      .eq('user_id', authStore.user.id)

    if (error) throw error

    userBalance.value = data?.reduce((total, item) => total + (item.minutes || 0), 0) || 0
  } catch (err) {
    console.error('Error fetching user balance:', err)
  }
}

// Account editing functions
function startEditingAccount() {
  editedAccount.value = {
    name: userProfile.value?.name || '',
    phone: userProfile.value?.phone || ''
  }
  isEditingAccount.value = true
}

function cancelEditingAccount() {
  isEditingAccount.value = false
}

async function saveAccount() {
  if (!authStore.user) return

  savingAccount.value = true

  try {
    const { error } = await supabase
      .from('user_profiles')
      .update({
        name: editedAccount.value.name,
        phone: editedAccount.value.phone
      })
      .eq('user_id', authStore.user.id)

    if (error) throw error

    // Update local state
    if (userProfile.value) {
      userProfile.value.name = editedAccount.value.name
      userProfile.value.phone = editedAccount.value.phone
    }

    isEditingAccount.value = false
    showSuccessMessage('Account information updated successfully')
  } catch (err) {
    console.error('Error saving account:', err)
    showErrorMessage('Failed to save account information. Please try again.')
  } finally {
    savingAccount.value = false
  }
}

// Password change functions
function cancelChangePassword() {
  passwordData.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
  showChangePasswordDialog.value = false
}

async function changePassword() {
  if (!passwordForm.value?.validate()) return

  changingPassword.value = true

  try {
    const { error } = await supabase.auth.updateUser({
      password: passwordData.value.newPassword
    })

    if (error) throw error

    showChangePasswordDialog.value = false
    passwordData.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
    showSuccessMessage('Password changed successfully')
  } catch (err) {
    console.error('Error changing password:', err)
    showErrorMessage('Failed to change password. Please try again.')
  } finally {
    changingPassword.value = false
  }
}

// Account deletion functions
function cancelDeleteAccount() {
  deleteConfirmEmail.value = ''
  deleteEmailError.value = false
  showDeleteAccountDialog.value = false
}

async function deleteAccount() {
  if (deleteConfirmEmail.value !== authStore.user?.email) {
    deleteEmailError.value = true
    return
  }

  deletingAccount.value = true

  try {
    // Call the delete account function
    const { error } = await supabase.rpc('delete_user_account')

    if (error) throw error

    // Sign out the user
    await authStore.signOut()
    router.push('/')
    showSuccessMessage('Account deleted successfully')
  } catch (err) {
    console.error('Error deleting account:', err)
    showErrorMessage('Failed to delete account. Please try again.')
  } finally {
    deletingAccount.value = false
  }
}

// Purchase functions
async function handlePurchase(purchaseData) {
  purchaseLoading.value = true

  try {
    // Handle purchase logic here
    console.log('Purchase data:', purchaseData)
    showSuccessMessage('Purchase completed successfully')
    await fetchUserBalance()
  } catch (err) {
    console.error('Error processing purchase:', err)
    showErrorMessage('Failed to process purchase. Please try again.')
  } finally {
    purchaseLoading.value = false
  }
}

// Display error messages using the snackbar
function showErrorMessage(message) {
  snackbarMessage.value = message
  snackbarColor.value = 'error'
  showSnackbar.value = true
}

// Display success messages using the snackbar
function showSuccessMessage(message) {
  snackbarMessage.value = message
  snackbarColor.value = 'success'
  showSnackbar.value = true
}

// Fetch data on component mount
onMounted(async () => {
  await Promise.all([
    fetchUserProfile(),
    fetchUserBalance()
  ])
})
</script>

<style scoped>
.border-b {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}
</style>

<route>
{
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}
</route>
