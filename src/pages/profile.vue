<template>
  
    <!-- Profile Content -->
    <div class="flex-grow-1 overflow-y-auto pa-6">
      <div class="d-flex align-center mb-6">
        <h1 class="text-h4">Profile</h1>
      </div>

      <!-- Pageant Information Card -->
      <v-card
        variant="elevated"
        class="mb-4"
        elevation="3"
      >
        <v-card-title class="d-flex align-center">
          <span class="text-h6">Pageant Information</span>
          <v-menu
            location="top"
            :close-on-content-click="true"
            max-width="300"
          >
            <template #activator="{ props }">
              <v-icon
                class="ml-2"
                color="primary"
                size="x-small"
                v-bind="props"
                icon="mdi-help-circle-outline"
              />
            </template>
            <v-card>
              <v-card-text>
                Information about the pageant title you're competing for and current events related to it. This helps the AI generate more relevant interview questions.
              </v-card-text>
            </v-card>
          </v-menu>
          <v-spacer />
          <v-btn
            color="primary"
            variant="text"
            density="comfortable"
            size="small"
            @click="startEditingPageantInfo"
          >
            EDIT
          </v-btn>
        </v-card-title>
        <v-card-text>
          <div
            v-if="profileLoading"
            class="d-flex justify-center pa-4"
          >
            <v-progress-circular
              indeterminate
              color="primary"
            />
          </div>

          <div v-else>
            <v-list>
              <v-list-item>
                <v-list-item-title>Pageant Title</v-list-item-title>
                <v-list-item-subtitle>
                  {{ userProfile?.pageant_title || 'Not specified' }}
                </v-list-item-subtitle>
              </v-list-item>

              <v-list-item>
                <v-list-item-title>Current Events Keywords</v-list-item-title>
                <v-list-item-subtitle>
                  <div v-if="userProfile?.current_events && userProfile.current_events.length > 0">
                    <v-chip
                      v-for="(event, index) in userProfile.current_events"
                      :key="index"
                      class="ma-1"
                      size="small"
                      color="primary"
                      variant="tonal"
                    >
                      {{ event }}
                    </v-chip>
                  </div>
                  <div
                    v-else
                    class="text-medium-emphasis"
                  >
                    No current events specified
                  </div>
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>

            <v-alert
              v-if="!userProfile?.pageant_title && (!userProfile?.current_events || userProfile.current_events.length === 0)"
              type="info"
              icon="mdi-alert-circle-outline"
              class="mt-2"
              density="compact"
            >
              Adding pageant title and current events will help generate more relevant interview questions.
            </v-alert>
          </div>
        </v-card-text>
      </v-card>

      <!-- Resume Card -->
      <v-card
        variant="elevated"
        class="mb-4"
        elevation="3"
      >
        <v-card-title class="d-flex align-center">
          <span class="text-h6">Resume</span>
          <v-menu
            location="top"
            :close-on-content-click="true"
            max-width="300"
          >
            <template #activator="{ props }">
              <v-icon
                class="ml-2"
                color="primary"
                size="x-small"
                v-bind="props"
                icon="mdi-help-circle-outline"
              />
            </template>
            <v-card>
              <v-card-text>
                Your resume is used by our AI to generate personalized interview questions, similar to how a real pageant judge would prepare questions based on your background.
              </v-card-text>
            </v-card>
          </v-menu>
          <v-spacer />
          <v-btn
            color="primary"
            variant="text"
            density="comfortable"
            size="small"
            @click="startEditingResume"
          >
            EDIT
          </v-btn>
        </v-card-title>
        <v-card-text>
          <div
            v-if="profileLoading"
            class="d-flex justify-center pa-4"
          >
            <v-progress-circular
              indeterminate
              color="primary"
            />
          </div>

          <div
            v-else-if="userProfile?.resume"
            class="text-body-1 resume-content"
            style="height: 200px; overflow-y: auto;"
          >
            <p style="white-space: pre-line">
              {{ userProfile.resume }}
            </p>
          </div>

          <div
            v-else
            class="text-body-1 text-medium-emphasis"
          >
            <v-alert
              type="info"
              icon="mdi-alert-circle-outline"
              class="mb-2"
              density="compact"
            >
              Please add your resume to help generate better personalized interview questions.
            </v-alert>
            <p>No resume added yet. Click the edit button to add your resume.</p>
          </div>
        </v-card-text>
      </v-card>

      <!-- Social Impact Initiative Card -->
      <v-card
        variant="elevated"
        class="mb-4"
        elevation="3"
      >
        <v-card-title class="d-flex align-center">
          <span class="text-h6">Social Impact Initiative</span>
          <v-menu
            location="top"
            :close-on-content-click="true"
            max-width="300"
          >
            <template #activator="{ props }">
              <v-icon
                class="ml-2"
                color="primary"
                size="x-small"
                v-bind="props"
                icon="mdi-help-circle-outline"
              />
            </template>
            <v-card>
              <v-card-text>
                Your social impact initiative describes the cause you're passionate about and how you plan to make a difference. This helps our AI generate relevant questions about your platform.
              </v-card-text>
            </v-card>
          </v-menu>
          <v-spacer />
          <v-btn
            color="primary"
            variant="text"
            density="comfortable"
            size="small"
            @click="startEditingInitiative"
          >
            EDIT
          </v-btn>
        </v-card-title>
        <v-card-text>
          <div
            v-if="profileLoading"
            class="d-flex justify-center pa-4"
          >
            <v-progress-circular
              indeterminate
              color="primary"
            />
          </div>

          <div
            v-else-if="userProfile?.social_impact_initiative"
            class="text-body-1 initiative-content"
            style="height: 200px; overflow-y: auto;"
          >
            <p style="white-space: pre-line">
              {{ userProfile.social_impact_initiative }}
            </p>
          </div>

          <div
            v-else
            class="text-body-1 text-medium-emphasis"
          >
            <v-alert
              type="info"
              icon="mdi-alert-circle-outline"
              class="mb-2"
              density="compact"
            >
              Adding a social impact initiative will help generate more relevant questions about your platform.
            </v-alert>
            <p>No social impact initiative added yet. Click the edit button to add your initiative.</p>
          </div>
        </v-card-text>
      </v-card>

      <!-- Pageant Information editing dialog -->
      <v-dialog
        v-model="isEditingPageantInfo"
        max-width="600px"
      >
        <v-card>
          <v-card-title>Edit Pageant Information</v-card-title>
          <v-card-text>
            <v-text-field
              v-model="editedPageantInfo.title"
              label="Pageant Title"
              variant="outlined"
              placeholder="e.g., Miss Indiana University"
              class="mb-4"
            />

            <v-combobox
              v-model="editedPageantInfo.events"
              label="Current Events Keywords"
              variant="outlined"
              chips
              multiple
              closable-chips
              placeholder="Enter keywords and press Enter"
              hint="Add keywords related to current events relevant to your pageant. Press ENTER after each new keyword."
              persistent-hint
            />
          </v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="error"
              variant="text"
              @click="cancelEditingPageantInfo"
            >
              CANCEL
            </v-btn>
            <v-btn
              color="success"
              variant="text"
              :loading="savingPageantInfo"
              @click="savePageantInfo"
            >
              SAVE
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Resume editing dialog -->
      <v-dialog
        v-model="isEditingResume"
        max-width="600px"
      >
        <v-card>
          <v-card-title>Edit Resume</v-card-title>
          <v-card-text>
            <DocumentUploader
              label="Import from document"
              hint="Upload a PDF, Word Doc, or text file to extract your resume"
              :loading="processingResumeFile"
              @text-extracted="(text) => { editedResume = text; showSuccessMessage('Successfully extracted text from document!'); }"
              @error="showErrorMessage($event)"
              @processing="processingResumeFile = $event"
            />

            <v-textarea
              v-model="editedResume"
              variant="outlined"
              auto-grow
              rows="10"
              placeholder="Add your resume here..."
            />
          </v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="error"
              variant="text"
              @click="cancelEditingResume"
            >
              CANCEL
            </v-btn>
            <v-btn
              color="success"
              variant="text"
              :loading="savingResume"
              @click="saveResume"
            >
              SAVE
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Social Impact Initiative editing dialog -->
      <v-dialog
        v-model="isEditingInitiative"
        max-width="600px"
      >
        <v-card>
          <v-card-title>Edit Social Impact Initiative</v-card-title>
          <v-card-text>
            <DocumentUploader
              label="Import from document"
              hint="Upload a PDF, Word Doc, or text file to extract your social impact initiative"
              :loading="processingInitiativeFile"
              @text-extracted="(text) => { editedInitiative = text; showSuccessMessage('Successfully extracted text from document!'); }"
              @error="showErrorMessage($event)"
              @processing="processingInitiativeFile = $event"
            />

            <v-textarea
              v-model="editedInitiative"
              variant="outlined"
              auto-grow
              rows="10"
              placeholder="Add your social impact initiative here..."
            />
          </v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="error"
              variant="text"
              @click="cancelEditingInitiative"
            >
              CANCEL
            </v-btn>
            <v-btn
              color="success"
              variant="text"
              :loading="savingInitiative"
              @click="saveInitiative"
            >
              SAVE
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Snackbar for messages -->
      <v-snackbar
        v-model="showSnackbar"
        :color="snackbarColor"
        :timeout="3000"
        location="top"
      >
        {{ snackbarMessage }}

        <template #actions>
          <v-btn
            variant="text"
            icon="mdi-close"
            @click="showSnackbar = false"
          />
        </template>
      </v-snackbar>
    </div>
  
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { supabase } from '@/plugins/supabase'
import DocumentUploader from '@/components/DocumentUploader.vue'

const authStore = useAuthStore()

// User profile data
const userProfile = ref(null)
const profileLoading = ref(false)

// Resume editing
const isEditingResume = ref(false)
const editedResume = ref('')
const savingResume = ref(false)
const processingResumeFile = ref(false)

// Social Impact Initiative editing
const isEditingInitiative = ref(false)
const editedInitiative = ref('')
const savingInitiative = ref(false)
const processingInitiativeFile = ref(false)

// Pageant Information editing
const isEditingPageantInfo = ref(false)
const editedPageantInfo = ref({
  title: '',
  events: []
})
const savingPageantInfo = ref(false)

// Snackbar for messages
const showSnackbar = ref(false)
const snackbarMessage = ref('')
const snackbarColor = ref('success')

// Define route meta to require authentication
defineOptions({
  meta: {
    requiresAuth: true
  }
})

// Fetch user profile data
async function fetchUserProfile() {
  if (!authStore.user) return

  profileLoading.value = true

  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', authStore.user.id)
      .single()

    if (error) throw error

    userProfile.value = data
  } catch (err) {
    console.error('Error fetching user profile:', err)
  } finally {
    profileLoading.value = false
  }
}

// Profile editing functions
function startEditingResume() {
  editedResume.value = userProfile.value?.resume || ''
  isEditingResume.value = true
}

function cancelEditingResume() {
  isEditingResume.value = false
}

async function saveResume() {
  if (!authStore.user) return

  savingResume.value = true

  try {
    const { error } = await supabase
      .from('user_profiles')
      .update({ resume: editedResume.value })
      .eq('user_id', authStore.user.id)

    if (error) throw error

    // Update local state with new resume
    if (userProfile.value) {
      userProfile.value.resume = editedResume.value
    }

    isEditingResume.value = false
    showSuccessMessage('Resume updated successfully')
  } catch (err) {
    console.error('Error saving resume:', err)
    showErrorMessage('Failed to save resume. Please try again.')
  } finally {
    savingResume.value = false
  }
}

// Social Impact Initiative editing functions
function startEditingInitiative() {
  editedInitiative.value = userProfile.value?.social_impact_initiative || ''
  isEditingInitiative.value = true
}

function cancelEditingInitiative() {
  isEditingInitiative.value = false
}

async function saveInitiative() {
  if (!authStore.user) return

  savingInitiative.value = true

  try {
    const { error } = await supabase
      .from('user_profiles')
      .update({ social_impact_initiative: editedInitiative.value })
      .eq('user_id', authStore.user.id)

    if (error) throw error

    // Update local state with new initiative
    if (userProfile.value) {
      userProfile.value.social_impact_initiative = editedInitiative.value
    }

    isEditingInitiative.value = false
    showSuccessMessage('Social impact initiative updated successfully')
  } catch (err) {
    console.error('Error saving social impact initiative:', err)
    showErrorMessage('Failed to save social impact initiative. Please try again.')
  } finally {
    savingInitiative.value = false
  }
}

// Pageant Information editing functions
function startEditingPageantInfo() {
  editedPageantInfo.value = {
    title: userProfile.value?.pageant_title || '',
    events: userProfile.value?.current_events || []
  }
  isEditingPageantInfo.value = true
}

function cancelEditingPageantInfo() {
  isEditingPageantInfo.value = false
}

async function savePageantInfo() {
  if (!authStore.user) return

  savingPageantInfo.value = true

  try {
    const { error } = await supabase
      .from('user_profiles')
      .update({
        pageant_title: editedPageantInfo.value.title,
        current_events: editedPageantInfo.value.events
      })
      .eq('user_id', authStore.user.id)

    if (error) throw error

    // Update local state with new pageant information
    if (userProfile.value) {
      userProfile.value.pageant_title = editedPageantInfo.value.title
      userProfile.value.current_events = editedPageantInfo.value.events
    }

    isEditingPageantInfo.value = false
    showSuccessMessage('Pageant information updated successfully')
  } catch (err) {
    console.error('Error saving pageant information:', err)
    showErrorMessage('Failed to save pageant information. Please try again.')
  } finally {
    savingPageantInfo.value = false
  }
}

// Display error messages using the snackbar
function showErrorMessage(message) {
  snackbarMessage.value = message
  snackbarColor.value = 'error'
  showSnackbar.value = true
}

// Display success messages using the snackbar
function showSuccessMessage(message) {
  snackbarMessage.value = message
  snackbarColor.value = 'success'
  showSnackbar.value = true
}

// Fetch data on component mount
onMounted(async () => {
  await fetchUserProfile()
})
</script>

<style scoped>
.border-b {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}
</style>

<route>
{
  meta: {
    requiresAuth: true,
    layout: 'default'
  }
}
</route>
