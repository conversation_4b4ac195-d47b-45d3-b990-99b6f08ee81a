<template>
  <div class="messages-layout">
    <!-- Left Sidebar - Navigation Drawer with Rail Mode -->
    <v-navigation-drawer
      v-model="drawerOpen"
      :rail="rail"
      :permanent="!mobile"
      :temporary="mobile"
      app
      location="left"
      :width="240"
      :rail-width="56"
      :mobile-breakpoint="900"
      elevation="0"
      class="border-e d-none d-md-flex"
    >
      <!-- Header -->
      <v-list-item class="pa-3 border-b" :class="{ 'justify-center': rail }">
        <template #prepend>
          <v-btn
            icon
            variant="text"
            size="small"
            @click="rail = !rail"
            :color="rail ? 'primary' : 'default'"
          >
            <v-icon>{{ rail ? 'mdi-menu' : 'mdi-menu-open' }}</v-icon>
          </v-btn>
        </template>
        <v-list-item-title v-if="!rail" class="text-h6 ms-2">Messages</v-list-item-title>
        <template #append v-if="!rail">
          <v-btn
            icon="mdi-plus"
            size="small"
            variant="text"
            @click="showNewMessageInterface = true"
          />
        </template>
      </v-list-item>

      <!-- Categories -->
      <v-list density="compact" nav rounded>
        <!-- Home/Dashboard -->
        <v-tooltip :disabled="!rail" location="end">
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-bind="rail ? tooltipProps : {}"
              :active="selectedCategory === 'home'"
              @click="selectCategory('home')"
            >
              <template #prepend>
                <v-icon>mdi-home</v-icon>
              </template>
              <v-list-item-title>Home</v-list-item-title>
              <template #append v-if="!rail">
                <v-chip
                  v-if="messagesStore.unreadCounts.total > 0"
                  size="small"
                  color="primary"
                >
                  {{ messagesStore.unreadCounts.total }}
                </v-chip>
              </template>
            </v-list-item>
          </template>
          <span>Home</span>
        </v-tooltip>

        <!-- Pinned -->
        <v-tooltip :disabled="!rail || messagesStore.conversationsByCategory.pinned.length === 0" location="end">
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-if="messagesStore.conversationsByCategory.pinned.length > 0"
              v-bind="rail ? tooltipProps : {}"
              :active="selectedCategory === 'pinned'"
              @click="selectCategory('pinned')"
            >
              <template #prepend>
                <v-icon>mdi-pin</v-icon>
              </template>
              <v-list-item-title>Pinned</v-list-item-title>
            </v-list-item>
          </template>
          <span>Pinned</span>
        </v-tooltip>

        <!-- DMs -->
        <v-tooltip :disabled="!rail" location="end">
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-bind="rail ? tooltipProps : {}"
              :active="selectedCategory === 'dm'"
              @click="selectCategory('dm')"
            >
              <template #prepend>
                <v-icon>mdi-account-multiple</v-icon>
              </template>
              <v-list-item-title>Direct Messages</v-list-item-title>
              <template #append v-if="!rail">
                <v-chip
                  v-if="messagesStore.unreadCounts.dm > 0"
                  size="small"
                  color="primary"
                >
                  {{ messagesStore.unreadCounts.dm }}
                </v-chip>
              </template>
            </v-list-item>
          </template>
          <span>Direct Messages</span>
        </v-tooltip>

        <!-- Support -->
        <v-tooltip :disabled="!rail" location="end">
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-bind="rail ? tooltipProps : {}"
              :active="selectedCategory === 'support'"
              @click="selectCategory('support')"
            >
              <template #prepend>
                <v-icon>mdi-help-circle</v-icon>
              </template>
              <v-list-item-title>
                {{ messagesStore.isSupportStaff ? 'Support Tickets' : 'Support' }}
              </v-list-item-title>
              <template #append v-if="!rail">
                <v-chip
                  v-if="messagesStore.unreadCounts.support > 0"
                  size="small"
                  color="primary"
                >
                  {{ messagesStore.unreadCounts.support }}
                </v-chip>
              </template>
            </v-list-item>
          </template>
          <span>{{ messagesStore.isSupportStaff ? 'Support Tickets' : 'Support' }}</span>
        </v-tooltip>

        <!-- Communities -->
        <v-list-group v-model="opened" value="communities" v-if="!rail">
          <template #activator="{ props }">
            <v-list-item v-bind="props">
              <template #prepend>
                <v-icon>mdi-account-group</v-icon>
              </template>
              <v-list-item-title>Communities</v-list-item-title>
              <template #append>
                <v-chip
                  v-if="messagesStore.unreadCounts.community > 0"
                  size="small"
                  color="primary"
                >
                  {{ messagesStore.unreadCounts.community }}
                </v-chip>
              </template>
            </v-list-item>
          </template>

          <v-list-item
            v-for="community in messagesStore.communities"
            :key="community.id"
            :active="selectedCategory === `community-${community.id}`"
            @click="selectCategory(`community-${community.id}`)"
            class="ms-4"
          >
            <v-list-item-title>{{ community.name }}</v-list-item-title>
          </v-list-item>
        </v-list-group>

        <!-- Communities icon only when rail mode -->
        <v-tooltip :disabled="!rail" location="end">
          <template #activator="{ props: tooltipProps }">
            <v-list-item
              v-if="rail"
              v-bind="tooltipProps"
              :active="selectedCategory.startsWith('community-')"
              @click="rail = false; selectCategory('communities')"
            >
              <template #prepend>
                <v-icon>mdi-account-group</v-icon>
              </template>
              <v-list-item-title>Communities</v-list-item-title>
            </v-list-item>
          </template>
          <span>Communities</span>
        </v-tooltip>
      </v-list>
    </v-navigation-drawer>

    <!-- Main Content Area -->
    <div class="content-area">
      <div class="d-flex fill-height">
        <!-- Middle Column - Conversation List -->
        <div
          v-if="!mobile || !messagesStore.activeConversationId"
          class="conversation-list-column border-e"
          :class="{
            'flex-shrink-0': !mobile,
            'w-100': mobile
          }"
          :style="!mobile ? 'width: 320px;' : ''"
        >
          <v-card flat class="fill-height d-flex flex-column">
            <!-- Mobile Header with Menu and New Message -->
            <div v-if="mobile" class="pa-3 border-b flex-shrink-0 d-flex align-center">
              <v-btn
                icon="mdi-menu"
                variant="text"
                @click="showMobileMenu = true"
                class="me-2"
              />
              <h2 class="text-h6 flex-grow-1">Messages</h2>
              <v-btn
                icon="mdi-plus"
                variant="text"
                @click="showNewMessageInterface = true"
              />
            </div>

            <!-- Search -->
            <div class="pa-3 border-b flex-shrink-0">
              <v-text-field
                v-model="searchQuery"
                placeholder="Search conversations..."
                prepend-inner-icon="mdi-magnify"
                variant="outlined"
                density="compact"
                hide-details
              />
            </div>

            <!-- Show Closed Conversations Toggle (Support category only) -->
            <div v-if="selectedCategory === 'support' && messagesStore.isSupportStaff" class="pa-3 border-b flex-shrink-0">
              <v-switch
                v-model="messagesStore.showClosedConversations"
                color="primary"
                density="compact"
                hide-details
              >
                <template #label>
                  <span class="text-body-2">Show closed tickets</span>
                </template>
              </v-switch>
            </div>

            <!-- Conversation List -->
            <div class="flex-grow-1 overflow-y-auto align-self-start w-100">
              <v-list density="compact" nav class="pa-0">
                <v-list-item
                  v-for="conversation in filteredConversations"
                  :key="conversation.id"
                  :active="messagesStore.activeConversationId === conversation.id"
                  @click="selectConversation(conversation.id)"
                  class="mb-1"
                >
                  <template #prepend>
                    <UserAvatar
                      v-if="(conversation.type === 'support' && messagesStore.isSupportStaff) || conversation.type === 'dm'"
                      :user="getUserParticipant(conversation)?.user"
                      size="32"
                    />
                    <!-- Show icons for community and other types -->
                    <v-avatar v-else size="32">
                      <v-icon v-if="conversation.type === 'community'">
                        mdi-account-group
                      </v-icon>
                      <v-icon v-else-if="conversation.type === 'support'">
                        mdi-help-circle
                      </v-icon>
                      <v-icon v-else>
                        mdi-account
                      </v-icon>
                    </v-avatar>
                  </template>

                  <v-list-item-title class="text-body-2">
                    {{ getConversationTitle(conversation) }}
                  </v-list-item-title>

                  <v-list-item-subtitle class="text-caption">
                    {{ getLastMessagePreview(conversation) }}
                  </v-list-item-subtitle>

                  <template #append>
                    <div class="d-flex flex-column align-end">
                      <v-btn
                        icon="mdi-pin"
                        size="x-small"
                        variant="text"
                        :color="conversation.is_pinned ? 'primary' : 'grey'"
                        @click.stop="messagesStore.togglePin(conversation.id)"
                      />
                      <span class="text-caption text-grey">
                        {{ formatTime(conversation.updated_at) }}
                      </span>
                    </div>
                  </template>
                </v-list-item>
              </v-list>

              <!-- New Support Request Button for regular users -->
              <div
                v-if="selectedCategory === 'support' && !messagesStore.isSupportStaff"
                class="pa-3 border-t"
              >
                <v-btn
                  color="primary"
                  variant="outlined"
                  block
                  @click="createNewSupportRequest"
                  prepend-icon="mdi-plus"
                >
                  New Support Request
                </v-btn>
              </div>
            </div>
          </v-card>
        </div>

        <!-- Right Column - Chat Area -->
        <div
          v-if="!mobile || messagesStore.activeConversationId"
          class="chat-area-column flex-grow-1"
        >
          <v-card flat class="fill-height d-flex flex-column">
            <!-- Chat Header -->
            <v-card-title
              v-if="messagesStore.activeConversation"
              class="d-flex align-center justify-space-between pa-4 border-b flex-shrink-0"
            >
              <div class="d-flex align-center">
                <!-- Mobile Back Button -->
                <v-btn
                  v-if="mobile"
                  icon="mdi-arrow-left"
                  variant="text"
                  @click="goBackToConversationList"
                  class="me-2"
                />

                <UserAvatar
                  v-if="(messagesStore.activeConversation.type === 'support' && messagesStore.isSupportStaff) || messagesStore.activeConversation.type === 'dm'"
                  :user="getUserParticipant(messagesStore.activeConversation)?.user"
                  size="32"
                  class="me-3"
                />
                <!-- Show icons for community and other types -->
                <v-avatar v-else size="32" class="me-3">
                  <v-icon v-if="messagesStore.activeConversation.type === 'community'">
                    mdi-account-group
                  </v-icon>
                  <v-icon v-else-if="messagesStore.activeConversation.type === 'support'">
                    mdi-help-circle
                  </v-icon>
                  <v-icon v-else>
                    mdi-account
                  </v-icon>
                </v-avatar>
                <div>
                  <div class="text-h6">
                    {{ getConversationTitle(messagesStore.activeConversation) }}
                  </div>
                  <div class="text-caption text-grey">
                    {{ getConversationSubtitle(messagesStore.activeConversation) }}
                  </div>
                </div>
              </div>

              <div class="d-flex align-center">
                <!-- Relationship Status Actions for DMs -->
                <template v-if="messagesStore.activeConversation.type === 'dm'">
                  <!-- Show "Pending" status for conversation initiator -->
                  <v-chip
                    v-if="getRelationshipStatusDisplay() === 'pending'"
                    color="warning"
                    variant="outlined"
                    size="small"
                    class="me-2"
                  >
                    Pending
                  </v-chip>

                  <!-- Show Accept/Block buttons for conversation recipient -->
                  <template v-if="getRelationshipStatusDisplay() === 'received'">
                    <v-btn
                      color="success"
                      variant="text"
                      @click="acceptDM"
                      class="me-2"
                    >
                      Accept
                    </v-btn>
                    <v-btn
                      color="error"
                      variant="text"
                      @click="blockDM"
                    >
                      Block
                    </v-btn>
                  </template>
                </template>

                <!-- Three-dot menu -->
                <v-menu>
                  <template #activator="{ props }">
                    <v-btn
                      icon="mdi-dots-vertical"
                      variant="text"
                      v-bind="props"
                    />
                  </template>
                  <v-list>
                    <!-- Admin Actions for Support Tickets -->
                    <template v-if="messagesStore.activeConversation?.type === 'support' && messagesStore.isSupportStaff">
                      <v-list-item v-if="messagesStore.activeConversation.status !== 'closed'" @click="closeTicket">
                        <template #prepend>
                          <v-icon>mdi-close-circle</v-icon>
                        </template>
                        <v-list-item-title>Close Ticket</v-list-item-title>
                      </v-list-item>
                      <v-list-item v-else @click="reopenTicket">
                        <template #prepend>
                          <v-icon>mdi-refresh</v-icon>
                        </template>
                        <v-list-item-title>Reopen Ticket</v-list-item-title>
                      </v-list-item>
                      <v-divider />
                    </template>

                    <v-list-item @click="messagesStore.togglePin(messagesStore.activeConversationId)">
                      <template #prepend>
                        <v-icon>{{ messagesStore.activeConversation?.is_pinned ? 'mdi-pin-off' : 'mdi-pin' }}</v-icon>
                      </template>
                      <v-list-item-title>
                        {{ messagesStore.activeConversation?.is_pinned ? 'Unpin' : 'Pin' }} Conversation
                      </v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </v-card-title>

            <!-- Home Dashboard -->
            <div v-if="selectedCategory === 'home'" class="pa-6 flex-grow-1 overflow-y-auto">
              <v-row>
                <v-col cols="12" md="6">
                  <v-card>
                    <v-card-title>Message Activity</v-card-title>
                    <v-card-text>
                      <v-list>
                        <v-list-item>
                          <v-list-item-title>Total Conversations</v-list-item-title>
                          <template #append>
                            <v-chip>{{ messagesStore.conversations.length }}</v-chip>
                          </template>
                        </v-list-item>
                        <v-list-item>
                          <v-list-item-title>Unread Messages</v-list-item-title>
                          <template #append>
                            <v-chip color="primary">{{ messagesStore.unreadCounts.total }}</v-chip>
                          </template>
                        </v-list-item>
                        <v-list-item>
                          <v-list-item-title>Active Communities</v-list-item-title>
                          <template #append>
                            <v-chip>{{ messagesStore.communities.length }}</v-chip>
                          </template>
                        </v-list-item>
                      </v-list>
                    </v-card-text>
                  </v-card>
                </v-col>

                <v-col cols="12" md="6">
                  <v-card>
                    <v-card-title>Recent Activity</v-card-title>
                    <v-card-text>
                      <v-list>
                        <v-list-item
                          v-for="conversation in messagesStore.conversations.slice(0, 5)"
                          :key="conversation.id"
                          @click="selectConversation(conversation.id)"
                        >
                          <v-list-item-title>{{ getConversationTitle(conversation) }}</v-list-item-title>
                          <v-list-item-subtitle>{{ getLastMessagePreview(conversation) }}</v-list-item-subtitle>
                        </v-list-item>
                      </v-list>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </div>

            <!-- Messages Area -->
            <div
              v-else-if="messagesStore.activeConversation"
              class="flex-grow-1 overflow-y-auto pa-4"
              ref="messagesContainer"
            >
              <div
                v-for="message in messagesStore.activeMessages"
                :key="message.id"
                class="mb-4"
              >
                <div
                  :class="[
                    'd-flex',
                    message.sender_id === authStore.user?.id ? 'justify-end' : 'justify-start'
                  ]"
                >
                  <div
                    :class="[
                      'message-bubble pa-3 rounded-lg',
                      message.sender_id === authStore.user?.id
                        ? 'bg-primary text-white'
                        : 'bg-grey-lighten-4'
                    ]"
                    style="max-width: 70%;"
                  >
                    <div
                      v-if="message.sender_id !== authStore.user?.id"
                      class="text-caption font-weight-bold mb-1"
                    >
                      {{ getSenderName(message) }}
                    </div>
                    <div class="text-body-2">{{ message.content }}</div>
                    <div
                      :class="[
                        'text-caption mt-1',
                        message.sender_id === authStore.user?.id
                          ? 'text-white'
                          : 'text-grey'
                      ]"
                    >
                      {{ formatTime(message.created_at) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div
              v-else-if="selectedCategory !== 'home' && !showNewMessageInterface"
              class="flex-grow-1 d-flex align-center justify-center"
            >
              <div class="text-center">
                <v-icon
                  size="64"
                  color="grey-lighten-2"
                  :icon="selectedCategory === 'support' ? 'mdi-help-circle' : 'mdi-message-outline'"
                />
                <div class="text-h6 mt-4 text-grey">
                  {{ getEmptyStateTitle() }}
                </div>
                <div class="text-body-2 text-grey mt-2">
                  {{ getEmptyStateSubtitle() }}
                </div>
                <!-- Support-specific action button for regular users -->
                <v-btn
                  v-if="selectedCategory === 'support' && !messagesStore.isSupportStaff"
                  color="primary"
                  class="mt-4"
                  @click="createNewSupportRequest"
                >
                  Contact Support
                </v-btn>
              </div>
            </div>

            <!-- New Message Interface -->
            <div
              v-else-if="showNewMessageInterface"
              class="flex-grow-1"
            >
              <NewMessageInterface
                :message-type="getNewMessageType()"
                @close="showNewMessageInterface = false"
                @conversation-created="handleConversationCreated"
              />
            </div>

            <!-- Message Input - At bottom of chat area only -->
            <div
              v-if="messagesStore.activeConversation"
              class="border-t flex-shrink-0"
            >
              <!-- Message Input -->
              <div
                v-if="canSendMessages()"
                class="pa-4"
              >
                <div class="d-flex align-center">
                  <v-text-field
                    v-model="newMessage"
                    placeholder="Type a message..."
                    variant="outlined"
                    density="compact"
                    hide-details
                    @keyup.enter="sendMessage"
                    class="flex-grow-1 me-2"
                  />
                  <v-btn
                    icon="mdi-send"
                    color="primary"
                    :disabled="!newMessage.trim()"
                    @click="sendMessage"
                  />
                </div>
              </div>

              <!-- Disabled Message Input State -->
              <div
                v-else
                class="pa-4"
              >
                <v-alert
                  type="info"
                  variant="tonal"
                  :text="getMessageInputDisabledReason()"
                />
              </div>
            </div>
          </v-card>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Menu Bottom Sheet -->
  <v-bottom-sheet v-model="showMobileMenu" v-if="mobile">
    <v-card>
      <v-card-title class="d-flex align-center justify-space-between">
        <span>Menu</span>
        <v-btn icon="mdi-close" variant="text" @click="showMobileMenu = false" />
      </v-card-title>
      <v-card-text>
        <v-list>
          <!-- Home/Dashboard -->
          <v-list-item
            :active="selectedCategory === 'home'"
            @click="selectCategory('home'); showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-home</v-icon>
            </template>
            <v-list-item-title>Home</v-list-item-title>
            <template #append>
              <v-chip
                v-if="messagesStore.unreadCounts.total > 0"
                size="small"
                color="primary"
              >
                {{ messagesStore.unreadCounts.total }}
              </v-chip>
            </template>
          </v-list-item>

          <!-- Pinned -->
          <v-list-item
            v-if="messagesStore.conversationsByCategory.pinned.length > 0"
            :active="selectedCategory === 'pinned'"
            @click="selectCategory('pinned'); showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-pin</v-icon>
            </template>
            <v-list-item-title>Pinned</v-list-item-title>
          </v-list-item>

          <!-- DMs -->
          <v-list-item
            :active="selectedCategory === 'dm'"
            @click="selectCategory('dm'); showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-account-multiple</v-icon>
            </template>
            <v-list-item-title>Direct Messages</v-list-item-title>
            <template #append>
              <v-chip
                v-if="messagesStore.unreadCounts.dm > 0"
                size="small"
                color="primary"
              >
                {{ messagesStore.unreadCounts.dm }}
              </v-chip>
            </template>
          </v-list-item>

          <!-- Support -->
          <v-list-item
            :active="selectedCategory === 'support'"
            @click="selectCategory('support'); showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-help-circle</v-icon>
            </template>
            <v-list-item-title>
              {{ messagesStore.isSupportStaff ? 'Support Tickets' : 'Support' }}
            </v-list-item-title>
            <template #append>
              <v-chip
                v-if="messagesStore.unreadCounts.support > 0"
                size="small"
                color="primary"
              >
                {{ messagesStore.unreadCounts.support }}
              </v-chip>
            </template>
          </v-list-item>

          <!-- Communities -->
          <v-list-item
            v-for="community in messagesStore.communities"
            :key="community.id"
            :active="selectedCategory === `community-${community.id}`"
            @click="selectCategory(`community-${community.id}`); showMobileMenu = false"
          >
            <template #prepend>
              <v-icon>mdi-account-group</v-icon>
            </template>
            <v-list-item-title>{{ community.name }}</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-card-text>
    </v-card>
  </v-bottom-sheet>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useDisplay } from 'vuetify'
import { useMessagesStore } from '@/stores/messages'
import { useNotificationsStore } from '@/stores/notifications'
import { useAuthStore } from '@/stores/auth'
import NewMessageInterface from '@/components/NewMessageInterface.vue'
import UserAvatar from '@/components/UserAvatar.vue'

// Stores
const messagesStore = useMessagesStore()
const notificationsStore = useNotificationsStore()
const authStore = useAuthStore()

// Display composable for responsive design
const { mobile } = useDisplay({ mobileBreakpoint: 'md' })

// State
const selectedCategory = ref('home')
const searchQuery = ref('')
const newMessage = ref('')
const showNewMessageInterface = ref(false)
const showMobileMenu = ref(false)
const messagesContainer = ref(null)
const drawerOpen = ref(true)
const rail = ref(false)
const _opened = ref([])

// Computed
const opened = computed({
  get: () => rail.value ? [] : _opened.value,
  set: val => { _opened.value = val }
})

const filteredConversations = computed(() => {
  let conversations = []

  if (selectedCategory.value === 'pinned') {
    conversations = messagesStore.conversationsByCategory.pinned
  } else if (selectedCategory.value === 'dm') {
    conversations = messagesStore.conversationsByCategory.dm
  } else if (selectedCategory.value === 'support') {
    conversations = messagesStore.conversationsByCategory.support
  } else if (selectedCategory.value.startsWith('community-')) {
    const communityId = selectedCategory.value.replace('community-', '')
    conversations = messagesStore.conversationsByCategory.community.filter(
      c => c.community_id === communityId
    )
  } else {
    conversations = messagesStore.conversations
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    conversations = conversations.filter(c =>
      getConversationTitle(c).toLowerCase().includes(query) ||
      getLastMessagePreview(c).toLowerCase().includes(query)
    )
  }

  return conversations
})

// Methods
function selectCategory(category) {
  // Clear active conversation when switching categories
  if (selectedCategory.value !== category) {
    messagesStore.setActiveConversation(null)
    showNewMessageInterface.value = false
  }
  selectedCategory.value = category
}

// Get user participant from a conversation (for avatar display)
function getUserParticipant(conversation) {
  if (!conversation.participants) return null

  if (messagesStore.isSupportStaff && conversation.type === 'support') {
    // For staff in support conversations, get the user (non-staff) participant
    return conversation.participants.find(p => p.user?.role === 'user')
  } else {
    // For DMs or regular users, get the other participant
    return conversation.participants.find(
      p => p.user_id !== authStore.user?.id
    )
  }
}

function getConversationTitle(conversation) {
  // Handle pending conversation
  if (conversation.id === 'pending') {
    return 'New Support Request'
  }

  if (conversation.title) return conversation.title

  if (conversation.type === 'community') {
    return conversation.community?.name || 'Community Chat'
  }

  if (conversation.type === 'support') {
    // For support staff, show the user's name who created the support request
    if (messagesStore.isSupportStaff) {
      const userParticipant = conversation.participants?.find(
        p => p.user?.role === 'user'
      )
      if (userParticipant?.user) {
        const userName = userParticipant.user.first_name
          ? `${userParticipant.user.first_name} ${userParticipant.user.last_name || ''}`.trim()
          : userParticipant.user.email
        return `Support: ${userName}`
      }
      return 'Support Request'
    }
    // For regular users, just show "Support Request"
    return 'Support Request'
  }

  // For DMs, show other participant's name
  const otherParticipant = conversation.participants?.find(
    p => p.user_id !== authStore.user?.id
  )
  return otherParticipant?.user?.first_name
    ? `${otherParticipant.user.first_name} ${otherParticipant.user.last_name || ''}`.trim()
    : otherParticipant?.user?.email || 'Direct Message'
}

function getConversationSubtitle(conversation) {
  // Handle pending conversation
  if (conversation.id === 'pending') {
    return 'Start typing to create your support request'
  }

  if (conversation.type === 'community') {
    return `${conversation.participants?.length || 0} members`
  }

  if (conversation.type === 'support') {
    if (messagesStore.isSupportStaff) {
      // For support staff, show the user's email
      const userParticipant = conversation.participants?.find(
        p => p.user?.role === 'user'
      )
      return userParticipant?.user?.email || 'Support conversation'
    }
    // For regular users, show that it's a support conversation
    return 'Support conversation'
  }

  const otherParticipant = conversation.participants?.find(
    p => p.user_id !== authStore.user?.id
  )
  return otherParticipant?.user?.email || ''
}

function getLastMessagePreview(conversation) {
  // Handle pending conversation
  if (conversation.id === 'pending') {
    return 'Start typing your message...'
  }

  const lastMessage = conversation.last_message?.[0]
  if (!lastMessage) return 'No messages yet'

  return lastMessage.content.length > 50
    ? `${lastMessage.content.substring(0, 50)}...`
    : lastMessage.content
}

function getSenderName(message) {
  if (!message.sender) return 'Unknown'

  return message.sender.first_name
    ? `${message.sender.first_name} ${message.sender.last_name || ''}`.trim()
    : message.sender.email
}

function getCurrentUserParticipant() {
  return messagesStore.activeConversation?.participants?.find(
    p => p.user_id === authStore.user?.id
  )
}

function didCurrentUserInitiateConversation() {
  if (!messagesStore.activeConversation || !messagesStore.activeMessages.length) {
    return false
  }

  // Check if the current user sent the first message
  const firstMessage = messagesStore.activeMessages[0]
  return firstMessage?.sender_id === authStore.user?.id
}

function getRelationshipStatusDisplay() {
  const currentParticipant = getCurrentUserParticipant()
  if (!currentParticipant || currentParticipant.relationship_status !== 'pending') {
    return null
  }

  // If current user initiated the conversation, show "Pending" status
  if (didCurrentUserInitiateConversation()) {
    return 'pending'
  }

  // If current user received the conversation request, show Accept/Block buttons
  return 'received'
}

function formatTime(timestamp) {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  const now = new Date()
  const diffInHours = (now - date) / (1000 * 60 * 60)

  if (diffInHours < 24) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }
  if (diffInHours < 168) { // 7 days
    return date.toLocaleDateString([], { weekday: 'short' })
  }
  return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
}

async function selectConversation(conversationId) {
  // Set the active conversation (this will handle subscription internally)
  messagesStore.setActiveConversation(conversationId)

  // Scroll to bottom after messages load
  await nextTick()
  scrollToBottom()
}

function goBackToConversationList() {
  messagesStore.setActiveConversation(null)
}

async function sendMessage() {
  if (!newMessage.value.trim() || !messagesStore.activeConversationId) return

  try {
    await messagesStore.sendMessage(
      messagesStore.activeConversationId,
      newMessage.value.trim()
    )
    newMessage.value = ''

    // Scroll to bottom after sending
    await nextTick()
    scrollToBottom()
  } catch (error) {
    console.error('Failed to send message:', error)
  }
}

async function acceptDM() {
  if (!messagesStore.activeConversationId) return

  try {
    await messagesStore.updateRelationshipStatus(
      messagesStore.activeConversationId,
      'accepted'
    )
  } catch (error) {
    console.error('Failed to accept DM:', error)
  }
}

async function blockDM() {
  if (!messagesStore.activeConversationId) return

  try {
    await messagesStore.updateRelationshipStatus(messagesStore.activeConversationId, 'blocked')
    console.log('DM blocked successfully')
  } catch (error) {
    console.error('Failed to block DM:', error)
  }
}

function handleConversationCreated(conversation) {
  // Close the new message interface
  showNewMessageInterface.value = false

  // Switch to the appropriate category if needed
  if (conversation.conversation_type === 'support') {
    selectedCategory.value = 'support'
  } else {
    selectedCategory.value = 'dm'
  }

  // Select the newly created conversation and ensure proper subscription
  if (conversation.id) {
    // Use nextTick to ensure the conversation is properly set before subscribing
    nextTick(() => {
      selectConversation(conversation.id)
    })
  }
}

function scrollToBottom() {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// Watch for new messages to auto-scroll
watch(
  () => messagesStore.activeMessages.length,
  () => {
    nextTick(() => scrollToBottom())
  }
)

function canSendMessages() {
  if (!messagesStore.activeConversation) return false

  // Support and community conversations are always allowed
  if (messagesStore.activeConversation.type === 'support' ||
      messagesStore.activeConversation.type === 'community') {
    return true
  }

  // For DM conversations, check relationship status
  const currentParticipant = getCurrentUserParticipant()
  return currentParticipant && currentParticipant.relationship_status === 'accepted'
}

function getMessageInputDisabledReason() {
  if (!messagesStore.activeConversation) return 'No conversation selected'

  const currentParticipant = getCurrentUserParticipant()
  if (!currentParticipant) return 'You are not a participant in this conversation'

  const status = currentParticipant.relationship_status
  if (status === 'pending') {
    if (didCurrentUserInitiateConversation()) {
      return 'Waiting for the other person to accept your message request'
    }
    return 'Please accept this message request to start chatting'
  }

  if (status === 'blocked') {
    return 'This conversation has been blocked'
  }

  return 'You cannot send messages in this conversation'
}

function getNewMessageType() {
  if (selectedCategory.value === 'dm') {
    return 'dm'
  }
  if (selectedCategory.value === 'support') {
    return 'support'
  }
  return null
}

function getEmptyStateTitle() {
  if (selectedCategory.value === 'support') {
    return 'No support conversations yet'
  }
  return 'No conversations yet'
}

function getEmptyStateSubtitle() {
  if (selectedCategory.value === 'support') {
    return 'Start a new support conversation'
  }
  return 'Start a new conversation'
}

function createNewSupportRequest() {
  try {
    // Create a pending support conversation and set it as active
    messagesStore.createPendingSupportConversation()

    // Switch to support category and select the pending conversation
    selectedCategory.value = 'support'
    selectConversation('pending')
  } catch (error) {
    console.error('Failed to create new support request:', error)
  }
}

async function closeTicket() {
  if (!messagesStore.activeConversationId || !messagesStore.isSupportStaff) return

  try {
    await messagesStore.updateConversationStatus(messagesStore.activeConversationId, 'closed')
    console.log('Ticket closed successfully')
  } catch (error) {
    console.error('Failed to close ticket:', error)
  }
}

async function reopenTicket() {
  if (!messagesStore.activeConversationId || !messagesStore.isSupportStaff) return

  try {
    await messagesStore.updateConversationStatus(messagesStore.activeConversationId, 'open')
    console.log('Ticket reopened successfully')
  } catch (error) {
    console.error('Failed to reopen ticket:', error)
  }
}

// Lifecycle
onMounted(() => {
  // Initialize with enhanced conversation fetching for support staff
  if (authStore.user) {
    messagesStore.fetchCommunities()
    messagesStore.fetchConversationsEnhanced()
    messagesStore.subscribeToConversations()
  }
  notificationsStore.initialize()

  // Add keyboard shortcut for toggling rail mode
  const handleKeydown = (event) => {
    if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
      event.preventDefault()
      rail.value = !rail.value
    }
  }

  document.addEventListener('keydown', handleKeydown)

  // Cleanup function will be called in onUnmounted
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })
})

onUnmounted(() => {
  messagesStore.unsubscribeAll()
})
</script>

<style scoped>
.message-bubble {
  word-wrap: break-word;
}

.border-e {
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.border-b {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.border-t {
  border-top: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.messages-layout {
  height: calc(100vh - 110px);
  position: relative;
  overflow: hidden;
}

@media (max-width: 599px) {
  .messages-layout {
    height: calc(100vh - 64px); /* Only app bar height on mobile */
  }
}

.content-area {
  height: 100%;
  position: relative;
}
</style>
