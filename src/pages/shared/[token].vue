<template>
  <v-container
    class="fill-height pa-4 pb-16 interview-container"
    bg-color="background"
  >
    <!-- Invisible div that indicates document is fully loaded -->
    <div
      v-if="!loading && interview"
      id="document-loaded"
      style="display: none;"
    />

    <v-card
      class="interview-card"
      bg-color="surface"
    >
      <v-card-title class="text-h5 mb-4 d-flex flex-column">
        <div class="d-flex align-center justify-space-between w-100">
          <div class="d-flex align-center flex-column align-start">
            <div class="title-container mb-1">
              <h2 class="text-h4">
                {{ interview?.title || 'Untitled Interview' }}
              </h2>
            </div>
            <span
              v-if="interview"
              class="text-caption text-grey text-start w-100 ml-5"
            >
              {{ formatDate(interview.created_at) }}
            </span>
          </div>
        </div>
      </v-card-title>

      <v-card-text>
        <!-- Loading state -->
        <div
          v-if="loading"
          class="d-flex justify-center align-center my-4"
        >
          <v-progress-circular
            indeterminate
            color="primary"
          />
        </div>

        <!-- Error state -->
        <v-alert
          v-if="error"
          type="error"
          class="mb-4"
        >
          {{ error }}
        </v-alert>

        <template v-if="!loading && interview">
          <!-- Audio player always visible at the top -->
          <div class="mb-6">
            <h3 class="text-h6 mb-3">
              Recording
            </h3>
            <!-- Standard audio player for non-self-destruct view -->
            <template v-if="!isSelfDestruct && recordingUrl">
              <audio
                ref="audioPlayer"
                controls
                class="w-100"
                :src="recordingUrl"
                @timeupdate="onTimeUpdate"
              >
                Your browser does not support the audio element.
              </audio>
            </template>

            <!-- Download button for self-destruct view -->
            <template v-else-if="isSelfDestruct && recordingUrl">
              <v-sheet
                rounded
                elevation="1"
                class="download-section pa-6 mb-4 text-center"
                color="surface-variant"
              >
                <v-row
                  align="center"
                  justify="center"
                >
                  <v-col
                    cols="12"
                    class="d-flex flex-column align-center"
                  >
                    <div class="d-flex align-center mb-4">
                      <v-icon
                        icon="mdi-download-circle"
                        color="primary"
                        size="28"
                        class="mr-2"
                      />
                      <h3 class="text-h5 text-primary">
                        Interview Recording Available
                      </h3>
                    </div>
                    <p
                      class="mb-4 text-high-emphasis text-center mx-auto"
                      style="max-width: 600px;"
                    >
                      This shared recording is only available for a limited time.
                      Download now to keep a permanent copy for your records.
                    </p>
                    <v-btn
                      color="primary"
                      :href="recordingUrl"
                      target="_blank"
                      download
                      prepend-icon="mdi-download"
                      class="download-btn px-6"
                      size="large"
                      variant="elevated"
                    >
                      Download Recording
                    </v-btn>
                  </v-col>
                </v-row>
              </v-sheet>
            </template>

            <!-- No recording available -->
            <v-card
              v-else
              variant="tonal"
              class="pa-3 text-center"
              bg-color="surface"
            >
              <p>
                No recording available for this interview.
              </p>
            </v-card>
          </div>

          <!-- Tabs for Transcript and Analysis - only shown when not self_destruct -->
          <template v-if="!isSelfDestruct">
            <v-tabs
              v-model="activeTab"
              bg-color="background"
              slider-color="primary"
              class="mb-4"
            >
              <v-tab
                value="transcript"
                class="text-body-1"
              >
                Transcript
              </v-tab>
              <v-tab
                value="analysis"
                class="text-body-1"
              >
                Analysis
              </v-tab>
            </v-tabs>

            <!-- Tabbed Content Display -->
            <v-window v-model="activeTab">
              <!-- Transcript Tab -->
              <v-window-item value="transcript">
                <!-- Transcript section -->
                <div class="mb-6">
                  <v-timeline
                    v-if="messages.length"
                    density="compact"
                    align="start"
                  >
                    <v-timeline-item
                      v-for="(message, index) in messages"
                      :key="index"
                      :dot-color="message.role === 'bot' ? 'primary' : 'secondary'"
                      :icon="message.role === 'bot' ? 'mdi-robot' : 'mdi-account'"
                      :size="'small'"
                      :class="{ 'active-message': isActiveMessage(message) }"
                    >
                      <template #opposite>
                        <span class="text-caption">{{ formatSeconds(message.secondsFromStart) }}</span>
                      </template>
                      <v-card
                        variant="tonal"
                        :class="[
                          'pa-3',
                          message.role === 'bot' ? 'bg-primary-lighten-5' : 'user-message-card',
                          isActiveMessage(message) ? 'active-message-card' : '',
                          'clickable-card'
                        ]"
                        @click="seekToMessage(message)"
                      >
                        <div class="d-flex align-center mb-1 position-relative">
                          <span
                            class="font-weight-bold"
                            :class="{ 'user-name': message.role === 'user' }"
                          >{{ message.role === 'bot' ? 'Ai Interviewer' : 'Candidate' }}</span>
                          <v-btn
                            v-if="isActiveMessage(message)"
                            icon
                            density="compact"
                            size="small"
                            :color="message.role === 'bot' ? 'primary' : 'secondary'"
                            class="playback-toggle-btn mr-1"
                            elevation="0"
                            @click.stop="togglePlayback"
                          >
                            <v-icon>{{ audioPlayer?.paused ? 'mdi-play' : 'mdi-pause' }}</v-icon>
                          </v-btn>
                        </div>
                        <p :class="{ 'user-message-text': message.role === 'user' }">
                          {{ message.message }}
                        </p>
                      </v-card>
                    </v-timeline-item>
                  </v-timeline>
                  <!-- Loading state for transcript -->
                  <v-card
                    v-else
                    variant="tonal"
                    class="pa-3 text-center"
                    bg-color="surface"
                  >
                    <p>
                      No transcript available for this interview.
                    </p>
                  </v-card>
                </div>
              </v-window-item>

              <!-- Analysis Tab -->
              <v-window-item
                value="analysis"
                class="page-break-before"
              >
                <v-row>
                  <!-- Left column: Feedback -->
                  <v-col
                    cols="12"
                    md="6"
                  >
                    <FeedbackAnalysis
                      :analysis="interview?.analysis"
                      :loading="false"
                    />
                  </v-col>

                  <!-- Right column: Filler Words Analysis -->
                  <v-col
                    cols="12"
                    md="6"
                    class="page-break-before"
                  >
                    <FillerWordsAnalysis
                      :analysis="interview?.analysis"
                      :loading="false"
                    />
                  </v-col>
                </v-row>
              </v-window-item>
            </v-window>
          </template>

          <!-- Self-destruct view: Everything on one page -->
          <template v-else>
            <!-- First section: Transcript -->
            <div class="mb-6">
              <h3 class="text-h6 mb-3">
                Transcript
              </h3>
              <v-timeline
                v-if="messages.length"
                density="compact"
                align="start"
              >
                <v-timeline-item
                  v-for="(message, index) in messages"
                  :key="index"
                  :dot-color="message.role === 'bot' ? 'primary' : 'secondary'"
                  :icon="message.role === 'bot' ? 'mdi-robot' : 'mdi-account'"
                  :size="'small'"
                  :class="{ 'active-message': isActiveMessage(message) }"
                >
                  <template #opposite>
                    <span class="text-caption">{{ formatSeconds(message.secondsFromStart) }}</span>
                  </template>
                  <v-card
                    variant="tonal"
                    :class="[
                      'pa-3',
                      message.role === 'bot' ? 'bg-primary-lighten-5' : 'user-message-card',
                      isActiveMessage(message) ? 'active-message-card' : '',
                      'clickable-card'
                    ]"
                    @click="seekToMessage(message)"
                  >
                    <div class="d-flex align-center mb-1 position-relative">
                      <span
                        class="font-weight-bold"
                        :class="{ 'user-name': message.role === 'user' }"
                      >{{ message.role === 'bot' ? 'Ai Interviewer' : 'Candidate' }}</span>
                      <v-btn
                        v-if="isActiveMessage(message)"
                        icon
                        density="compact"
                        size="small"
                        :color="message.role === 'bot' ? 'primary' : 'secondary'"
                        class="playback-toggle-btn mr-1"
                        elevation="0"
                        @click.stop="togglePlayback"
                      >
                        <v-icon>{{ audioPlayer?.paused ? 'mdi-play' : 'mdi-pause' }}</v-icon>
                      </v-btn>
                    </div>
                    <p :class="{ 'user-message-text': message.role === 'user' }">
                      {{ message.message }}
                    </p>
                  </v-card>
                </v-timeline-item>
              </v-timeline>
              <!-- Loading state for transcript -->
              <v-card
                v-else
                variant="tonal"
                class="pa-3 text-center"
                bg-color="surface"
              >
                <p>
                  No transcript available for this interview.
                </p>
              </v-card>
            </div>

            <!-- Second section: Analysis -->
            <div class="mt-8 page-break-before">
              <h3 class="text-h6 mb-3">
                Analysis
              </h3>
              <v-row>
                <!-- Left column: Feedback -->
                <v-col
                  cols="12"
                  md="6"
                >
                  <FeedbackAnalysis
                    :analysis="interview?.analysis"
                    :loading="false"
                  />
                </v-col>

                <!-- Right column: Filler Words Analysis -->
                <v-col
                  cols="12"
                  md="6"
                  class="page-break-before"
                >
                  <FillerWordsAnalysis
                    :analysis="interview?.analysis"
                    :loading="false"
                  />
                </v-col>
              </v-row>
            </div>
          </template>
        </template>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { supabase } from '@/plugins/supabase'
import FillerWordsAnalysis from '@/components/FillerWordsAnalysis.vue'
import FeedbackAnalysis from '@/components/FeedbackAnalysis.vue'
import { useThemeStore } from '@/stores/theme'

const route = useRoute()
const themeStore = useThemeStore()
const interview = ref(null)
const loading = ref(true)
const error = ref(null)
const currentTime = ref(0)
const audioPlayer = ref(null)
const selectedMessage = ref(null)
const token = ref(null)
const activeTab = ref('transcript') // Default active tab
const isSelfDestruct = computed(() => route.query.self_destruct === 'true')

// Define meta options
defineOptions({
  name: 'SharedInterviewPage',
  meta: {
    requiresAuth: false
  }
})

// Computed properties for interview data
const recordingUrl = computed(() => interview.value?.end_of_call_report?.recordingUrl || null)
const messages = computed(() => {
  if (!interview.value?.end_of_call_report?.artifact?.messages) return []
  // Filter out system messages, only show bot and user messages
  return interview.value.end_of_call_report.artifact.messages.filter(
    msg => msg.role === 'bot' || msg.role === 'user'
  )
})

// Format date string
function formatDate(dateString) {
  if (!dateString) return ''
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

// Format seconds to mm:ss
function formatSeconds(seconds) {
  if (seconds === undefined || seconds === null) return '00:00'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// Update current time as audio plays
function onTimeUpdate(event) {
  currentTime.value = event.target.currentTime

  // Check if we should stop playback at the end of the selected message segment
  if (selectedMessage.value) {
    const currentIndex = messages.value.indexOf(selectedMessage.value)
    const nextMsgStart = currentIndex < messages.value.length - 1
      ? messages.value[currentIndex + 1].secondsFromStart - 1
      : Infinity

    // If we've reached the next message's timestamp, pause the audio
    if (currentTime.value >= nextMsgStart) {
      audioPlayer.value.pause()
      selectedMessage.value = null
      console.log('Paused at end of message segment')
    }
  }
}

// Seek audio to the timestamp of the clicked message
function seekToMessage(message) {
  if (!audioPlayer.value || message.secondsFromStart === undefined) return

  // Store the selected message
  selectedMessage.value = message

  // Set the audio player's current time to the message's timestamp
  // Subtract 1 second to compensate for the timing lag
  const seekTime = Math.max(0, message.secondsFromStart - 1)
  audioPlayer.value.currentTime = seekTime

  // Start playing if paused
  if (audioPlayer.value.paused) {
    audioPlayer.value.play()
      .catch(err => console.error('Error playing audio:', err))
  }

  console.log('Seeking to:', seekTime, 'seconds (original timestamp:', message.secondsFromStart, ')')
}

// Check if a message is currently active based on audio playback position
function isActiveMessage(message) {
  if (!messages.value.length || currentTime.value === 0) return false

  const index = messages.value.indexOf(message)

  // Get the current message's start time - adjust by 1 second to fix timing issue
  const currentMsgStart = (message.secondsFromStart || 0) - 1

  // Ensure we don't go below 0
  const adjustedStart = Math.max(0, currentMsgStart)

  // Get the next message's start time (if there is a next message) - adjust by 1 second
  const nextMsgStart = index < messages.value.length - 1
    ? Math.max(0, (messages.value[index + 1].secondsFromStart || Infinity) - 1)
    : Infinity

  // This message is active if the current time is between this message's
  // adjusted start time and the next message's adjusted start time
  return currentTime.value >= adjustedStart && currentTime.value < nextMsgStart
}

// Toggle playback
function togglePlayback() {
  if (audioPlayer.value) {
    if (audioPlayer.value.paused) {
      audioPlayer.value.play()
    } else {
      audioPlayer.value.pause()
    }
  }
}

// Apply theme settings from the token data
function applyThemeSettings(tokenData) {
  // First check for URL parameters (they take precedence)
  const { theme: urlTheme, mode: urlMode } = route.query

  if (urlTheme && urlMode) {
    // Apply theme from URL parameters if they exist
    if (themeStore.availableThemes.some(t => t.name === urlTheme)) {
      themeStore.setThemeName(urlTheme)
    }

    if (urlMode === 'light' || urlMode === 'dark') {
      themeStore.setMode(urlMode)
    }
  } else if (tokenData) {
    // Apply theme from token data if URL parameters don't exist
    const themeName = tokenData.theme_name || 'default'
    const themeMode = tokenData.theme_mode || 'light'

    // Validate theme name
    if (themeStore.availableThemes.some(t => t.name === themeName)) {
      themeStore.setThemeName(themeName)
    } else {
      themeStore.setThemeName('default')
    }

    // Validate theme mode
    if (themeMode === 'light' || themeMode === 'dark') {
      themeStore.setMode(themeMode)
    } else {
      themeStore.setMode('light')
    }
  }
}

// Fetch interview details
async function fetchInterviewDetails() {
  loading.value = true
  error.value = null

  try {
    console.log('Fetching interview with token:', route.params.token)

    // First, validate the share token
    const { data: tokenData, error: tokenError } = await supabase
      .from('share_tokens')
      .select('interview_id, token, self_destruct, theme_name, theme_mode')
      .eq('token', route.params.token)
      .is('revoked_at', null)
      .single()

    if (tokenError) {
      console.error('Token validation error:', tokenError)
      throw new Error('Invalid or expired share link')
    }

    console.log('Token validated, interview ID:', tokenData.interview_id)

    // Apply theme settings from the token
    applyThemeSettings(tokenData)

    // Store the token
    token.value = tokenData.token

    // Then fetch the interview details
    const { data: interviewData, error: fetchError } = await supabase
      .from('interviews')
      .select('*, end_of_call_report, analysis')
      .eq('id', tokenData.interview_id)
      .single()

    if (fetchError) {
      console.error('Interview fetch error:', fetchError)
      throw fetchError
    }

    if (interviewData) {
      interview.value = interviewData
      console.log('Interview data loaded successfully')

      // Check if messages have secondsFromStart property
      if (interviewData?.end_of_call_report?.artifact?.messages) {
        const msgs = interviewData.end_of_call_report.artifact.messages
        console.log('Message timestamps:', msgs.map(m => ({
          role: m.role,
          secondsFromStart: m.secondsFromStart,
          preview: m.message?.substring(0, 20) + '...'
        })))

        // Add timestamps if they don't exist
        if (msgs.length > 0 && msgs[0].secondsFromStart === undefined) {
          console.log('Adding mock timestamps for testing')
          let cumulative = 0
          msgs.forEach((msg) => {
            // Simple algorithm: start with 0, add 5-15 seconds per message
            cumulative += 5 + Math.floor(Math.random() * 10)
            msg.secondsFromStart = cumulative
          })
        }
      }

      // If this is a self-destructing token, revoke it after fetching the data
      if (isSelfDestruct.value && token.value) {
        await revokeToken()
      }
    } else {
      error.value = 'Interview not found'
    }
  } catch (err) {
    console.error('Error fetching interview details:', err)
    error.value = err.message || 'Failed to load interview details'
  } finally {
    loading.value = false
  }
}

// Revoke the current token if it's a self-destructing one
async function revokeToken() {
  if (!token.value) {
    console.warn('No token to revoke')
    return
  }

  try {
    console.log('Revoking self-destructing token:', token.value)

    // Use the Edge Function to revoke the token
    const functionUrl = `${supabase.supabaseUrl}/functions/v1/revoke-token`
    const response = await fetch(functionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabase.supabaseKey}`  // Include the anon key for JWT verification
      },
      body: JSON.stringify({ token: token.value })
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      console.error('Error response from token revocation:', errorData)
      throw new Error(errorData.error || 'Failed to revoke token')
    }

    const result = await response.json().catch(() => ({ success: true }))
    console.log('Token revocation response:', result)

    console.log('Self-destructing token revoked successfully')
  } catch (err) {
    console.error('Error revoking token:', err)
    // We don't want to show this error to the user as it's not critical
    // The page has already been viewed and data loaded
  }
}

// Fetch interview data when component is mounted
onMounted(async () => {
  await fetchInterviewDetails()
})
</script>

<style scoped>
.voice-assistant-container {
  margin-left: 16px;
}

/* PDF printing styles */
@media print {
  .page-break-before {
    page-break-before: always;
  }
}

.voice-assistant-card {
  border-radius: 12px;
  overflow: hidden;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--v-theme-primary), 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--v-theme-primary), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--v-theme-primary), 0);
  }
}

.v-timeline-item__body {
  max-width: 550px;
}

audio {
  width: 100%;
  border-radius: 70px;
  background-color: var(--v-theme-secondary);
}

/* Attempt to style audio controls in supported browsers */
audio::-webkit-media-controls-panel {
  background-color: var(--v-theme-secondary);
}

audio::-webkit-media-controls-play-button {
  background-color: var(--v-theme-primary);
  border-radius: 50%;
}

audio::-webkit-media-controls-timeline {
  background-color: var(--v-theme-primary);
  border-radius: 25px;
  height: 4px;
  margin-right: 10px;
  margin-left: 10px;
}

audio::-webkit-media-controls-volume-slider {
  background-color: var(--v-theme-primary);
  border-radius: 25px;
  height: 4px;
}

audio::-webkit-media-controls-mute-button {
  background-color: var(--v-theme-primary);
  border-radius: 50%;
}

/* Force text color to remain black in both light and dark themes */
audio::-webkit-media-controls-current-time-display,
audio::-webkit-media-controls-time-remaining-display,
audio::-webkit-media-controls-enclosure {
  color: #000000 !important;
}

/* Ensure text remains black in any Vuetify theme context */
:deep(audio::-webkit-media-controls-current-time-display),
:deep(audio::-webkit-media-controls-time-remaining-display) {
  color: #000000 !important;
  text-shadow: none !important;
}

/* Use high specificity to override theme colors */
.v-theme--light audio::-webkit-media-controls *,
.v-theme--dark audio::-webkit-media-controls * {
  color: #000000 !important;
}

.v-timeline-item {
  margin-bottom: 8px;
}

.clickable-card {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.clickable-card:hover {
  transform: scale(1.01);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.active-message-card {
  border: 2px solid var(--v-theme-primary) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
  background-color: var(--v-theme-surface) !important;
  transition: all 0.3s ease;
  transform: scale(1.02);
}

.active-message-card p,
.active-message-card span {
  color: var(--v-theme-primary) !important;
  font-weight: 500 !important;
}

.user-message-card {
  background-color: var(--v-theme-secondary) !important;
  border-left: 4px solid var(--v-theme-primary) !important;
}

.user-name {
  color: var(--v-theme-primary) !important;
}

.user-message-text {
  color: var(--v-theme-primary) !important;
  font-weight: 400;
}

.active-message .v-timeline-divider__dot {
  animation: pulse 2s infinite;
}

/* Dark mode adjustments for user messages */
.v-theme--dark .user-message-card {
  background-color: rgba(var(--v-theme-secondary-rgb), 0.15) !important;
  border-left: 4px solid var(--v-theme-primary) !important;
}

.v-theme--dark .user-name {
  color: var(--v-theme-primary) !important;
}

.v-theme--dark .user-message-text {
  color: var(--v-theme-primary) !important;
}

.playback-toggle-btn {
  position: absolute;
  top: -2px;
  right: -8px;
  z-index: 2;
  opacity: 0.9;
  transition: all 0.2s ease;
}

.playback-toggle-btn:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* Download button styles */
.download-card {
  border: 1px dashed var(--v-theme-primary);
  transition: all 0.3s ease;
}

.download-card:hover {
  box-shadow: 0 4px 12px rgba(var(--v-theme-primary-rgb), 0.3);
  transform: translateY(-2px);
}

.download-btn {
  min-width: 200px;
  transition: all 0.3s ease;
}

.download-btn:hover {
  transform: scale(1.05);
}

/* Download section styles */
.download-section {
  border: 1px solid rgba(var(--v-theme-primary-rgb), 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.download-section:hover {
  box-shadow: 0 8px 16px rgba(var(--v-theme-primary-rgb), 0.2);
}

/* Add theme-aware text styles */
.text-high-emphasis {
  color: rgba(var(--v-theme-on-surface), 0.87) !important;
}

.text-primary {
  color: rgb(var(--v-theme-primary)) !important;
}

@media (max-width: 600px) {
  .download-section {
    padding: 16px !important;
  }
}

/* Update layout styles */
.interview-container {
  height: 100%;
  width: 100%;
  background-color: var(--v-theme-background) !important;
  color: var(--v-theme-on-background) !important;
  padding-bottom: 80px !important;
}

.interview-card {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  background-color: var(--v-theme-surface) !important;
  color: var(--v-theme-on-surface) !important;
}

/* .interview-card h1,
.interview-card h2,
.interview-card h3,
.interview-card h4,
.interview-card h5,
.interview-card h6,
.interview-card p,
.interview-card span,
.interview-card label,
.interview-card .text-caption,
.interview-card .text-h6,
.interview-card .text-h5,
.interview-card .text-h4,
.interview-card .text-h3,
.interview-card .text-h2,
.interview-card .text-h1 {
  color: var(--v-theme-on-surface) !important;
} */

/* Ensure summary and transcript text are readable */
.interview-card .v-card-text,
.interview-card .v-card-title,
.interview-card .pa-3 {
  color: var(--v-theme-on-surface) !important;
}

/* Title container styles */
.title-container {
  min-width: 300px;
}

/* Tab styles */
.v-tabs {
  border-radius: 8px;
  background-color: var(--v-theme-surface-variant) !important;
}

.v-tab {
  font-weight: 500;
  letter-spacing: 0.5px;
  min-width: 120px;
}

.v-tab--selected {
  background-color: rgba(var(--v-theme-primary), 0.1);
}

.v-window {
  border-radius: 8px;
  overflow: hidden;
}

/* Responsive adjustments for tabs */
@media (max-width: 600px) {
  .v-tab {
    min-width: unset;
  }
}
</style>

<route>
{
  meta: {
    requiresAuth: false
  }
}
</route>
