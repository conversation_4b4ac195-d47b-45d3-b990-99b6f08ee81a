<template>
  <v-container class="fill-height">
    <v-row justify="center">
      <v-col
        cols="12"
        sm="8"
        md="6"
        lg="4"
      >
        <v-card class="elevation-12 rounded-lg my-16">
          <v-card-title class="text-center py-4">
            <h1 class="text-h4 font-weight-bold">
              Reset Password
            </h1>
          </v-card-title>

          <v-card-text>
            <v-alert
              v-if="errorMessage"
              type="error"
              variant="tonal"
              closable
              class="mb-4"
              @click:close="errorMessage = ''"
            >
              {{ errorMessage }}
            </v-alert>

            <v-alert
              v-if="successMessage"
              type="success"
              variant="tonal"
              closable
              class="mb-4"
              @click:close="successMessage = ''"
            >
              {{ successMessage }}
            </v-alert>

            <v-form
              v-if="showForm"
              ref="form"
              @submit.prevent="handleResetPassword"
            >
              <v-text-field
                v-model="password"
                label="New Password"
                prepend-inner-icon="mdi-lock-outline"
                variant="outlined"
                :append-inner-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                :type="showPassword ? 'text' : 'password'"
                :rules="[rules.required, rules.min]"
                @click:append-inner="showPassword = !showPassword"
              />

              <v-text-field
                v-model="confirmPassword"
                label="Confirm Password"
                prepend-inner-icon="mdi-lock-outline"
                variant="outlined"
                :append-inner-icon="showConfirmPassword ? 'mdi-eye-off' : 'mdi-eye'"
                :type="showConfirmPassword ? 'text' : 'password'"
                :rules="[rules.required, rules.min, rules.match]"
                @click:append-inner="showConfirmPassword = !showConfirmPassword"
              />

              <v-btn
                block
                color="primary"
                size="large"
                type="submit"
                :loading="loading"
                class="mt-4"
              >
                Reset Password
              </v-btn>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { supabase } from '@/plugins/supabase'

const router = useRouter()
const route = useRoute()

// Form state
const form = ref(null)
const password = ref('')
const confirmPassword = ref('')
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const errorMessage = ref('')
const successMessage = ref('')
const loading = ref(false)
const showForm = ref(true)

// Form validation rules
const rules = {
  required: v => !!v || 'This field is required',
  min: v => v.length >= 6 || 'Password must be at least 6 characters',
  match: v => v === password.value || 'Passwords must match'
}

// Extract access token from URL hash on mount
onMounted(() => {
  if (route.hash) {
    const hashParams = new URLSearchParams(route.hash.substring(1))
    const accessToken = hashParams.get('access_token')

    if (!accessToken) {
      errorMessage.value = 'No access token found. Please try the password reset process again.'
      return
    }

    // Set the access token in Supabase session
    supabase.auth.setSession({
      access_token: accessToken,
      refresh_token: hashParams.get('refresh_token')
    })
  } else {
    errorMessage.value = 'Invalid password reset link. Please try the password reset process again.'
  }
})

// Handle password reset
async function handleResetPassword() {
  const { valid } = await form.value.validate()

  if (!valid) return

  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const { error } = await supabase.auth.updateUser({
      password: password.value
    })

    if (error) throw error

    successMessage.value = 'Password reset successfully!'
    showForm.value = false

    // Clear form
    password.value = ''
    confirmPassword.value = ''

    // Redirect to login page after a delay
    setTimeout(() => {
      router.push('/login')
    }, 2000)
  } catch (err) {
    console.error('Error resetting password:', err)
    errorMessage.value = err.message || 'Failed to reset password. Please try again.'
  } finally {
    loading.value = false
  }
}
</script>

<route>
{
  meta: {
    requiresAuth: false,
    layout: 'default'
  }
}
</route>

<style scoped>
.v-card {
  border-radius: 16px;
}
</style>
