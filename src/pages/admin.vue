<template>
  <!-- Admin Content -->
  <div class="flex-grow-1 overflow-y-auto pa-4">
    <v-card class="mb-4">
      <v-card-title class="d-flex align-center">
        <v-icon
          size="large"
          color="primary"
          class="mr-2"
        >
          mdi-shield-account
        </v-icon>
        Admin Dashboard
      </v-card-title>
    </v-card>

    <!-- Tabs Section -->
    <v-card>
      <!-- Desktop Tabs (hidden on mobile) -->
      <v-tabs
        v-model="activeTab"
        grow
        bg-color="background"
        slider-color="primary"
        class="mb-4 d-none d-md-flex"
      >
        <v-tab
          value="transactions"
          class="text-body-1"
        >
          <v-icon start>
            mdi-cash
          </v-icon>
          Transactions
        </v-tab>
        <v-tab
          value="users"
          class="text-body-1"
        >
          <v-icon start>
            mdi-account-group
          </v-icon>
          Users
        </v-tab>
        <v-tab
          value="user_minutes"
          class="text-body-1"
        >
          <v-icon start>
            mdi-clock-outline
          </v-icon>
          User Minutes
        </v-tab>
        <v-tab
          value="coupon_codes"
          class="text-body-1"
        >
          <v-icon start>
            mdi-ticket-percent
          </v-icon>
          Coupon Codes
        </v-tab>
        <v-tab
          value="interview_profiles"
          class="text-body-1"
        >
          <v-icon start>
            mdi-account-voice
          </v-icon>
          Interview Profiles
        </v-tab>
        <v-tab
          value="interviews"
          class="text-body-1"
        >
          <v-icon start>
            mdi-microphone-message
          </v-icon>
          Interviews
        </v-tab>
      </v-tabs>

      <!-- Mobile Dropdown (visible only on mobile) -->
      <div class="d-flex d-md-none pa-4 pb-0 mobile-section-select">
        <v-select
          v-model="activeTab"
          :items="tabOptions"
          item-title="title"
          item-value="value"
          label="Select Section"
          variant="outlined"
          density="compact"
          hide-details
          class="flex-grow-1"
        >
          <template #prepend-inner>
            <v-icon
              :icon="getTabIcon(activeTab)"
              size="small"
              class="mr-2"
            />
          </template>
        </v-select>
      </div>

      <v-window
        v-model="activeTab"
        class="ma-0 pa-0 ma-md-auto pa-md-auto"
      >
        <!-- Transactions Tab -->
        <v-window-item value="transactions">
          <v-card-title class="d-flex align-center justify-space-between">
            <div>
              Transactions
            </div>
            <v-text-field
              v-model="transactionSearch"
              append-icon="mdi-magnify"
              label="Search"
              single-line
              hide-details
              density="compact"
              class="ml-4"
              style="max-width: 300px"
              variant="outlined"
            />
          </v-card-title>

          <div class="pa-0 ma-0">
            <div
              v-if="loadingTransactions"
              class="d-flex justify-center py-4"
            >
              <v-progress-circular
                indeterminate
                color="primary"
              />
            </div>

            <div
              v-else-if="transactionsError"
              class="text-center py-4 text-error"
            >
              {{ transactionsError }}
            </div>

            <div
              v-if="!loadingTransactions && !transactionsError"
              class="mb-4"
            >
              <v-row dense>
                <v-col
                  cols="12"
                  sm="6"
                  md="3"
                >
                  <v-card
                    variant="elevated"
                    color="primary"
                    class="pa-4 transaction-stat-card"
                    elevation="3"
                  >
                    <div class="text-caption">
                      Today
                    </div>
                    <div class="text-h6 font-weight-bold">
                      {{ formatCurrency(totalToday) }}
                    </div>
                  </v-card>
                </v-col>
                <v-col
                  cols="12"
                  sm="6"
                  md="3"
                >
                  <v-card
                    variant="elevated"
                    color="success"
                    class="pa-4 transaction-stat-card"
                    elevation="3"
                  >
                    <div class="text-caption">
                      Yesterday
                    </div>
                    <div class="text-h6 font-weight-bold">
                      {{ formatCurrency(totalYesterday) }}
                    </div>
                  </v-card>
                </v-col>
                <v-col
                  cols="12"
                  sm="6"
                  md="3"
                >
                  <v-card
                    variant="elevated"
                    color="info"
                    class="pa-4 transaction-stat-card"
                    elevation="3"
                  >
                    <div class="text-caption">
                      Last 7 Days
                    </div>
                    <div class="text-h6 font-weight-bold">
                      {{ formatCurrency(totalLast7Days) }}
                    </div>
                  </v-card>
                </v-col>
                <v-col
                  cols="12"
                  sm="6"
                  md="3"
                >
                  <v-card
                    variant="elevated"
                    color="warning"
                    class="pa-4 transaction-stat-card"
                    elevation="3"
                  >
                    <div class="text-caption">
                      Last 30 Days
                    </div>
                    <div class="text-h6 font-weight-bold">
                      {{ formatCurrency(totalLast30Days) }}
                    </div>
                  </v-card>
                </v-col>
              </v-row>
            </div>

            <div
              v-if="transactions.length === 0 && !loadingTransactions && !transactionsError"
              class="text-center py-4"
            >
              <v-alert
                type="info"
                title="No Records"
                text="No transaction records found."
              />
            </div>

            <v-data-table
              v-else
              :headers="transactionsHeaders"
              :items="transactions"
              :search="transactionSearch"
              :loading="loadingTransactions"
              :items-per-page="transactionItemsPerPage"
              :page="transactionPage"
              :sort-by="transactionsSortBy"
              :custom-filter="customTransactionFilter"
              :mobile-breakpoint="600"
              density="comfortable"
              hover
              class="elevation-1"
              item-value="id"
              @update:page="transactionPage = $event"
              @update:sort-by="transactionsSortBy = $event"
              @update:items-per-page="transactionItemsPerPage = $event; transactionPage = 1"
            >
              <template #[`item.created_at`]="{ item }">
                <div class="text-no-wrap">
                  <div>{{ formatDateOnly(item.created_at) }}</div>
                  <div class="text-caption">{{ formatTimeOnly(item.created_at) }}</div>
                </div>
              </template>
              <template #[`item.total`]="{ item }">
                {{ formatCurrency(item.total) }}
              </template>
              <template #[`item.user`]="{ item }">
                <div>
                  <div><strong>{{ item.user_first_name || '' }} {{ item.user_last_name || '' }}</strong></div>
                  <div class="text-caption">
                    {{ item.user_email || '' }}
                  </div>
                </div>
              </template>
              <template #[`item.actions`]="{ item }">
                <div class="d-flex">
                  <v-btn
                    v-if="item.payment_intent_id"
                    size="small"
                    color="primary"
                    variant="text"
                    icon
                    :href="getStripeLink(item.payment_intent_id, item.livemode)"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="mr-2"
                  >
                    <v-icon>mdi-link</v-icon>
                  </v-btn>
                  <v-btn
                    size="small"
                    color="info"
                    variant="text"
                    icon
                    :to="`/admin/user/${item.user_id}`"
                  >
                    <v-icon>mdi-account</v-icon>
                  </v-btn>
                </div>
              </template>
            </v-data-table>
          </div>
        </v-window-item>

        <!-- Users Tab -->
        <v-window-item
          value="users"
          class="pa-2"
        >
          <v-card-title class="d-flex align-center justify-space-between">
            <div>
              User Profiles
            </div>
            <v-text-field
              v-model="search"
              append-icon="mdi-magnify"
              label="Search"
              single-line
              hide-details
              density="compact"
              class="ml-4"
              style="max-width: 300px"
              variant="outlined"
            />
          </v-card-title>

          <v-card-text>
            <div
              v-if="loadingUsers"
              class="d-flex justify-center py-4"
            >
              <v-progress-circular
                indeterminate
                color="primary"
              />
            </div>

            <div
              v-else-if="errorMessage"
              class="text-center py-4 text-error"
            >
              {{ errorMessage }}
            </div>

            <v-data-table
              v-else
              :headers="headers"
              :items="users"
              :search="search"
              :loading="loadingUsers"
              :items-per-page="itemsPerPage"
              :page="page"
              :sort-by="usersSortBy"
              :custom-filter="customUserFilter"
              :mobile-breakpoint="600"
              density="comfortable"
              hover
              class="elevation-1"
              item-value="user_id"
              @update:page="page = $event"
              @update:sort-by="usersSortBy = $event"
              @update:items-per-page="itemsPerPage = $event; page = 1"
            >
              <template #[`item.created_at`]="{ item }">
                <div class="text-no-wrap">
                  <div>{{ formatDateOnly(item.created_at) }}</div>
                  <div class="text-caption">{{ formatTimeOnly(item.created_at) }}</div>
                </div>
              </template>

              <template #[`item.avatar`]="{ item }">
                <v-avatar
                  size="32"
                  color="primary"
                  class="text-white font-weight-medium"
                >
                  {{ getUserInitialsFromProfile(item) }}
                </v-avatar>
              </template>

              <template #[`item.user`]="{ item }">
                <div class="py-2">
                  <div><strong>{{ item.first_name || '' }} {{ item.last_name || '' }}</strong></div>
                  <div class="text-caption">
                    {{ item.email || '' }}
                  </div>
                </div>
              </template>

              <template #[`item.mobile_phone`]="{ item }">
                <div class="text-no-wrap">
                  {{ formatPhoneForDisplay(item.mobile_phone) || '-' }}
                </div>
              </template>

              <template #[`item.minutes_balance`]="{ item }">
                <span :class="{'text-success': item.minutes_balance > 0, 'text-grey': item.minutes_balance === 0}">
                  {{ item.minutes_balance }}
                </span>
              </template>

              <template #[`item.minutes_used`]="{ item }">
                <span :class="{'text-primary': item.minutes_used > 0, 'text-grey': item.minutes_used === 0}">
                  {{ item.minutes_used }}
                </span>
              </template>

              <template #[`item.interviews_count`]="{ item }">
                <div class="text-center w-100">
                  <v-chip
                    v-if="item.interviews_count > 0"
                    size="small"
                    color="info"
                    variant="flat"
                    class="font-weight-medium"
                  >
                    {{ item.interviews_count }}
                  </v-chip>
                  <span v-else>0</span>
                </div>
              </template>

              <template #[`item.total_spent`]="{ item }">
                {{ formatCurrency(item.total_spent) }}
              </template>

              <template #[`item.actions`]="{ item }">
                <v-icon
                  small
                  class="mr-2"
                  @click="editUser(item)"
                >
                  mdi-pencil
                </v-icon>
              </template>
            </v-data-table>
          </v-card-text>
        </v-window-item>

        <!-- User Minutes Tab -->
        <v-window-item value="user_minutes">
          <v-card-title class="d-flex align-center justify-space-between">
            <div>
              User Minutes
            </div>
            <v-text-field
              v-model="minutesSearch"
              append-icon="mdi-magnify"
              label="Search"
              single-line
              hide-details
              density="compact"
              class="ml-4"
              style="max-width: 300px"
              variant="outlined"
            />
          </v-card-title>

          <v-card-text>
            <div
              v-if="loadingAllMinutes"
              class="d-flex justify-center py-4"
            >
              <v-progress-circular
                indeterminate
                color="primary"
              />
            </div>

            <div
              v-else-if="allMinutesError"
              class="text-center py-4 text-error"
            >
              {{ allMinutesError }}
            </div>

            <div
              v-else-if="allMinutes.length === 0"
              class="text-center py-4"
            >
              <v-alert
                type="info"
                title="No Records"
                text="No user minutes records found."
              />
            </div>

            <v-data-table
              v-else
              :headers="allMinutesHeaders"
              :items="allMinutes"
              :search="minutesSearch"
              :loading="loadingAllMinutes"
              :items-per-page="minutesItemsPerPage"
              :page="minutesPage"
              :sort-by="minutesSortBy"
              :mobile-breakpoint="600"
              density="comfortable"
              hover
              class="elevation-1"
              item-value="id"
              @update:page="minutesPage = $event"
              @update:sort-by="minutesSortBy = $event"
              @update:items-per-page="minutesItemsPerPage = $event; minutesPage = 1"
            >
              <template #[`item.created_at`]="{ item }">
                <div class="text-no-wrap">
                  <div>{{ formatDateOnly(item.created_at) }}</div>
                  <div class="text-caption">{{ formatTimeOnly(item.created_at) }}</div>
                </div>
              </template>
              <template #[`item.minutes`]="{ item }">
                <span :class="{'text-error': item.minutes < 0, 'text-success': item.minutes > 0}">
                  {{ item.minutes }}
                </span>
              </template>
              <template #[`item.user`]="{ item }">
                <div>
                  <div><strong>{{ item.user_first_name || '' }} {{ item.user_last_name || '' }}</strong></div>
                  <div class="text-caption">
                    {{ item.user_email || '' }}
                  </div>
                </div>
              </template>
              <template #[`item.source`]="{ item }">
                <v-chip
                  v-if="item.interview_id"
                  size="small"
                  color="primary"
                  class="text-none"
                  @click="openInterview(item.interview_id)"
                >
                  Interview
                </v-chip>
                <v-chip
                  v-else-if="item.transaction_id"
                  size="small"
                  color="success"
                  class="text-none"
                >
                  Transaction
                </v-chip>
                <v-chip
                  v-else
                  size="small"
                  color="grey"
                  class="text-none"
                >
                  Manual
                </v-chip>
              </template>
              <template #[`item.actions`]="{ item }">
                <v-btn
                  size="small"
                  color="info"
                  variant="text"
                  icon
                  :to="`/admin/user/${item.user_id}`"
                >
                  <v-icon>mdi-account</v-icon>
                </v-btn>
              </template>
            </v-data-table>
          </v-card-text>
        </v-window-item>

        <!-- Coupon Codes Tab -->
        <v-window-item value="coupon_codes">
          <v-card-title>
            Coupon Codes
          </v-card-title>
          <v-card-text>
            <CouponCodesTable @create-coupon="openCreateCouponDialog" />
          </v-card-text>
        </v-window-item>

        <!-- Interview Profiles Tab -->
        <v-window-item
          value="interview_profiles"
          class="pa-2"
        >
          <v-card-title class="d-flex align-center justify-space-between">
            <div>
              Interview Profiles
            </div>
            <v-spacer />
            <v-btn
              color="primary"
              variant="flat"
              @click="openAddProfileDialog"
            >
              <v-icon left>
                mdi-plus
              </v-icon>
              Add Profile
            </v-btn>
          </v-card-title>
          <v-card-text>
            <div
              v-if="loadingProfiles"
              class="d-flex justify-center py-4"
            >
              <v-progress-circular
                indeterminate
                color="primary"
              />
            </div>
            <div
              v-else-if="profilesError"
              class="text-center py-4 text-error"
            >
              {{ profilesError }}
            </div>
            <v-data-table
              v-else
              :headers="profilesHeaders"
              :items="interviewProfiles"
              :loading="loadingProfiles"
              :items-per-page="profilesItemsPerPage"
              :page="profilesPage"
              :server-items-length="totalProfiles"
              :mobile-breakpoint="600"
              class="elevation-1"
              @update:page="profilesPage = $event"
              @update:items-per-page="profilesItemsPerPage = $event; profilesPage = 1"
            >
              <template #[`item.created_at`]="{ item }">
                <div class="text-no-wrap">
                  <div>{{ formatDateOnly(item.created_at) }}</div>
                  <div class="text-caption">{{ formatTimeOnly(item.created_at) }}</div>
                </div>
              </template>
              <template #[`item.updated_at`]="{ item }">
                <div class="text-no-wrap">
                  <div>{{ formatDateOnly(item.updated_at) }}</div>
                  <div class="text-caption">{{ formatTimeOnly(item.updated_at) }}</div>
                </div>
              </template>
              <template #[`item.status`]="{ item }">
                <v-chip
                  :color="item.status === 'active' ? 'success' : 'grey'"
                  size="small"
                >
                  {{ item.status }}
                </v-chip>
              </template>
              <template #[`item.actions`]="{ item }">
                <v-icon
                  small
                  class="mr-2"
                  @click="openEditProfileDialog(item)"
                >
                  mdi-pencil
                </v-icon>
                <v-icon
                  small
                  @click="confirmDeleteProfile(item)"
                >
                  mdi-delete
                </v-icon>
              </template>
            </v-data-table>
          </v-card-text>
          <InterviewProfileDialog
            v-model="profileDialog"
            :editing-profile="editingProfile"
            @saved="onProfileSaved"
            @closed="closeProfileDialog"
          />
          <v-dialog
            v-model="deleteDialog"
            max-width="400"
          >
            <v-card>
              <v-card-title class="text-h6">
                Confirm Delete
              </v-card-title>
              <v-card-text>Are you sure you want to delete this profile?</v-card-text>
              <v-card-actions>
                <v-spacer />
                <v-btn
                  variant="text"
                  @click="deleteDialog = false"
                >
                  Cancel
                </v-btn>
                <v-btn
                  color="error"
                  variant="text"
                  :loading="deletingProfile"
                  @click="deleteProfile"
                >
                  Delete
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-window-item>

        <!-- Interviews Tab -->
        <v-window-item value="interviews">
          <v-card-title class="d-flex align-center justify-space-between">
            <div>
              Interviews
            </div>
            <v-text-field
              v-model="interviewSearch"
              append-icon="mdi-magnify"
              label="Search"
              single-line
              hide-details
              density="compact"
              class="ml-4"
              style="max-width: 300px"
              variant="outlined"
            />
          </v-card-title>

          <v-card-text>
            <div
              v-if="loadingInterviews"
              class="d-flex justify-center py-4"
            >
              <v-progress-circular
                indeterminate
                color="primary"
              />
            </div>

            <div
              v-else-if="interviewsError"
              class="text-center py-4 text-error"
            >
              {{ interviewsError }}
            </div>

            <div
              v-else-if="interviews.length === 0"
              class="text-center py-4"
            >
              <v-alert
                type="info"
                title="No Records"
                text="No interview records found."
              />
            </div>

            <v-data-table
              v-else
              :headers="interviewsHeaders"
              :items="interviews"
              :search="interviewSearch"
              :loading="loadingInterviews"
              :items-per-page="interviewsItemsPerPage"
              :page="interviewsPage"
              :sort-by="interviewsSortBy"
              :mobile-breakpoint="600"
              density="comfortable"
              hover
              class="elevation-1"
              item-value="id"
              @update:page="interviewsPage = $event"
              @update:sort-by="interviewsSortBy = $event"
            >
              <template #[`item.title`]="{ item }">
                {{ item.title || 'Untitled Interview' }}
              </template>
              <template #[`item.user_name`]="{ item }">
                {{ item.user_name || 'Unknown User' }}
              </template>
              <template #[`item.created_at`]="{ item }">
                <div class="text-no-wrap">
                  <div>{{ formatDateOnly(item.created_at) }}</div>
                  <div class="text-caption">{{ formatTimeOnly(item.created_at) }}</div>
                </div>
              </template>
              <template #[`item.duration_minutes`]="{ item }">
                {{ item.duration_minutes || 0 }}
              </template>
              <template #[`item.overall`]="{ item }">
                <v-chip
                  v-if="item.overall != null"
                  :color="getScoreColor(item.overall)"
                  size="small"
                  variant="flat"
                  class="font-weight-medium score-chip"
                >
                  {{ Number(item.overall).toFixed(1) }}
                </v-chip>
                <span v-else>-</span>
              </template>
              <template #[`item.fillerWords`]="{ item }">
                <v-chip
                  v-if="item.fillerWords != null"
                  :color="getFillerWordColor(item.fillerWords)"
                  size="small"
                  variant="flat"
                  class="font-weight-medium score-chip"
                >
                  {{ Number(item.fillerWords).toFixed(1) + '%' }}
                </v-chip>
                <span v-else>-</span>
              </template>
              <!-- Dynamic category columns -->
              <template
                v-for="category in categoryList"
                :key="category"
                #[`item.${category}`]="{ item }"
              >
                <v-chip
                  v-if="item[category] != null"
                  :color="getScoreColor(item[category])"
                  size="small"
                  variant="flat"
                  class="font-weight-medium score-chip"
                >
                  {{ Number(item[category]).toFixed(1) }}
                </v-chip>
                <span v-else>-</span>
              </template>
              <template #[`item.actions`]="{ item }">
                <v-btn
                  size="small"
                  variant="text"
                  color="primary"
                  :to="{ name: 'interview-details', params: { id: item.id } }"
                >
                  View
                </v-btn>
              </template>
            </v-data-table>
          </v-card-text>
        </v-window-item>
      </v-window>
    </v-card>

    <!-- Create Coupon Dialog -->
    <v-dialog
      v-model="createCouponDialog"
      max-width="600px"
    >
      <v-card>
        <v-card-title>
          <div class="d-flex align-center">
            <v-icon
              color="primary"
              class="mr-2"
            >
              mdi-ticket-percent
            </v-icon>
            Create Coupon Code
          </div>
        </v-card-title>
        <v-card-text>
          <CouponGenerator @coupon-created="onCouponCreated" />
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="grey-darken-1"
            variant="text"
            @click="createCouponDialog = false"
          >
            Close
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { supabase } from '@/plugins/supabase'
import InterviewProfileDialog from '@/components/InterviewProfileDialog.vue'
import CouponGenerator from '@/components/CouponGenerator.vue'
import CouponCodesTable from '@/components/CouponCodesTable.vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Accept tab as a prop
const props = defineProps({
  tab: { type: String, default: '' }
})

// Active tab
const activeTab = ref(props.tab || 'transactions')

// Tab options for mobile dropdown
const tabOptions = ref([
  { title: 'Transactions', value: 'transactions', icon: 'mdi-cash' },
  { title: 'Users', value: 'users', icon: 'mdi-account-group' },
  { title: 'User Minutes', value: 'user_minutes', icon: 'mdi-clock-outline' },
  { title: 'Coupon Codes', value: 'coupon_codes', icon: 'mdi-ticket-percent' },
  { title: 'Interview Profiles', value: 'interview_profiles', icon: 'mdi-account-voice' },
  { title: 'Interviews', value: 'interviews', icon: 'mdi-microphone-message' }
])

// Get tab icon helper function
const getTabIcon = (tabValue) => {
  const tab = tabOptions.value.find(option => option.value === tabValue)
  return tab ? tab.icon : 'mdi-view-dashboard'
}

// Watch for route changes to update activeTab
watch(
  () => route.params.tab,
  (newTab) => {
    if (newTab && newTab !== activeTab.value) {
      activeTab.value = newTab
    } else if (!newTab) {
      activeTab.value = 'transactions'
    }
  },
  { immediate: true }
)

// Watch for tab changes to update the route
watch(activeTab, (newTab) => {
  if (route.params.tab !== newTab) {
    if (newTab === 'transactions') {
      router.replace({ name: 'admin' })
    } else {
      router.replace({ name: 'admin-tab', params: { tab: newTab } })
    }
  }
})

// User profiles table data
const users = ref([])
const loadingUsers = ref(false)
const errorMessage = ref(null)
const search = ref('')

// Pagination
const page = ref(1)
const itemsPerPage = ref(10)
const usersSortBy = ref([{ key: 'created_at', order: 'desc' }])

// Table headers
const headers = ref([
  { title: 'Avatar', key: 'avatar', sortable: false, width: '80px' },
  { title: 'User', key: 'user', sortable: false, width: '220px' },
  { title: 'Phone', key: 'mobile_phone', sortable: true, width: '150px' },
  { title: 'Role', key: 'role', sortable: true },
  { title: 'Minutes Used', key: 'minutes_used', sortable: true, align: 'end' },
  { title: 'Minutes Balance', key: 'minutes_balance', sortable: true, align: 'end' },
  { title: 'Interviews', key: 'interviews_count', sortable: true, align: 'center' },
  { title: 'Total Spent', key: 'total_spent', sortable: true, align: 'end' },
  { title: 'Created At', key: 'created_at', sortable: true, width: '140px' },
  { title: 'Actions', key: 'actions', sortable: false }
])

// Transactions table data
const transactions = ref([])
const loadingTransactions = ref(false)
const transactionsError = ref(null)
const transactionSearch = ref('')

// Transaction totals
const totalToday = ref(0)
const totalYesterday = ref(0)
const totalLast7Days = ref(0)
const totalLast30Days = ref(0)

// Transactions pagination
const transactionPage = ref(1)
const transactionItemsPerPage = ref(10)
const transactionsSortBy = ref([{ key: 'created_at', order: 'desc' }])

// Transactions table headers
const transactionsHeaders = ref([
  { title: 'Date', key: 'created_at', sortable: true },
  { title: 'User', key: 'user', sortable: false },
  { title: 'Product', key: 'product_name', sortable: true },
  { title: 'Amount', key: 'total', sortable: true, align: 'end' },
  { title: 'Minutes', key: 'minutes', sortable: true },
  { title: 'Referral', key: 'referral_code', sortable: true },
  { title: 'Actions', key: 'actions', sortable: false }
])

// Interview Profiles state
const interviewProfiles = ref([])
const loadingProfiles = ref(false)
const profilesError = ref(null)
const profilesPage = ref(1)
const profilesItemsPerPage = ref(10)
const totalProfiles = ref(0)
const profilesHeaders = ref([
  { title: 'Title', key: 'title', sortable: true },
  { title: 'Status', key: 'status', sortable: true },
  { title: 'Created At', key: 'created_at', sortable: true },
  { title: 'Updated At', key: 'updated_at', sortable: true },
  { title: 'Actions', key: 'actions', sortable: false }
])
const profileDialog = ref(false)
const editingProfile = ref(null)
const deleteDialog = ref(false)
const deletingProfile = ref(false)
const profileToDelete = ref(null)

// Interviews tab state
const interviews = ref([])
const interviewData = ref([])
const categoryList = ref([])
const loadingInterviews = ref(false)
const interviewsError = ref(null)
const interviewSearch = ref('')
const interviewsPage = ref(1)
const interviewsItemsPerPage = ref(10)
const interviewsSortBy = ref([{ key: 'created_at', order: 'desc' }])
const interviewsHeaders = ref([
  { title: 'Title', key: 'title', sortable: true },
  { title: 'User', key: 'user_name', sortable: true },
  { title: 'Date', key: 'created_at', sortable: true },
  { title: 'Duration (min)', key: 'duration_minutes', sortable: true, align: 'end' },
  { title: 'Overall Score', key: 'overall', sortable: true, align: 'end' },
  { title: 'Filler Words %', key: 'fillerWords', sortable: true, align: 'end' },
  { title: 'Actions', key: 'actions', sortable: false, align: 'end' }
])

// User Minutes tab state
const allMinutes = ref([])
const loadingAllMinutes = ref(false)
const allMinutesError = ref(null)
const minutesSearch = ref('')
const minutesPage = ref(1)
const minutesItemsPerPage = ref(10)
const minutesSortBy = ref([{ key: 'created_at', order: 'desc' }])
const allMinutesHeaders = ref([
  { title: 'Date/Time', key: 'created_at', sortable: true },
  { title: 'User', key: 'user', sortable: false },
  { title: 'Minutes', key: 'minutes', sortable: true, align: 'end' },
  { title: 'Note', key: 'note', sortable: false },
  { title: 'Source', key: 'source', sortable: false },
  { title: 'Actions', key: 'actions', sortable: false }
])

// Create Coupon Dialog state
const createCouponDialog = ref(false)

onMounted(() => {
  // Redirect if user is not an admin
  if (!authStore.user?.user_metadata?.role || authStore.user.user_metadata.role !== 'admin') {
    console.log('Unauthorized access attempt to admin page')
    router.push('/interviews')
  }

  // Load initial data for the current tab
  if (activeTab.value === 'users') {
    fetchUsers()
  } else if (activeTab.value === 'transactions') {
    fetchTransactions()
  } else if (activeTab.value === 'interview_profiles') {
    fetchInterviewProfiles()
  } else if (activeTab.value === 'interviews') {
    fetchInterviews()
  } else if (activeTab.value === 'user_minutes') {
    fetchAllUserMinutes()
  }
  // Note: coupon_codes tab doesn't need special data fetching as CouponCodesTable handles its own data
})

// Watch for tab changes
watch(activeTab, (newTab) => {
  if (newTab === 'users') {
    fetchUsers()
  } else if (newTab === 'transactions') {
    fetchTransactions()
  } else if (newTab === 'interview_profiles') {
    fetchInterviewProfiles()
  } else if (newTab === 'interviews') {
    fetchInterviews()
  } else if (newTab === 'user_minutes') {
    fetchAllUserMinutes()
  }
  // Note: coupon_codes tab doesn't need special data fetching as CouponCodesTable handles its own data
})

// Watch for changes to pagination
watch([page, itemsPerPage], () => {
  if (activeTab.value === 'users') {
    fetchUsers()
  }
})

// Watch for changes to transactions pagination
watch([transactionPage, transactionItemsPerPage], () => {
  if (activeTab.value === 'transactions') {
    fetchTransactions()
  }
})

// Watch for pagination
watch([profilesPage, profilesItemsPerPage], () => {
  if (activeTab.value === 'interview_profiles') {
    fetchInterviewProfiles()
  }
})

// Watch for interviews pagination
watch([interviewsPage, interviewsItemsPerPage], () => {
  if (activeTab.value === 'interviews') {
    fetchInterviews()
  }
})

// Watch for minutes pagination
watch([minutesPage, minutesItemsPerPage], () => {
  if (activeTab.value === 'user_minutes') {
    fetchAllUserMinutes()
  }
})

// Format date only (no time)
function formatDateOnly(dateString) {
  if (!dateString) return ''

  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date)
}

// Format time only (no date)
function formatTimeOnly(dateString) {
  if (!dateString) return ''

  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    hour: 'numeric',
    minute: 'numeric'
  }).format(date)
}

// Format currency
function formatCurrency(value) {
  if (!value) return '$0.00'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value)
}

// Format phone number for display
function formatPhoneForDisplay(phoneNumber) {
  if (!phoneNumber) return ''

  const digitsOnly = phoneNumber.replace(/\D/g, '')
  if (digitsOnly.length === 10) {
    return `(${digitsOnly.slice(0, 3)}) ${digitsOnly.slice(3, 6)}-${digitsOnly.slice(6)}`
  }

  return phoneNumber
}

// Get Stripe link
function getStripeLink(paymentIntentId, livemode) {
  // Determine if this is a test or live mode transaction
  const baseUrl = livemode
    ? 'https://dashboard.stripe.com/payments/'
    : 'https://dashboard.stripe.com/test/payments/';

  return baseUrl + paymentIntentId;
}

// Fetch users with pagination
async function fetchUsers() {
  loadingUsers.value = true
  errorMessage.value = null

  try {
    console.log('Fetching all users')

    // Fetch all user data
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error

    console.log('Raw user data received:', data)

    // Get user IDs for additional queries
    const userIds = data.map(user => user.user_id).filter(Boolean)

    // Fetch minutes data for all users
    const { data: minutesData, error: minutesError } = await supabase
      .from('user_minutes')
      .select('user_id, minutes')
      .in('user_id', userIds)

    if (minutesError) throw minutesError

    // Calculate minutes balance and used for each user
    const userMinutes = {}
    minutesData.forEach(record => {
      if (!userMinutes[record.user_id]) {
        userMinutes[record.user_id] = {
          balance: 0,
          used: 0
        }
      }
      // Add all minutes to balance
      userMinutes[record.user_id].balance += record.minutes

      // Track used minutes (negative values as positive numbers)
      if (record.minutes < 0) {
        userMinutes[record.user_id].used += Math.abs(record.minutes)
      }
    })

    // Fetch interviews count for each user
    const { data: interviewsData, error: interviewsError } = await supabase
      .from('interviews')
      .select('user_id, id')
      .in('user_id', userIds)

    if (interviewsError) throw interviewsError

    // Count interviews for each user
    const userInterviews = {}
    interviewsData.forEach(interview => {
      if (!userInterviews[interview.user_id]) {
        userInterviews[interview.user_id] = 0
      }
      userInterviews[interview.user_id]++
    })

    // Fetch transaction data for all users
    const { data: transactionsData, error: transactionsError } = await supabase
      .from('transactions')
      .select('user_id, total')
      .in('user_id', userIds)

    if (transactionsError) throw transactionsError

    // Calculate total spent for each user
    const userSpending = {}
    transactionsData.forEach(transaction => {
      if (!userSpending[transaction.user_id]) {
        userSpending[transaction.user_id] = 0
      }
      // Add transaction total to user's spending
      if (transaction.total) {
        userSpending[transaction.user_id] += transaction.total
      }
    })

    // Combine user data with minutes, interviews, and spending data
    users.value = data.map(user => ({
      ...user,
      minutes_balance: userMinutes[user.user_id]?.balance || 0,
      minutes_used: userMinutes[user.user_id]?.used || 0,
      interviews_count: userInterviews[user.user_id] || 0,
      total_spent: userSpending[user.user_id] || 0
    }))
  } catch (err) {
    console.error('Error fetching user profiles:', err)
    errorMessage.value = 'Failed to load user profiles. Please try again.'
  } finally {
    loadingUsers.value = false
  }
}

// Fetch transactions with pagination
async function fetchTransactions() {
  loadingTransactions.value = true
  transactionsError.value = null

  try {
    console.log('Fetching all transactions')

    // Fetch all transaction data
    const { data: transactionData, error: transactionError } = await supabase
      .from('transactions')
      .select('*')
      .order('created_at', { ascending: false })

    if (transactionError) throw transactionError

    console.log('Raw transactions data received:', transactionData)

    // Get unique user IDs from transactions
    const userIds = [...new Set(transactionData.map(t => t.user_id).filter(Boolean))]

    // If there are user IDs, fetch the corresponding user profiles
    let userProfiles = {}
    if (userIds.length > 0) {
      const { data: userData, error: userError } = await supabase
        .from('user_profiles')
        .select('user_id, first_name, last_name, email')
        .in('user_id', userIds)

      if (userError) throw userError

      // Create a lookup map of user profiles by user_id
      userProfiles = userData.reduce((acc, user) => {
        acc[user.user_id] = user
        return acc
      }, {})
    }

    // Combine transaction data with user profile data
    transactions.value = transactionData.map(transaction => {
      const userProfile = userProfiles[transaction.user_id] || {}
      return {
        ...transaction,
        user_first_name: userProfile.first_name || '',
        user_last_name: userProfile.last_name || '',
        user_email: userProfile.email || ''
      }
    })

    computeTransactionTotals()
  } catch (err) {
    console.error('Error fetching transactions:', err)
    transactionsError.value = 'Failed to load transactions. Please try again.'
  } finally {
    loadingTransactions.value = false
  }
}

// Calculate totals for various time ranges
function computeTransactionTotals() {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const yesterdayStart = new Date(today)
  yesterdayStart.setDate(yesterdayStart.getDate() - 1)

  const last7Start = new Date(today)
  last7Start.setDate(last7Start.getDate() - 6)

  const last30Start = new Date(today)
  last30Start.setDate(last30Start.getDate() - 29)

  let todaySum = 0
  let yesterdaySum = 0
  let sevenSum = 0
  let thirtySum = 0

  transactions.value.forEach(t => {
    if (!t.created_at || !t.total) return
    const d = new Date(t.created_at)

    if (d >= today) {
      todaySum += t.total
    }
    if (d >= yesterdayStart && d < today) {
      yesterdaySum += t.total
    }
    if (d >= last7Start) {
      sevenSum += t.total
    }
    if (d >= last30Start) {
      thirtySum += t.total
    }
  })

  totalToday.value = todaySum
  totalYesterday.value = yesterdaySum
  totalLast7Days.value = sevenSum
  totalLast30Days.value = thirtySum
}

// Fetch interview profiles
async function fetchInterviewProfiles() {
  loadingProfiles.value = true
  profilesError.value = null
  try {
    const from = (profilesPage.value - 1) * profilesItemsPerPage.value
    const to = from + profilesItemsPerPage.value - 1
    // Get count for pagination
    const countResponse = await supabase
      .from('interview_profiles')
      .select('*', { count: 'exact', head: true })
    if (countResponse.error) throw countResponse.error
    totalProfiles.value = countResponse.count || 0
    // Fetch actual data
    const { data, error } = await supabase
      .from('interview_profiles')
      .select('*')
      .range(from, to)
      .order('created_at', { ascending: false })
    if (error) throw error
    interviewProfiles.value = data || []
  } catch (err) {
    console.error('Error fetching interview profiles:', err)
    profilesError.value = 'Failed to load interview profiles. Please try again.'
  } finally {
    loadingProfiles.value = false
  }
}

// Edit user function
function editUser(user) {
  console.log('Edit user:', user)
  // Log to confirm we have the correct ID
  console.log('Navigating to edit user with ID:', user.user_id)
  // Create the full path to ensure it's correctly formed
  const editPath = `/admin/user/${user.user_id}`
  console.log('Navigating to path:', editPath)

  // Use router.push with state to pass the user data
  router.push({
    path: editPath,
    // Pass the user object as state which persists during navigation
    state: {
      userObject: user
    }
  })
}

function openAddProfileDialog() {
  editingProfile.value = null
  profileDialog.value = true
}
function openEditProfileDialog(profile) {
  editingProfile.value = { ...profile }
  profileDialog.value = true
}
function closeProfileDialog() {
  profileDialog.value = false
  editingProfile.value = null
}
function onProfileSaved() {
  closeProfileDialog()
  fetchInterviewProfiles()
}
function confirmDeleteProfile(profile) {
  profileToDelete.value = profile
  deleteDialog.value = true
}
async function deleteProfile() {
  if (!profileToDelete.value) return
  deletingProfile.value = true
  try {
    const { error } = await supabase
      .from('interview_profiles')
      .delete()
      .eq('id', profileToDelete.value.id)
    if (error) throw error
    deleteDialog.value = false
    fetchInterviewProfiles()
  } catch (err) {
    console.error('Error deleting profile:', err)
    profilesError.value = 'Failed to delete profile. Please try again.'
  } finally {
    deletingProfile.value = false
    profileToDelete.value = null
  }
}

// Fetch all interviews with user info
async function fetchInterviews() {
  loadingInterviews.value = true
  interviewsError.value = null

  try {
    console.log('Fetching all interviews')

    // Get all interviews from the database
    const { data: interviewsData, error: interviewsError } = await supabase
      .from('interviews')
      .select('id, created_at, duration_minutes, title, analysis, user_id')
      .order('created_at', { ascending: false })

    if (interviewsError) throw interviewsError

    console.log('Raw interviews data received:', interviewsData)

    // Get unique user IDs from the interviews
    const userIds = [...new Set(interviewsData.map(interview => interview.user_id).filter(Boolean))]

    // If we have user IDs, fetch the corresponding user profiles
    let userProfiles = {}
    if (userIds.length > 0) {
      const { data: userData, error: userError } = await supabase
        .from('user_profiles')
        .select('user_id, first_name, last_name, email')
        .in('user_id', userIds)

      if (userError) throw userError

      // Create a lookup map of user profiles by user_id
      userProfiles = userData.reduce((acc, user) => {
        acc[user.user_id] = user
        return acc
      }, {})
    }

    // If we have interviews, extract the scores from the analysis field
    if (interviewsData && interviewsData.length > 0) {
      // Process interviews to extract scores
      const processedInterviews = interviewsData.map(interview => {
        let scores = null
        let fillerWordsPercentage = null
        let overall = null

        // Get user information
        const userProfile = userProfiles[interview.user_id] || {}
        const userName = userProfile.first_name && userProfile.last_name
          ? `${userProfile.first_name} ${userProfile.last_name}`
          : userProfile.email || 'Unknown User'

        // Try to extract scores from analysis JSON
        if (interview.analysis) {
          try {
            const analysis = typeof interview.analysis === 'string'
              ? JSON.parse(interview.analysis)
              : interview.analysis

            // Get filler words percentage if available
            if (analysis.filler_words) {
              // Use the filler_word_percentage field if available
              if (analysis.filler_words.filler_word_percentage !== undefined) {
                fillerWordsPercentage = analysis.filler_words.filler_word_percentage;
              }
              // Otherwise calculate it from total_words and total_filler_words
              else if (analysis.filler_words.total_words && analysis.filler_words.total_filler_words) {
                const totalWords = analysis.filler_words.total_words;
                const totalFillerWords = analysis.filler_words.total_filler_words;

                if (totalWords > 0) {
                  fillerWordsPercentage = (totalFillerWords / totalWords) * 100;
                }
              }
            }

            if (analysis.grading) {
              // Get overall score
              overall = analysis.grading.overall?.score || null

              // Get category scores
              const categoryScores = {}
              if (analysis.grading.categories) {
                Object.entries(analysis.grading.categories).forEach(([category, data]) => {
                  // Skip Composure and Delivery category and Personal Introduction
                  if (category !== 'Composure and Delivery' && category !== 'Personal Introduction') {
                    categoryScores[category] = data.score

                    // Add to category list for dynamic chart creation
                    if (!categoryList.value.includes(category)) {
                      categoryList.value.push(category)
                    }
                  }
                })
              }

              scores = {
                overall,
                ...categoryScores
              }
            }
          } catch (e) {
            console.error(`Failed to parse analysis for interview ${interview.id}:`, e)
          }
        }

        // Process the interview to include flattened score values for data table
        const processedItem = {
          ...interview,
          user_name: userName,
          scores,
          fillerWords: fillerWordsPercentage,
          overall
        }

        // Add each category as a direct property for the data table
        if (scores) {
          categoryList.value.forEach(category => {
            if (scores[category] !== undefined) {
              processedItem[category] = scores[category]
            }
          })
        }

        return processedItem
      })

      console.log('Processed interviews with scores:', processedInterviews)
      console.log('Categories found:', categoryList.value)

      // Update the data ref
      interviewData.value = processedInterviews
      interviews.value = processedInterviews

      // Filter out Personal Introduction from category list if it exists
      categoryList.value = categoryList.value.filter(category =>
        category !== 'Personal Introduction'
      );

      // Add headers for each category dynamically
      if (categoryList.value.length > 0) {
        // Start with the basic headers
        const updatedHeaders = [
          { title: 'Title', key: 'title', sortable: true },
          { title: 'User', key: 'user_name', sortable: true },
          { title: 'Date', key: 'created_at', sortable: true },
          { title: 'Duration (min)', key: 'duration_minutes', sortable: true, align: 'end' },
          { title: 'Overall Score', key: 'overall', sortable: true, align: 'end' },
          { title: 'Filler Words %', key: 'fillerWords', sortable: true, align: 'end' }
        ]

        // Add a header for each category
        categoryList.value.forEach(category => {
          if (!updatedHeaders.some(header => header.key === category)) {
            updatedHeaders.push({
              title: category,
              key: category,
              sortable: true,
              align: 'end'
            })
          }
        })

        // Add the actions column last
        updatedHeaders.push({
          title: 'Actions',
          key: 'actions',
          sortable: false,
          align: 'end'
        })

        interviewsHeaders.value = updatedHeaders
      }
    } else {
      interviewData.value = []
      interviews.value = []
      console.log('No interviews found')
    }
  } catch (error) {
    console.error('Error fetching interviews:', error)
    interviewsError.value = 'Failed to load interview data. Please try again.'
  } finally {
    loadingInterviews.value = false
  }
}

// Helper function to get color based on score
const getScoreColor = (score) => {
  if (score >= 8) return 'success'  // Green for excellent scores
  if (score >= 6) return 'info'      // Blue for good scores
  if (score >= 4) return 'warning'   // Yellow/orange for average scores
  return 'error'                     // Red for low scores
}

// Helper function to get color based on filler word percentage
const getFillerWordColor = (percentage) => {
  if (percentage <= 2) return 'success'  // Green for excellent (low filler words)
  if (percentage <= 5) return 'info'      // Blue for good
  if (percentage <= 10) return 'warning'  // Yellow/orange for average
  return 'error'                          // Red for high filler words
}

// Fetch all user minutes records with pagination
async function fetchAllUserMinutes() {
  loadingAllMinutes.value = true
  allMinutesError.value = null

  try {
    console.log('Fetching all user minutes records')

    // Get all user minutes records from the database
    const { data: minutesData, error: minutesError } = await supabase
      .from('user_minutes')
      .select('*')
      .order('created_at', { ascending: false })

    if (minutesError) throw minutesError

    console.log('Raw user minutes data received:', minutesData)

    // Get unique user IDs from the minutes records
    const userIds = [...new Set(minutesData.map(m => m.user_id).filter(Boolean))]

    // If there are user IDs, fetch the corresponding user profiles
    let userProfiles = {}
    if (userIds.length > 0) {
      const { data: userData, error: userError } = await supabase
        .from('user_profiles')
        .select('user_id, first_name, last_name, email')
        .in('user_id', userIds)

      if (userError) throw userError

      // Create a lookup map of user profiles by user_id
      userProfiles = userData.reduce((acc, user) => {
        acc[user.user_id] = user
        return acc
      }, {})
    }

    // Combine minutes data with user profile data
    allMinutes.value = minutesData.map(record => {
      const userProfile = userProfiles[record.user_id] || {}
      return {
        ...record,
        user_first_name: userProfile.first_name || '',
        user_last_name: userProfile.last_name || '',
        user_email: userProfile.email || ''
      }
    })
  } catch (err) {
    console.error('Error fetching user minutes:', err)
    allMinutesError.value = 'Failed to load user minutes. Please try again.'
  } finally {
    loadingAllMinutes.value = false
  }
}

// Open interview in a new tab
function openInterview(interviewId) {
  if (interviewId) {
    window.open(`/interviews/${interviewId}`, '_blank')
  }
}

// Open create coupon dialog
function openCreateCouponDialog() {
  createCouponDialog.value = true
}

// Handle coupon creation success
function onCouponCreated() {
  // Close the dialog after successful creation
  createCouponDialog.value = false
  // The CouponCodesTable component should automatically refresh its data
}

// Custom filter function for users table
function customUserFilter(value, search, item) {
  if (!search) return true

  const searchText = search.toString().toLowerCase()

  // Access raw data in Vuetify 3
  const rawItem = item.raw || item

  // Check email
  if (rawItem.email && rawItem.email.toLowerCase().includes(searchText)) {
    return true
  }

  // Check first name
  if (rawItem.first_name && rawItem.first_name.toLowerCase().includes(searchText)) {
    return true
  }

  // Check last name
  if (rawItem.last_name && rawItem.last_name.toLowerCase().includes(searchText)) {
    return true
  }

    // Check full name
  const fullName = `${rawItem.first_name || ''} ${rawItem.last_name || ''}`.trim().toLowerCase()
  if (fullName && fullName.includes(searchText)) {
    return true
  }

  // Check phone number (digits only and formatted)
  if (rawItem.mobile_phone) {
    const digits = rawItem.mobile_phone.replace(/\D/g, '')
    const searchDigits = searchText.replace(/\D/g, '')
    if (digits.includes(searchDigits) && searchDigits) {
      return true
    }
    if (rawItem.mobile_phone.toLowerCase().includes(searchText)) {
      return true
    }
  }

  // Default case: check if the displayed value contains the search text
  return value != null && value.toString().toLowerCase().includes(searchText)
}

// Custom filter function for transactions
function customTransactionFilter(value, search, item) {
  if (!search) return true

  const searchText = search.toString().toLowerCase()

  // Access raw data in Vuetify 3
  const rawItem = item.raw || item

  // Check user email
  if (rawItem.user_email && rawItem.user_email.toLowerCase().includes(searchText)) {
    return true
  }

  // Check user first name
  if (rawItem.user_first_name && rawItem.user_first_name.toLowerCase().includes(searchText)) {
    return true
  }

  // Check user last name
  if (rawItem.user_last_name && rawItem.user_last_name.toLowerCase().includes(searchText)) {
    return true
  }

  // Check full name
  const fullName = `${rawItem.user_first_name || ''} ${rawItem.user_last_name || ''}`.trim().toLowerCase()
  if (fullName && fullName.includes(searchText)) {
    return true
  }

  // Check product name
  if (rawItem.product_name && rawItem.product_name.toLowerCase().includes(searchText)) {
    return true
  }

  // Check referral code
  if (rawItem.referral_code && rawItem.referral_code.toLowerCase().includes(searchText)) {
    return true
  }

  // Default case: check if the displayed value contains the search text
  return value != null && value.toString().toLowerCase().includes(searchText)
}

// Helper function to get user initials from profile
function getUserInitialsFromProfile(profile) {
  if (!profile) return '?'

  // Try to get initials from first and last name
  const firstName = profile.first_name?.trim()
  const lastName = profile.last_name?.trim()

  if (firstName && lastName) {
    return `${firstName[0]}${lastName[0]}`.toUpperCase()
  }

  if (firstName) {
    return firstName[0].toUpperCase()
  }

  // Fallback to email first letter
  if (profile.email) {
    return profile.email[0].toUpperCase()
  }

  return '?'
}
</script>

<route>
{
  meta: {
    requiresAuth: true,
    requiresAdmin: true
  }
}
</route>

<style scoped>
.tabs-with-border {
  border-bottom: 1.5px solid #e0e0e0; /* Adjust color/thickness as needed */
}

/* Tab styles from interview page */
.v-tabs {
  border-radius: 8px;
  background-color: var(--v-theme-surface-variant) !important;
}

.v-tab {
  font-weight: 500;
  letter-spacing: 0.5px;
  min-width: 120px;
}

.v-tab--selected {
  background-color: rgba(var(--v-theme-primary), 0.1);
}

.v-window {
  border-radius: 8px;
  overflow: hidden;
}

/* Ensure score chips have good contrast in all themes */
.score-chip {
  font-weight: 700 !important;
  color: white !important;
  text-shadow: 0px 0px 1px rgba(0, 0, 0, 0.3);
}

:deep(.v-theme--light) .score-chip {
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.1);
}

:deep(.v-theme--dark) .score-chip {
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

/* Style score chips by color */
:deep(.score-chip.bg-success) {
  background-color: #43A047 !important;
}

:deep(.score-chip.bg-info) {
  background-color: #1E88E5 !important;
}

:deep(.score-chip.bg-warning) {
  background-color: #FB8C00 !important;
}

:deep(.score-chip.bg-error) {
  background-color: #E53935 !important;
}

/* Transaction stat cards styling */
.transaction-stat-card {
  transition: transform 0.2s, box-shadow 0.2s;
  border-radius: 12px !important;
}

.transaction-stat-card:hover {
  transform: translateY(-3px);
}

:deep(.v-theme--light) .transaction-stat-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

:deep(.v-theme--dark) .transaction-stat-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

.transaction-stat-card .text-caption {
  font-weight: 500;
  opacity: 0.85;
}

.transaction-stat-card .text-h6 {
  margin-top: 8px;
  font-size: 1.5rem !important;
}

/* Responsive adjustments for tabs */
@media (max-width: 600px) {
  .v-tab {
    min-width: unset;
  }
}

/* Mobile dropdown styling */
@media (max-width: 959px) {
  .mobile-section-select {
    margin-bottom: 16px;
  }

  .mobile-section-select .v-field__prepend-inner {
    padding-top: 8px;
  }

  /* Ensure proper spacing for mobile content */
  .v-window-item {
    padding: 16px !important;
  }

  /* Adjust card padding on mobile */
  .v-card-text {
    padding: 12px !important;
  }

  /* Make data tables more mobile friendly */
  .v-data-table {
    font-size: 0.875rem;
  }
}
</style>
