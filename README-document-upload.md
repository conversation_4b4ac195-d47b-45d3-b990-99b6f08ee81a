# Document Upload and Text Extraction

This document explains how to set up document text extraction in StageCoachAI. The system supports extracting text from various document formats so users can easily upload resume/CV content.

## Supported File Types

- **Text (.txt)**: Processed directly in the browser
- **PDF (.pdf)**: Processed in the browser using PDF.js
- **Word Documents (.docx)**: Processed using a Supabase Edge Function
- **Legacy Word Documents (.doc)**: Not supported (users must convert to .docx)

## Setup Requirements

1. Supabase project with storage and edge functions enabled
2. Storage bucket named "documents" 
3. Edge function for document processing
4. Proper Row Level Security (RLS) policies for the storage bucket

## Setting Up Supabase Storage

1. Go to your Supabase project dashboard
2. Navigate to Storage → Buckets
3. Click "Create Bucket"
4. Name it "documents" and select "Private" for the privacy setting
5. Configure the following RLS policies:

### RLS Policy for Document Upload

```sql
CREATE POLICY "Users can upload documents" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'documents' 
  AND (storage.foldername(name))[1] = 'temp' 
  AND (storage.foldername(name))[2] = auth.uid()::text
);
```

### RLS Policy for Document Access

```sql
CREATE POLICY "Users can access their own documents" 
ON storage.objects 
FOR SELECT 
TO authenticated 
USING (
  bucket_id = 'documents' 
  AND (storage.foldername(name))[1] = 'temp' 
  AND (storage.foldername(name))[2] = auth.uid()::text
);
```

### RLS Policy for Document Deletion

```sql
CREATE POLICY "Users can delete their own documents" 
ON storage.objects 
FOR DELETE 
TO authenticated 
USING (
  bucket_id = 'documents' 
  AND (storage.foldername(name))[1] = 'temp' 
  AND (storage.foldername(name))[2] = auth.uid()::text
);
```

## Setting Up the Edge Function

The `extract-document-text` Edge Function processes document files and extracts text. To deploy it:

1. Make sure you have the Supabase CLI installed:
   ```bash
   npm install -g supabase
   ```

2. Login to Supabase:
   ```bash
   supabase login
   ```

3. Link your local project to your Supabase project:
   ```bash
   supabase link --project-ref your-project-ref
   ```

4. Deploy the edge function:
   ```bash
   supabase functions deploy extract-document-text
   ```

### Edge Function Requirements

The edge function (`supabase/functions/extract-document-text/index.ts`) uses:

- Supabase JS client for authentication and storage operations
- Mammoth.js for DOCX parsing

### Testing the Edge Function

After deployment, you can test by calling it from the UI, or directly:

```bash
curl -X POST \
  https://<your-project-ref>.supabase.co/functions/v1/extract-document-text \
  -H "Authorization: Bearer <access-token>" \
  -H "Content-Type: application/json" \
  -d '{"bucketName":"documents","filePath":"path/to/file.docx"}'
```

## Implementation Details

The document upload and text extraction is implemented in the account profile page:

1. User selects a document file on the resume editor
2. Application checks file type:
   - For `.txt`: Processes directly in the browser
   - For `.pdf`: Uses PDF.js to extract text
   - For `.docx`: Uploads to Supabase storage and invokes the edge function
   - For `.doc`: Shows error message asking for conversion to .docx

3. The edge function:
   - Downloads file from storage
   - Extracts text using appropriate method for the file type
   - Returns text content
   - The temporary file is deleted after processing

## Debugging Issues

### Common Problems

- **"Permission denied"**: Check storage bucket RLS policies
- **"Bucket not found"**: Ensure 'documents' bucket exists
- **"Edge Function not found"**: Ensure function is deployed
- **"Legacy .doc files not supported"**: User needs to convert file to .docx format

### Client-Side Debugging

The front-end code contains detailed console logging to help debug issues:
- Check browser console for detailed logs
- Error messages are shown in UI snackbars

### Server-Side Debugging

To view Edge Function logs:
```bash
supabase functions logs extract-document-text --project-ref your-project-ref
``` 