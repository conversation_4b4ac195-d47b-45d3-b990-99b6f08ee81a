# Supabase Project Export PRD

## Overview
This project aims to create a comprehensive export package for a Supabase project that can be easily imported into a new Supabase instance. The current project is accessed remotely, and we need to extract all necessary components to allow for easy replication.

## Goals
- Create a structured export of all database schema components (tables, functions, triggers, RLS policies)
- Export storage bucket configurations and policies
- Save all edge function code locally
- Provide clear documentation and instructions for reimporting

## Requirements

### 1. Database Schema Export
- Create SQL scripts for all tables, including constraints and indexes
- Export all database functions with proper security settings
- Export all Row Level Security (RLS) policies
- Export all triggers and their associated functions
- Organize SQL scripts in a logical folder structure

### 2. Storage Configuration
- Export bucket definitions (name, access settings, size limits)
- Export RLS policies for storage buckets
- Document folder structures used in storage

### 3. Edge Functions
- Save all edge function code locally
- Document required environment variables
- Ensure dependencies are correctly specified using JSR or NPM format
- Create instructions for redeploying functions

### 4. Documentation
- Create a detailed README.md with step-by-step instructions
- Document all required environment variables
- Provide Supabase CLI commands for importing
- Include verification steps

### 5. Implementation Plan
- Create folder structure for export package
- Write scripts to automate extraction where possible
- Test import process on a fresh Supabase instance
- Create instructions for using Supabase MCP tools for setup

## Technical Constraints
- The extraction should be done without disrupting the existing remote Supabase project
- Scripts should be idempotent where possible
- SQL scripts should follow Supabase migration format
- Edge functions should maintain the same folder structure

## Deliverables
1. Export package with organized SQL scripts
2. Complete set of edge function code
3. Storage bucket configuration scripts
4. Comprehensive README.md with setup instructions
5. Implementation scripts for automation 