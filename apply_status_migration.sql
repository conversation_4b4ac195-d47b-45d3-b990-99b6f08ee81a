-- Add status column to conversations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'conversations' 
        AND column_name = 'status'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.conversations 
        ADD COLUMN status text NOT NULL DEFAULT 'open';
        
        -- Add check constraint
        ALTER TABLE public.conversations 
        ADD CONSTRAINT conversations_status_check 
        CHECK (status IN ('open', 'closed'));
        
        -- <PERSON><PERSON> indexes
        CREATE INDEX idx_conversations_status ON public.conversations (status);
        CREATE INDEX idx_conversations_type_status ON public.conversations (type, status);
    END IF;
END $$;

-- Create function to update conversation status (admin/staff only)
CREATE OR REPLACE FUNCTION public.update_conversation_status(
  conversation_id uuid,
  new_status text
)
RETURNS json
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
DECLARE
  current_user_role text;
  conversation_record record;
  result json;
BEGIN
  -- Check if user is authenticated
  IF auth.uid() IS NULL THEN
    RAISE EXCEPTION 'authentication required';
  END IF;

  -- Get current user role
  SELECT role INTO current_user_role
  FROM public.user_profiles
  WHERE user_id = auth.uid();

  -- Only admin and staff can update conversation status
  IF current_user_role NOT IN ('admin', 'staff') THEN
    RAISE EXCEPTION 'insufficient permissions - only admin and staff can update conversation status';
  END IF;

  -- Validate status value
  IF new_status NOT IN ('open', 'closed') THEN
    RAISE EXCEPTION 'invalid status value - must be open or closed';
  END IF;

  -- Check if conversation exists and user has access to it
  SELECT c.* INTO conversation_record
  FROM public.conversations c
  INNER JOIN public.conversation_participants cp 
    ON c.id = cp.conversation_id
  WHERE c.id = update_conversation_status.conversation_id
    AND cp.user_id = auth.uid();

  IF NOT FOUND THEN
    RAISE EXCEPTION 'conversation not found or access denied';
  END IF;

  -- Update the conversation status
  UPDATE public.conversations
  SET 
    status = new_status,
    updated_at = NOW()
  WHERE id = update_conversation_status.conversation_id;

  -- Prepare result
  result := json_build_object(
    'success', true,
    'conversation_id', conversation_id,
    'old_status', conversation_record.status,
    'new_status', new_status,
    'updated_at', NOW()
  );

  RETURN result;
END;
$$; 